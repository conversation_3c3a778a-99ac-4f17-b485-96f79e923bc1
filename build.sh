#!/bin/bash

needdtb=1
cpuname=$1
logfile=buildpmon.log

function myexit() {
  kill -- -$pgid 2>/dev/null
}
trap myexit EXIT SIGINT

# Detect the host processor, and determine the GCC version.
case $('uname') in
  Linux*)
    case $(uname -m) in
      x86_64)
	if command -v loongarch64-linux-gnu-gcc >/dev/null 2>&1; then
	    :
	elif [ -d "/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.6/" ]; then
	    export PATH=/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.6/bin:$PATH
        else  
            echo -e "\033[0;31mNo toolchain, Need GCC 8.3 GNU/Linux rc1.6 version\033[0m" 
            echo "You can obtain the toolchain from http://www.loongnix.cn/zh/toolchain/GNU/" 
            echo "Decompress cmd: tar xvf loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.6.tar.xz  -C /opt/"
            exit 1
        fi
         ;;
      loongarch64)
        GCC_VERSION=$(gcc -v 2>&1 | tail -1 | awk '{print $3}')
        case $GCC_VERSION in
          8.3.*)
            echo Use compiler version: $GCC_VERSION
            ;;
          *)
            echo Only support the version of compiler is GCC8.3 for now.
            exit 1
            ;;
        esac
        ;;
    esac
    ;;
  *)
    echo Only support Linux.
    exit 1
    ;;
esac

case $cpuname in
    ls2k300 | ls2k500 | ls2k1000)
		cd zloader.soc
		make  $cpuname
		;;
	ls2k1500)
		cp Targets/ls2k/conf/ls_pai.2k1500 Targets/ls2k/conf/ls.2k
		cp Targets/ls2k/conf/ls2k1500.dts Targets/ls2k/conf/loongson.dts
		cd zloader.ls2k
		;;
	ls2k2000)
		cp Targets/ls2k/conf/ls_pai.2k2000 Targets/ls2k/conf/ls.2k
		cp Targets/ls2k/conf/ls2k2000.dts Targets/ls2k/conf/loongson.dts
		cd zloader.ls2k
		;;
	ls3a5000 | ls3a6000 | ls3c5000 | ls3c6000 | ls3d6000 | ls3e6000 | ls3b6000)
        cp Targets/ls3a5000_7a/conf/$cpuname  Targets/ls3a5000_7a/conf/ls.3a5000_7a
		cd zloader.3a5000_7a
        needdtb=0
		;;
    clean | clear)
        make clean > /dev/null 2>&1
        git checkout Targets/ls2k/conf/ls.2k > /dev/null 2>&1
        git checkout Targets/ls2k/conf/loongson.dts > /dev/null 2>&1
        git checkout Targets/ls3a5000_7a/conf/ls.3a5000_7a > /dev/null 2>&1
        echo -e "\033[0;32mMake Clean Complete.\033[0m"
        exit 1
		;;
    help)
        echo "Compile dependency packages:"
        echo "sudo apt-get install xutils-dev make iasl gawk bison flex build-essential patch"
        exit 1
        ;;
	*)
        echo -e "\033[0;31mError: Please specify CPU type\033[0m"
        echo "Valid options: ls2k300 ls2k500 ls2k1000 ls2k1500 ls2k2000"
        echo "               ls3a5000 ls3a6000 ls3c5000 ls3c6000 ls3d6000 ls3e6000 ls3b6000"
        echo -e "\033[0;32mExample1: $0 ls2k300 \033[0m"
        echo -e "\033[0;32mExample2: $0 clean \033[0m"
        exit 1
        ;;
esac

rm -rf $logfile
echo "Build cpu is $cpuname" | tee $logfile
set -m
tail -f  $logfile | while read -r line; do
  if ! echo "$line" | grep -q "loongarch64-linux-gnu-gcc "; then
    continue
  fi
  files=$(echo "$line" | grep -oP '\S+\.(c|S)')
  if [ -z "$files" ]; then
    continue
  fi
  filename=$(basename "$files")
  echo CC "$filename"
done &
pgid=$(jobs -p)

make cfg >> $logfile 2>&1
make all tgt=rom CROSS_COMPILE=loongarch64-linux-gnu- DEBUG=-g >> $logfile 2>&1

if [ -e "gzrom.bin" ]; then  
  if [ "$needdtb" -ne 0 ]; then  
      make dtb >> $logfile 2>&1
      if [ -e "gzrom-dtb.bin" ]; then
        mv gzrom-dtb.bin  ../$cpuname-dtb.bin
      else
        echo -e "\033[0;31mCompilation of $cpuname failed, Make dtb error!\033[0m"
      fi
  fi
  mv gzrom.bin  ../$cpuname.bin
  echo -e "\033[0;32mSuccessfully compiled $cpuname\033[0m"
else
  echo -e "\033[0;31mCompilation of $cpuname failed!\033[0m"
fi

grep -i " error:" $logfile
echo -e "\033[0;32mCompile logs to view zloader/$logfile\033[0m"
