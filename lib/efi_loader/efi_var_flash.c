// SPDX-License-Identifier: GPL-2.0+
/*
 * Flash interface for UEFI variables
 *
 * Copyright (c) 2020, <PERSON>
 */

#define LOG_CATEGORY LOGC_EFI

#include <pflash.h>
#include <charset.h>
#include <sys/types.h>
#include <include/bonito.h>
#include <efi_loader.h>
#include <efi_variable.h>
#include <dev/pflash_tgt.h>
#include <linux/kernel.h>
#include <sys/malloc.h>

/* GUID used by <PERSON><PERSON> to store the MOK database */
#define SHIM_LOCK_GUID \
	EFI_GUID(0x605dab50, 0xe046, 0x4300, \
		 0xab, 0xb6, 0x3d, 0xd8, 0x10, 0xdd, 0x8b, 0x23)

static const efi_guid_t shim_lock_guid = SHIM_LOCK_GUID;
extern struct fl_map *tgt_flashmap();
char variable_buffer[EFI_VAR_BUF_SIZE];

efi_status_t efi_var_collect(struct efi_var_store **bufp, off_t *lenp,
					    u32 check_attr_mask)
{
	size_t len = EFI_VAR_BUF_SIZE;
	struct efi_var_entry *var, *old_var;
	struct efi_var_store *buf = variable_buffer;
	size_t old_var_name_length = 2;

	memset(buf, 0, EFI_VAR_BUF_SIZE);
	var = buf->var;
	old_var = var;
	for (;;) {
		efi_uintn_t data_length, var_name_length;
		u8 *data;
		efi_status_t ret;

		if ((uintptr_t)buf + len <=
		    (uintptr_t)var->name + old_var_name_length)
			return EFI_BUFFER_TOO_SMALL;

		var_name_length = (uintptr_t)buf + len - (uintptr_t)var->name;
		memcpy(var->name, old_var->name, old_var_name_length);
		guidcpy(&var->guid, &old_var->guid);
		ret = efi_get_next_variable_name_int(
				&var_name_length, var->name, &var->guid);
		if (ret == EFI_NOT_FOUND)
			break;
		if (ret != EFI_SUCCESS) {
			free(buf);
			return ret;
		}
		old_var_name_length = var_name_length;
		old_var = var;

		data = (u8 *)var->name + old_var_name_length;
		data_length = (uintptr_t)buf + len - (uintptr_t)data;
		ret = efi_get_variable_int(var->name, &var->guid,
					   &var->attr, &data_length, data,
					   &var->time);
		if (ret != EFI_SUCCESS) {
			free(buf);
			return ret;
		}
		if ((var->attr & check_attr_mask) == check_attr_mask) {
			var->length = data_length;
			var = (struct efi_var_entry *)ALIGN((uintptr_t)data + data_length, 8);
		}
	}

	buf->reserved = 0;
	buf->magic = efi_var_store_MAGIC;
	len = (uintptr_t)var - (uintptr_t)buf;
	buf->crc32 = crc32(0, (u8 *)buf->var,
			   len - sizeof(struct efi_var_store));
	buf->length = len;
	*bufp = buf;
	*lenp = len;

	return EFI_SUCCESS;
}

/**
 * efi_var_to_flash() - save non-volatile variables to flash
 *
 * Return:	status code
 */
efi_status_t efi_var_to_flash(void)
{
	efi_status_t ret;
	struct efi_var_store *buf;
	char *var;
	off_t len;
	off_t actlen;
	int r;

	ret = efi_var_collect(&buf, &len, EFI_VARIABLE_NON_VOLATILE);
	if (ret != EFI_SUCCESS)
		goto error;

	var = (char *)(tgt_flashmap()->fl_map_base) + VAR_OFFS;

	efi_flash_program((void *)var, len, (void *)buf, 0);
error:
	return ret;
}

efi_status_t efi_var_restore(struct efi_var_store *buf, bool safe)
{
	u16 *data;
	char *fp;
	efi_status_t ret;
	struct efi_var_entry *var, *last_var;
	char var_head_buf[] = {
		  // This is the VARIABLE_STORE_HEADER
		  // gEfiVariableGuid = { 0xddcf3616, 0x3275, 0x4164, {0x98, 0xb6, 0xfe, 0x85, 0x70, 0x7f, 0xfe, 0x7d}}
		    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
			0x98, 0xb6, 0xfe, 0x85, 0x70, 0x7f, 0xfe, 0x7d,
		  // Size: 0x10000 (gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageVariableSize) -
		  //         0x48 (size of EFI_FIRMWARE_VOLUME_HEADER) = 0xffb8
		  // This can speed up the Variable Dispatch a bit.
		    0x18, 0x00, 0x00, 0x00,
		  // FORMATTED: 0x5A #HEALTHY: 0xFE #Reserved: UINT16 #Reserved1: UINT32
			0x00, 0x00, 0x00, 0x00
	};

	if (buf->reserved || buf->magic != efi_var_store_MAGIC) {
		fp = (char *)(tgt_flashmap()->fl_map_base) + VAR_OFFS;
		efi_flash_program((void *)fp, 100, (void *)var_head_buf, 0);
		memcpy(buf, fp, EFI_VAR_BUF_SIZE);
	}

	last_var = (struct efi_var_entry *)((u8 *)buf + buf->length);
	for (var = buf->var; var < last_var; var = (struct efi_var_entry *)
		   ALIGN((uintptr_t)data + var->length, 8)) {

		data = var->name + u16_strlen(var->name) + 1;
		/*
		 * Secure boot related and non-volatile variables shall only be
		 * restored from pmon's preseed.
		 */
		if (!safe &&
		    (efi_auth_var_get_type(var->name, &var->guid) !=
		     EFI_AUTH_VAR_NONE ||
		     !guidcmp(&var->guid, &shim_lock_guid) ||
		     !(var->attr & EFI_VARIABLE_NON_VOLATILE)))
			continue;
		if (!var->length)
			continue;
		ret = efi_var_mem_ins(var->name, &var->guid, var->attr,
				      var->length, data, 0, NULL,
				      var->time);
		if (ret != EFI_SUCCESS)
			log_err("Failed to set EFI variable %ls\n", var->name);
	}
	return EFI_SUCCESS;
}

/**
 * efi_var_from_flash() - read variables from flash
 *
 * In case the flash does not exist yet or a variable cannot be set EFI_SUCCESS
 * is returned.
 *
 * Return:	status code
 */
efi_status_t efi_var_from_flash(void)
{
	struct efi_var_store *buf;
	char *var;
	off_t len;
	efi_status_t ret;
	int r;

	var = (char *)(tgt_flashmap()->fl_map_base) + VAR_OFFS;

	buf = calloc(1, EFI_VAR_BUF_SIZE);
	if (!buf) {
		log_err("Out of memory\n");
		return EFI_OUT_OF_RESOURCES;
	}

	memcpy(buf, var, EFI_VAR_BUF_SIZE);
	if (efi_var_restore(buf, false) != EFI_SUCCESS)
		log_err("Invalid EFI variables store\n");
	
    free(buf);
	return EFI_SUCCESS;
}
