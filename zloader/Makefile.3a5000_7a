TARGET := ls3a5000_7a
TARGETEL := loongson
START := start.o
MEMSIZE := 128
ifndef S
S := $(shell cd ../; pwd)
endif

ASL_DIR		:=../pmon/arch/loongarch/acpi/acpi_tables/dsdt
export TARGET_LIB=mem_config.a ls7a.a
export TARGET_CFG=make -C ${S}/pmon/arch/loongarch/common/ddr4/ -e TARGETIN=ls3a5000_7a TARGETELIN=loongson clean;\
                  make -C ${S}/pmon/arch/loongarch/ls7a/api -e TARGETIN=ls3a5000_7a clean;\
                  ${S}/zloader/bin2c ${S}/Targets/${TARGET}/${TARGETEL}/dvfs.bin \
                  ${S}/Targets/${TARGET}/include/dvfs.h dvfs_data
export TARGET_LD_HEAD=make -C ${S}/pmon/arch/loongarch/common/ddr4/ -e TARGETIN=ls3a5000_7a TARGETELIN=loongson; \
                      make -C ${S}/pmon/arch/loongarch/ls7a/api -e TARGETIN=ls3a5000_7a
ifneq ($(wildcard ${S}/Targets/${TARGET}/compile/${TARGETEL}/Makefile),)
export IDENT_DEF=$(shell cat ${S}/Targets/${TARGET}/compile/${TARGETEL}/Makefile | grep "IDENT" | sed 's/IDENT=//g')
export DTS_CORE_FREQ=$(shell cat ${S}/Targets/${TARGET}/compile/${TARGETEL}/Makefile | grep "IDENT" | sed 's/.*CORE_FREQ=//' | sed 's/ .*//' | xargs -I N echo N | awk '{printf "%d\n", strtonum($$1) * 1000000}')
endif
include Makefile.inc

acpi: ${S}/include/dsdt.h

${S}/include/dsdt.h:	$(ASL_DIR)/dsdt.asl
	$(CPP) -x assembler-with-cpp -P $(IDENT_DEF) ${INCLUDES} -o $<.tmp $<;
#	sed -i '7i include ("dual_7a.asl")' $<.tmp	#dual 7a
	iasl -p $< -tc -va $<.tmp;
	mv $(patsubst %.asl,%.hex,$<) $@
	sed -i -e "s,unsigned char dsdt_aml_code,const unsigned char AmlCode," $@

help:
	@echo ""
	@echo "********************************************************************************************************************"
	@echo use make cfg     for config
	@echo use make tgt=rom for generate romfile gzrom.bin for desktop
	@echo use make cleanall for clean all code
	@echo use make DEBUG=-g MYCC="'"-g3 -DMYDBG='"printf(\"debug:%s,%d\\n\",__FILE__,__LINE__);"'"'" to support MYDBG macro.
	@echo "********************************************************************************************************************"
	@echo ""
