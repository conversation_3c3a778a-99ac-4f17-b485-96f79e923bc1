import re
import os
import subprocess
import sys
import glob

def elf_to_syms(objfile):
 if not os.path.exists(objfile):
  return ()
 txt=os.popen("LC_ALL=C readelf -W -s " + objfile + " |grep FUNC").read().strip()
 syms=re.compile("[0-9]+:\s+([0-9a-f]+)\s+([0-9]+)\s+FUNC\s+\S+\s+\S+\s+[0-9]+\s+(\S+)").findall(txt)
 return syms

f=open(sys.argv[2],"w")
f.write("""
typedef struct elf_sym {
  const char *st_name;		/* Symbol name, index in string tbl */
 unsigned long *st_value;		/* Value of the symbol */
  long st_size;		/* Associated symbol size */
} elf_sym;
const elf_sym symtabs[] = {
""");
syms = elf_to_syms(sys.argv[1])
syms.sort(key=lambda x : int(x[0],16))
for i in syms:
    f.write("{{\"{2}\", 0x{0}, {1}}},\n".format(i[0],i[1],i[2]))
f.write("""{0,},
};
elf_sym *symtab = symtabs;
""");
