#ifndef __LINUX_TYPES_H_
#define __LINUX_TYPES_H_

typedef unsigned char       u8;
typedef unsigned short      u16;
typedef unsigned int        u32;
typedef unsigned long long  u64;
typedef unsigned int        uint;
typedef signed char         s8;
typedef signed short        s16;
typedef signed int          s32;
typedef signed long long    s64;
typedef signed int          sint;
typedef unsigned int        bool;

typedef char                INT8;
typedef short               INT16;
typedef int                 INT32;
typedef unsigned char       UINT8;
typedef unsigned short      UINT16;
typedef unsigned int        UINT32;

typedef unsigned long       device_t;

#endif /* __LINUX_TYPES_H_ */
