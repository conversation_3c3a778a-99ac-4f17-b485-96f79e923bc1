#include "./core.h"


unsigned int mci_debug = 0;
int in_detect = 0;
int mci_init_flag = 0;
static const unsigned freqs[] = { 400000, 300000, 200000, 100000 };

static struct mmc_command stop_cmd = {
		.opcode = MMC_STOP_TRANSMISSION,
		.flags = MMC_RSP_SPI_R1 | MMC_RSP_R1 | MMC_CMD_AC,
		/* Some hosts wait for busy anyway, so provide a busy timeout */
};



void mmc_request_done(struct mmc_host *host, struct mmc_request *mrq)
{
	struct mmc_command *cmd = mrq->cmd;

	int err = cmd->error;
}



static int mmc_mrq_prep(struct mmc_host *host, struct mmc_request *mrq)
{
	unsigned int i, sz = 0;

	if (mrq->cmd) {
		mrq->cmd->error = 0;
		mrq->cmd->mrq = mrq;
		mrq->cmd->data = mrq->data;
	}
	if (mrq->data) {
		/* check blksz and blocks */
		if (mrq->data->blksz > host->max_blk_size ||
		    mrq->data->blocks > host->max_blk_count ||
		    mrq->data->blocks * mrq->data->blksz > host->max_req_size)
			return -EINVAL;

		mrq->data->error = 0;
		mrq->data->mrq = mrq;
		if (mrq->stop) {
			mrq->data->stop = mrq->stop;
			mrq->stop->error = 0;
			mrq->stop->mrq = mrq;
		}
	}

	return 0;
}


static int __mmc_start_req(struct mmc_host *host, struct mmc_request *mrq)
{
	int err;
        /* prepare mrq */
	err = mmc_mrq_prep(host, mrq);
	if (err)
	{
                mci_err("MCI Err: __mmc_start_req can not request: %d\n", err);
		mrq->cmd->error = err;
		return err;
	}
	host->ops->request(host, mrq);

	return 0;
}


void mmc_wait_for_req_done(struct mmc_host *host, struct mmc_request *mrq)
{
	struct mmc_command *cmd;

	while (1);

}

void mmc_wait_for_req(struct mmc_host *host, struct mmc_request *mrq)
{
	struct mmc_command *cmd;
       
	__mmc_start_req(host, mrq);

	/* wait irq and handle irq event */
	host->ops->wait_handle_irq(host);

	cmd = mrq->cmd;

	if (cmd->error) {
		mci_err("MCI Err: mmc wait for req cmd err, cmd %d\n", cmd->opcode);
	}

	if (cmd->data && cmd->data->error) {
		mci_err("MCI Err: mmc wait for req data err, cmd %d\n", cmd->opcode);
	}
}


int mmc_wait_for_cmd(struct mmc_host *host, struct mmc_command *cmd, int retries)
{
	struct mmc_request mrq = {};
        mci_dbg("MCI: mmc wait for cmd: %d\n", cmd->opcode);
        /* clear cmd resp */
	memset(cmd->resp, 0, sizeof(cmd->resp));
	cmd->retries = retries;

	mrq.cmd = cmd;
	cmd->data = NULL;

	mmc_wait_for_req(host, &mrq);

	return cmd->error;
}



struct mci_host_manager mci_hosts_m = {};
struct mci_card_manager mci_cards_m = {};

int mmc_of_parse(struct mmc_host *host, struct host_conf *p_conf)
{
	u32 bus_width, drv_type, cd_debounce_delay_ms;
	int ret;
	bool cd_cap_invert, cd_gpio_invert = false;
	bool ro_cap_invert, ro_gpio_invert = false;


	bus_width = p_conf->bus_width;
	if (bus_width < 0) {
		mci_err("MCI Err: host %d bus-width  property is missing, assuming 1 bit\n", host->index);
		bus_width = 1;
	}

	switch (bus_width) {
	case 8:
		host->caps |= MMC_CAP_8_BIT_DATA;
		/* Hosts capable of 8-bit transfers can also do 4 bits */
	case 4:
		host->caps |= MMC_CAP_4_BIT_DATA;
		break;
	case 1:
		break;
	default:
		mci_err("MCI Err: Invalid bus-width value %u!\n", bus_width);
		return -EINVAL;
	}

        if (p_conf->max_frequency > 0)
		host->f_max = p_conf->max_frequency;  
	
	if (p_conf->cap_sd_highspeed)
		host->caps |= MMC_CAP_SD_HIGHSPEED;
	if (p_conf->cap_mmc_highspeed)
		host->caps |= MMC_CAP_MMC_HIGHSPEED;
	if (p_conf->sd_uhs_sdr12)
		host->caps |= MMC_CAP_UHS_SDR12;
	if (p_conf->sd_uhs_sdr25)
		host->caps |= MMC_CAP_UHS_SDR25;
	if (p_conf->sd_uhs_sdr50)
		host->caps |= MMC_CAP_UHS_SDR50;
	if (p_conf->sd_uhs_sdr104)
		host->caps |= MMC_CAP_UHS_SDR104;
	if (p_conf->sd_uhs_ddr50)
		host->caps |= MMC_CAP_UHS_DDR50;
	if (p_conf->cap_power_off_card)
		host->caps |= MMC_CAP_POWER_OFF_CARD;
	if (p_conf->cap_mmc_hw_reset)
		host->caps |= MMC_CAP_HW_RESET;
	if (p_conf->cap_sdio_irq)
		host->caps |= MMC_CAP_SDIO_IRQ;
	if (p_conf->full_pwr_cycle)
		host->caps2 |= MMC_CAP2_FULL_PWR_CYCLE;
	if (p_conf->mmc_ddr_3_3v)
		host->caps |= MMC_CAP_3_3V_DDR;
	if (p_conf->mmc_ddr_1_8v)
		host->caps |= MMC_CAP_1_8V_DDR;
	if (p_conf->mmc_ddr_1_2v)
		host->caps |= MMC_CAP_1_2V_DDR;
	if (p_conf->mmc_hs200_1_8v)
		host->caps2 |= MMC_CAP2_HS200_1_8V_SDR;
	if (p_conf->mmc_hs200_1_2v)
		host->caps2 |= MMC_CAP2_HS200_1_2V_SDR;
	if (p_conf->mmc_hs400_1_8v)
		host->caps2 |= MMC_CAP2_HS400_1_8V | MMC_CAP2_HS200_1_8V_SDR;
	if (p_conf->mmc_hs400_1_2v)
		host->caps2 |= MMC_CAP2_HS400_1_2V | MMC_CAP2_HS200_1_2V_SDR;
	if (p_conf->mmc_hs400_enhanced_strobe)
		host->caps2 |= MMC_CAP2_HS400_ES;
	if (p_conf->no_sdio)
		host->caps2 |= MMC_CAP2_NO_SDIO;
	if (p_conf->no_sd)
		host->caps2 |= MMC_CAP2_NO_SD;
	if (p_conf->no_mmc)
		host->caps2 |= MMC_CAP2_NO_MMC;

	mci_dbg("MCI: mmc_of_parse: host %d, caps: 0x%x, caps: 0x%x\n", host->index, host->caps, host->caps2);
	return 0;
}



int mmc_add_host(struct mmc_host *host)
{
	int err = 0;
	mmc_start_host(host);
        mci_hosts_m.array[mci_hosts_m.num++] = host;

	return 0;
}


struct mmc_host *mmc_alloc_host(int extra)
{
	int err;
	struct mmc_host *host;

	host = malloc(extra);
	if (!host)
		return NULL;
	memset(host, 0x00, extra);

	/* scanning will be enabled when we're ready */

	host->max_segs = 1;
	host->max_seg_size = PAGE_SIZE;

	host->max_req_size = PAGE_SIZE;
	host->max_blk_size = 512;
	host->max_blk_count = PAGE_SIZE / 512;

	host->ios.power_delay_ms = 10;

	return host;
}


/***************************************************************************
* Description:
* Parameters: rd_wr_flag: 0:write 1:read
*             blks_num:   块数
*             blk_addr:   emmc内的块地址
*             data_base:  要写/读的数据在内存中的地址
***************************************************************************/
int mmc_rw_simple(struct mmc_card *card, u32 rw_flags,
		u32 blks_num, u32 blk_addr, u64 data_base)
{
	int err = 0;
        struct mmc_host *host = card->host;
	struct mmc_request mrq = {};
	struct mmc_command cmd = {};
	struct mmc_data data = {};

	mci_dbg("MCI: mmc_rw_simple: blks_num: %d, blk_addr: 0x%x\n", blks_num, blk_addr);
        //write
	if (rw_flags == MMC_DATA_WRITE) {
		if (blks_num > 1) {
                        mrq.stop = &stop_cmd;
			cmd.opcode = MMC_WRITE_MULTIPLE_BLOCK;
                } else 
			cmd.opcode = MMC_WRITE_BLOCK;
	} else {
		if (blks_num > 1) {
                        mrq.stop = &stop_cmd;
			cmd.opcode = MMC_READ_MULTIPLE_BLOCK;
                } else 
			cmd.opcode = MMC_READ_SINGLE_BLOCK;
	}

	data.flags = rw_flags;
	data.host_data_base = data_base;
	data.blk_addr = blk_addr;
	data.blocks = blks_num;
	data.blksz = DEFAULT_BLKSZ;

	mrq.data = &data;
	mrq.cmd  = &cmd;
	mrq.host = host;

	cmd.data = &data;
	cmd.mrq = &mrq;
	cmd.arg = blk_addr;
	cmd.flags = MMC_RSP_R1 | MMC_CMD_ADTC;
	if (blks_num > 1)
		cmd.multi_blk_cmd = 1;

	mmc_wait_for_req(host, &mrq);
	if (cmd.error || data.error) {
		err = -1;
	}
        return err;
}

static inline void mmc_set_ios(struct mmc_host *host)
{
	struct mmc_ios *ios = &host->ios;
	host->ops->set_ios(host, ios);
}

void mmc_set_bus_width(struct mmc_host *host, unsigned int width)
{
	host->ios.bus_width = width;
	mmc_set_ios(host);
}


void mmc_set_timing(struct mmc_host *host, unsigned int timing)
{
	host->ios.timing = timing;
	mmc_set_ios(host);
}

void mmc_set_clock(struct mmc_host *host, unsigned int hz)
{
	if (hz && hz < host->f_min)
	        mci_dbg("MCI Warning: hz too small\n");

	if (hz > host->f_max)
		hz = host->f_max;

	host->ios.clock = hz;
	mmc_set_ios(host);
}




void mmc_set_initial_state(struct mmc_host *host)
{
	host->ios.bus_width = MMC_BUS_WIDTH_1;
	host->ios.timing = MMC_TIMING_LEGACY;
	host->ios.drv_type = 0;
	host->ios.enhanced_strobe = 0;

	mmc_set_ios(host);
}


int mmc_set_signal_voltage(struct mmc_host *host, int signal_voltage)
{
	int err = 0;
	return err;
}

void mmc_set_initial_signal_voltage(struct mmc_host *host)
{
	/* Try to set signal voltage to 3.3V but fall back to 1.8v or 1.2v */
	if (!mmc_set_signal_voltage(host, MMC_SIGNAL_VOLTAGE_330))
		mci_dbg("MCI: host initial signal voltage of 3.3v\n");
	else if (!mmc_set_signal_voltage(host, MMC_SIGNAL_VOLTAGE_180))
		mci_dbg("MCI: host initial signal voltage of 1.8v\n");
	else if (!mmc_set_signal_voltage(host, MMC_SIGNAL_VOLTAGE_120))
		mci_dbg("MCI: host initial signal voltage of 1.2v\n");
}


void mmc_power_off(struct mmc_host *host)
{
}

void mmc_power_up(struct mmc_host *host, u32 ocr)
{

	mci_dbg("MCI: mmc_power_up: host %d\n", host->index);

	if (host->ios.power_mode == MMC_POWER_ON)
		return;

	host->ios.vdd = fls(ocr) - 1;
	host->ios.power_mode = MMC_POWER_UP;

	/* Set initial state and call mmc_set_ios */
	mmc_set_initial_state(host);

	/* set signal voltage */
	mmc_set_initial_signal_voltage(host);

	/*
	 * This delay should be sufficient to allow the power supply
	 * to reach the minimum voltage.
	 */
	mdelay(host->ios.power_delay_ms);

	host->ios.clock = host->f_init;

	host->ios.power_mode = MMC_POWER_ON;
	mmc_set_ios(host);

	/*
	 * This delay must be at least 74 clock sizes, or 1 ms, or the
	 * time required to reach a stable voltage.
	 */
	mdelay(host->ios.power_delay_ms);
}


void mmc_power_cycle(struct mmc_host *host, u32 ocr)
{
	mmc_power_off(host);
	/* Wait at least 1 ms according to SD spec */
	mdelay(1);
	mmc_power_up(host, ocr);
}

void mmc_start_host(struct mmc_host *host)
{
	mci_dbg("MCI: mmc_start_host: host %d\n", host->index);

	host->f_init = max(freqs[0], host->f_min);
	host->ios.power_mode = MMC_POWER_UNDEFINED;

	mmc_power_up(host, host->ocr_avail);
        
}


static void mmc_hw_reset_for_init(struct mmc_host *host)
{
	if (!(host->caps & MMC_CAP_HW_RESET) || !host->ops->hw_reset)
		return;
	if (host->ops->hw_reset)
		host->ops->hw_reset(host);
}

int mmc_go_idle(struct mmc_host *host)
{
	int err;
	struct mmc_command cmd = {};

        mci_dbg("MCI: host %d go idle\n", host->index);

	cmd.opcode = MMC_GO_IDLE_STATE;
	cmd.arg = 0;
	cmd.flags = MMC_RSP_SPI_R1 | MMC_RSP_NONE | MMC_CMD_BC;

	err = mmc_wait_for_cmd(host, &cmd, 1);
	mdelay(1);

	return err;
}


static int mmc_rescan_try_freq(struct mmc_host *host, unsigned freq)
{
	host->f_init = freq;

	mci_dbg("MCI: host %d trying to scan card at %u Hz\n", host->index, host->f_init);

	mmc_power_up(host, host->ocr_avail);

	/*
	 * Some eMMCs (with VCCQ always on) may not be reset after power up, so
	 * do a hardware reset if possible.
	 */
	mmc_hw_reset_for_init(host);

	/*
	 * sdio_reset sends CMD52 to reset card.  Since we do not know
	 * if the card is being re-initialized, just send it.  CMD52
	 * should be ignored by SD/eMMC cards.
	 * Skip it if we already know that we do not support SDIO commands
	 */
#if 0
	if (!(host->caps2 & MMC_CAP2_NO_SDIO))   //ignore sdio card now in pmom

		sdio_reset(host);
#endif

	mmc_go_idle(host);
	in_detect = 1;

	if (!(host->caps2 & MMC_CAP2_NO_SD))
		mmc_send_if_cond(host, host->ocr_avail);

	in_detect = 0;
	/* Order's important: probe SDIO, then SD, then MMC */

#if 0
	if (!(host->caps2 & MMC_CAP2_NO_SDIO))    //ignore sdio card now in pmom
		if (!mmc_attach_sdio(host))
			return 0;
#endif
	if (!(host->caps2 & MMC_CAP2_NO_SD))
		if (!mmc_attach_sd(host))
			return 0;

	/* for mmc dev such as eMMC*/
	if (!(host->caps2 & MMC_CAP2_NO_MMC))
		if (!mmc_attach_mmc(host))
			return 0;

	return -EIO;
}

/*
 * Change the bus mode (open drain/push-pull) of a host.
 */
void mmc_set_bus_mode(struct mmc_host *host, unsigned int mode)
{
	/* ls controler bus mode is fixed */
}



/*
 * Mask off any voltages we don't support and select
 * the lowest voltage
 */
u32 mmc_select_voltage(struct mmc_host *host, u32 ocr)
{
	int bit;

	/*
	 * Sanity check the voltages that the card claims to
	 * support.
	 */
	if (ocr & 0x7F) {
		mci_dbg("MCI Warning: card claims to support voltages below defined range\n");
		ocr &= ~0x7F;
	}

	ocr &= host->ocr_avail;
	if (!ocr) {
		mci_dbg("MCI Warning: no support for card's volts\n");
		return 0;
	}

	if (host->caps2 & MMC_CAP2_FULL_PWR_CYCLE) {
		bit = ffs(ocr) - 1;
		ocr &= 3 << bit;
		mmc_power_cycle(host, ocr);
	} else {
		bit = fls(ocr) - 1;
		ocr &= 3 << bit;
		if (bit != host->ios.vdd)
			mci_dbg("MCI Warning: exceeding card's volts\n");
	}

	return ocr;
}



struct mmc_card *mmc_alloc_card(struct mmc_host *host)
{
	struct mmc_card *card;

	card = malloc(sizeof(struct mmc_card));

	if (!card)
		return NULL;

	memset(card, 0x00, sizeof(struct mmc_card));
	card->host = host;
	return card;
}


void mmc_rescan(struct mmc_host *host)
{
	int i = 0;
	mci_dbg("MCI: host %d mmc_rescan\n", host->index);

        /* detect from low freq to high freq*/
	for (i = 0; i < ARRAY_SIZE(freqs); i++) {
		if (!mmc_rescan_try_freq(host, max(freqs[i], host->f_min)))
			break;
		if (freqs[i] <= host->f_min)
			break;
	}
}


int mmc_send_status(struct mmc_card *card, u32 *status)
{
	
	int err;
	struct mmc_command cmd = {};

	cmd.opcode = MMC_SEND_STATUS;
	cmd.arg = card->rca << 16;
	cmd.flags = MMC_RSP_SPI_R2 | MMC_RSP_R1 | MMC_CMD_AC;

	err = mmc_wait_for_cmd(card->host, &cmd, 1);
	if (err)
		return err;

	if (status)
		*status = cmd.resp[0];

	return 0;
}

