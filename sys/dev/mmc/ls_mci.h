#ifndef LS_MCI_H
#define LS_MCI_H

#include "./core.h"


#define readl_uc(addr) (readl(PHYS_TO_UNCACHED(addr)))
#define writel_uc(v, addr) (writel(v, PHYS_TO_UNCACHED(addr)))
#define readb_uc(addr) (readb(PHYS_TO_UNCACHED(addr)))
#define writeb_uc(v, addr) (writeb(v, PHYS_TO_UNCACHED(addr)))





#define SDICON     	0x00
#define SDIPRE     	0x04
#define SDICMDARG  	0x08
#define SDICMDCON  	0x0c
#define SDICMDSTA  	0x10
#define SDIRSP0    	0x14
#define SDIRSP1    	0x18
#define SDIRSP2    	0x1C
#define SDIRSP3    	0x20
#define SDIDTIMER  	0x24
#define SDIBSIZE   	0x28
#define SDIDATCON  	0x2C
#define SDIDATCNT  	0x30
#define SDIDSTA    	0x34
#define SDIFSTA    	0x38
#define SDIINTMSK  	0x3C
#define SDIWRDAT   	0x40
#define SDISTAADD0 	0x44
#define SDISTAADD1 	0x48
#define SDISTAADD2 	0x4c
#define SDISTAADD3 	0x50
#define SDISTAADD4 	0x54
#define SDISTAADD5 	0x58
#define SDISTAADD6 	0x5c
#define SDISTAADD7	0x60
#define SDIINTEN   	0x64
#define SDIDLLMVAL    	0xf0
#define SDIDLLCON       0xf4
#define SDIPDELAY       0xf8
#define SDISESEL 	0xfc

#define SDIPRE_REVCLOCK       (1<<31)
#define SDICON_RESET          (1<<8)
#define SDICON_CLOCKENABLE    (1<<0)


#define DAT_4_WIRE 1
#define ERASE_START_ADDR 0x5000
#define ERASE_End_ADDR   0x5000

#define SDICMDSTAT            (0x10)
#define SDITIMER              (0x24)
#define SDIDCON               (0x2C)
#define SDIDCNT               (0x30)


#define SDICON_RESET          (1<<8)
#define SDICON_MMCCLOCK       (1<<5)
#define SDICON_BYTEORDER      (1<<4)
#define SDICON_SDIOIRQ        (1<<3)
#define SDICON_RWAITEN        (1<<2)
#define SDICON_FIFORESET      (1<<1)
#define SDICON_CLOCKTYPE      (1<<0)

#define SDIPRE_REVCLOCK       (1<<31)

#define SDICMDCON_CMD6DATA    (1<<18)
#define SDICMDCON_AUTOSTOP    (1<<12)
#define SDICMDCON_WITHDATA    (1<<11)
#define SDICMDCON_LONGRSP     (1<<10)
#define SDICMDCON_WAITRSP     (1<<9)
#define SDICMDCON_CMDSTART    (1<<8)
#define SDICMDCON_SENDERHOST  (1<<6)
#define SDICMDCON_INDEX       (0x3f)

#define SDICMDSTAT_CRCFAIL    (1<<12)
#define SDICMDSTAT_CMDSENT    (1<<11)
#define SDICMDSTAT_CMDTIMEOUT (1<<10)
#define SDICMDSTAT_RSPFIN     (1<<9)
#define SDICMDSTAT_XFERING    (1<<8)
#define SDICMDSTAT_INDEX      (0xff)

#define SDIDCON_8BIT_BUS      (1<<26)
#define SDIDCON_IRQPERIOD     (1<<21)
#define SDIDCON_TXAFTERRESP   (1<<20)
#define SDIDCON_RXAFTERCMD    (1<<19)
#define SDIDCON_BUSYAFTERCMD  (1<<18)
#define SDIDCON_BLOCKMODE     (1<<17)
#define SDIDCON_4BIT_BUS      (1<<16)
#define MISC_CTRL_SDIO_DMAEN  (1<<15)
#define SDIDCON_STOP          (1<<14)
#define SDIDCON_DATMODE	      (3<<12)
#define SDIDCON_BLKNUM        (0x7ff)

/* constants for SDIDCON_DATMODE */
#define SDIDCON_XFER_READY    (0<<12)
#define SDIDCON_XFER_CHKSTART (1<<12)
#define SDIDCON_XFER_RXSTART  (2<<12)
#define SDIDCON_XFER_TXSTART  (3<<12)

#define SDIDCON_BLKNUM_MASK   (0xFFF)
#define SDIDCNT_BLKNUM_SHIFT  (12)

#define SDIDSTA_RDYWAITREQ    (1<<10)
#define SDIDSTA_SDIOIRQDETECT (1<<9)
#define SDIDSTA_FIFOFAIL      (1<<8)
#define SDIDSTA_CRCFAIL       (1<<7)
#define SDIDSTA_RXCRCFAIL     (1<<6)
#define SDIDSTA_DATATIMEOUT   (1<<5)
#define SDIDSTA_XFERFINISH    (1<<4)
#define SDIDSTA_BUSYFINISH    (1<<3)
#define SDIDSTA_SBITERR       (1<<2)
#define SDIDSTA_TXDATAON      (1<<1)
#define SDIDSTA_RXDATAON      (1<<0)

#define SDIFSTA_TFDET          (1<<13)
#define SDIFSTA_RFDET          (1<<12)
#define SDIFSTA_TFFULL         (1<<11)
#define SDIFSTA_TFEMPTY        (1<<10)
#define SDIFSTA_RFLAST         (1<<9)
#define SDIFSTA_RFFULL         (1<<8)
#define SDIFSTA_RFEMPTY        (1<<7)
#define SDIFSTA_COUNTMASK      (0x7f)

#define SDIIMSK_RESPONSEND     (1<<14)
#define SDIIMSK_READWAIT       (1<<13)
#define SDIIMSK_SDIOIRQ        (1<<12)
#define SDIIMSK_FIFOFAIL       (1<<11)


#define SDIIMSK_RESPONSECRC    (1<<8)
#define SDIIMSK_CMDTIMEOUT     (1<<7)
#define SDIIMSK_CMDSENT        (1<<6)
#define SDIIMSK_PROGERR        (1<<4)
#define SDIIMSK_TXCRCFAIL      (1<<3)
#define SDIIMSK_RXCRCFAIL      (1<<2)
#define SDIIMSK_DATATIMEOUT    (1<<1)
#define SDIIMSK_DATAFINISH     (1<<0)

#define SDIIMSK_BUSYFINISH     (1<<6)
#define SDIIMSK_SBITERR        (1<<5)
#define SDIIMSK_TXFIFOHALF     (1<<4)
#define SDIIMSK_TXFIFOEMPTY    (1<<3)
#define SDIIMSK_RXFIFOLAST     (1<<2)
#define SDIIMSK_RXFIFOFULL     (1<<1)
#define SDIIMSK_RXFIFOHALF     (1<<0)

#define SDIINT_CLEARALL        0x1ff
#define SDIINT_ENALL           0x1ff


enum dll_type {
	ALL_DELAY,
	RD_DELAY,
	PAD_DELAY,
};
#define RD_DELAY_MASK        0x0000FF00
#define PAD_DELAY_MASK       0x000000FF


#define MAX_HOSTS 2

#define PCI_AUTO_ADDR   	0x0



#define clear_imask_all(x)    clear_imask((0xFFFFFFFF), (x))

struct ls_mci_host;


/*just like dts property in linux*/
struct ls_mci_pdata {
#define LOONGSON_SDIO_EMMC_VER_1_0	0x100
#define LOONGSON_SDIO_EMMC_VER_1_1	0x110
    u32                    version;
    unsigned long          ocr_avail;
    void		   (*send_cmd_fixup)(struct ls_mci_host *host, struct mmc_command *cmd);
    void	           (*get_cmd_resp_fixup)(struct ls_mci_host *host, struct mmc_command *cmd);
    void		   (*conf_mux)(struct ls_mci_host *ls_host);
    void	           (*set_dll)(struct ls_mmc_host *ls_host, u32 dll);
    void                   (*hw_host_init)(struct ls_mmc_host *ls_host);
    void                   (*get_base_addr)(struct ls_mmc_host *ls_host);
};

enum mci_host_state {
    HOST_DISABLE,
    HOST_ENABLE,
};

enum ls_mmc_host_type {
	SDIO_LS2P500,
	SDIO_LS2K300,
	EMMC_LS2K1500,
	SDIO_LS2K1500,
	EMMC_LS2K2000,
	SDIO_LS2K2000,
};


enum ls2k_mci_waitfor {
	COMPLETION_NONE,
	COMPLETION_FINALIZE,
	COMPLETION_CMDSENT,            /* just send cmd, such as cmd0 */
	COMPLETION_RSPFIN,             /* wait for cmd  resp */
	COMPLETION_XFERFINISH,         /* wait for data finish */
	COMPLETION_XFERFINISH_RSPFIN,  /* wait for data trans cmds send ok*/
};

struct ls_mci_host {
    u32			    index;           	/* index of controler in soc */
    u32                     enable;             /* enable or disable in hardware */
    u32                     auto_stop;          /* controler can send stop cmd automatically*/
    u32                     wdma;
    u32                     rdma;
    u32			    clock_frequency;    /* input clk */
    u64                     dma_desc_addr;
    u32			    ls_h_type;          /* host type of SOC type and controler type */
    u32                     ocr_avail;
    u64			    base;   	        /* base addr */
    u32			    sdr_dll;            /* sdr dll for controler */
    struct host_conf	    p_conf;             /* pre conf by user */
    struct mmc_host	    *host;              /* strut of mmc core */
    struct ls_mci_pdata     pdata;
    struct mmc_request      *cur_mrq;           /* current mrq */
    enum ls2k_mci_waitfor   complete_what;
    int                     cmd_is_stop;
    int                     dma_complete;
};

static void inline  clear_imask(u32 mask, struct ls_mci_host *ls_host)
{
	writel(mask, ls_host->base + SDIINTMSK);
}

static void ls2k_mci_send_command(struct ls_mci_host *ls_host, struct mmc_command *cmd);
void ls_mci_probe(void);
static void ls_mci_send_request(struct mmc_host *host);
#endif
