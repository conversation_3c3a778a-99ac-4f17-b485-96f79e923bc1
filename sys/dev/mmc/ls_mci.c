#include <pmon.h>
#include <stdlib.h>
#include <stdio.h>
#include <ctype.h>
#include <unistd.h>
#include <string.h>
#include <termio.h>
#include <fcntl.h>
#include <file.h>
#include <ramfile.h>
#include <sys/unistd.h>
#undef _KERNEL
#include <errno.h>
#include <linux/types.h>
#include <pmon/dev/loopdev.h>
#include "./ls_mci.h"
#include "./core.h"



/*
 * These are general common host configs.
 * Different soc can override these in 
 * ls_host_default_set.
 */
struct ls_mci_host common_host_config[MAX_HOSTS] = {
        {
		.index		     = 0,
		.enable		     = SDIO0_EN,
		.auto_stop           = 1,
                .sdr_dll             = 0,
		.clock_frequency     = 100000000,
		.ocr_avail           = MMC_VDD_32_33 | MMC_VDD_33_34,        /* set by actual host voltage*/
		.p_conf ={
		        .max_frequency     = 50000000,	
			.cap_mmc_highspeed = true,
			.bus_width	   = 8,
			.no_sd             = true,
			.no_sdio           = true,
		},
	},
	{
		.index		     = 1,
		.enable		     = SDIO1_EN,
		.auto_stop           = 1,
                .sdr_dll             = 0,
		.clock_frequency     = 100000000,
		.ocr_avail           = MMC_VDD_32_33 | MMC_VDD_33_34,        /* set by actual host voltage*/
		.p_conf ={
			.bus_width	 = 4,
			.max_frequency   = 25000000,  
			.no_mmc          = true,
		},
	},
};


/*
****************************************************************************
****************************************************************************
*/

extern int mci_init_flag;


static void ls_host_hw_init(struct ls_mci_host *ls_host)
{
	mci_dbg("MCI: hw init: ls host %d\n", ls_host->index);
#if defined(LOONGSON_2K1500)
	if (ls_host->index == 0) {
	    	ls_host->base = (readl(BONITO_PCICFG0_BASE_VA | (28ULL << 11) + 0x10ULL) & 0xfffffff0) | 0x8000000000000000ULL;
	}
	if (ls_host->index == 1) {
	    	ls_host->base = (readl(BONITO_PCICFG0_BASE_VA | (28ULL << 11) | (1ULL << 8) + 0x10ULL) & 0xfffffff0) | 0x8000000000000000ULL;
	}
	u32 pad_value = readl_uc(0x100106e0); 
        pad_value &= ~0x3;
	writel_uc(pad_value | 0x2, 0x100106e0);

#elif defined(LOONGSON_2K2000)
	if (ls_host->index == 0) {
		ls_host->base = PHYS_TO_UNCACHED(0x79990000);
	}
	if (ls_host->index == 1) {
		ls_host->base = PHYS_TO_UNCACHED(0x79991000);
	}
	u32 pad_value = readl_uc(0x100106e0); 
	pad_value &= ~0x3;
	writel_uc(pad_value | 0x2, 0x100106e0);
#elif defined(LOONGARCH_2K500)
	if (ls_host->index == 0) {
		ls_host->base = PHYS_TO_UNCACHED(0x1ff64000);
                ls_host->wdma = PHYS_TO_UNCACHED(0x1fe10c30);
	        ls_host->rdma = PHYS_TO_UNCACHED(0x1fe10c30);

	}
        if (ls_host->index == 1) {
	        ls_host->base = PHYS_TO_UNCACHED(0x1ff66000);
                ls_host->wdma = PHYS_TO_UNCACHED(0x1fe10c20);
	        ls_host->rdma = PHYS_TO_UNCACHED(0x1fe10c20);
                readl_uc(0x1fe10100) |= 0x0000c000;   //sdio1 use dma2
	}
#elif defined(LOONGARCH_2P500)
	if (ls_host->index == 0) {	
		ls_host->base = PHYS_TO_UNCACHED(0x14210000);
                readl_uc(0x14000490) &= ~0x0000ff00;
	    	readl_uc(0x14000490) |= 0x0000aa00;
		readl_uc(0x14000494) |= 0x00000fff;
	}
        if (ls_host->index == 1) {
	        ls_host->base = PHYS_TO_UNCACHED(0x14218000);
                readl_uc(0x14000490) &= ~0x000000ff;
		readl_uc(0x14000490) |= 0x00000055;
		readl_uc(0x14000494) |= 0x00fff000;
	}
#elif defined(LOONGARCH_2P300)
	if (ls_host->index == 0) {	
		ls_host->base = PHYS_TO_UNCACHED(0x14210000);
		readl_uc(0x14000494) |=  0x0fffff00;
	}
        if (ls_host->index == 1) {
	        ls_host->base = PHYS_TO_UNCACHED(0x14218000);
		readl_uc(0x14000494) |= 0xf0000000;
		readl_uc(0x14000498) |= 0x000000ff;
	}

#elif defined(LOONGARCH_2K300)
	if (ls_host->index == 0) {	
		ls_host->base = PHYS_TO_UNCACHED(0x16140000);
	    	readl_uc(0x160004a4) |=  0xfff00000;
	    	readl_uc(0x160004a8) |=  0x000000ff;
	}
        if (ls_host->index == 1) {
		ls_host->base = PHYS_TO_UNCACHED(0x16148000);
	    	readl_uc(0x160004a8) |=  0x000fff00;
#if defined(AIC8800_WIFI)
#if (NPAI_99 > 0)
		readl_uc(0x160004a0) &= ~0x0000f300;
		readl_uc(0x160004a4) &= ~0x00000f3c;
		
		writeb_uc(0x00, 0x16104844);     //GPIO 68
		writeb_uc(0x01, 0x16104944);

		writeb_uc(0x01, 0x16104846);     //GPIO 70

		writeb_uc(0x01, 0x16104847);    //GPIO 71
							       
		writeb_uc(0x00, 0x16104851);    //GPIO 81
		writeb_uc(0x01, 0x16104951);     
		
		writeb_uc(0x01, 0x16104852);    //GPIO 82

		writeb_uc(0x00, 0x16104854);     //GPIO 84 				       
		writeb_uc(0x01, 0x16104954); 

		writeb_uc(0x01, 0x16104855);     //GPIO 85
#elif (NPAI_99_PLUS > 0 || NPAI_99_PLUS_SINGLE_NET > 0)
                /* GPIO复用 */
                // GPIO68 GPIO71
                readl_uc(0x160004a0) &= ~0x0000c300;
                //GPIO82 GPIO87
                readl_uc(0x160004a4) &= ~0x0000c030;

                // GPIO68-HWAKE [O] 始终拉高
                writeb_uc(0x00, 0x16104844);     //GPIO 68
                writeb_uc(0x01, 0x16104944);

                // GPIO71-DWAKE [I]
                writeb_uc(0x01, 0x16104847);    //GPIO 71

                // GPIO82-BT_RST 始终拉低
                writeb_uc(0x00, 0x16104852);    //GPIO 82
                writeb_uc(0x00, 0x16104952);    //GPIO 82

                // GPIO87-D_H_W [I]
                writeb_uc(0x01, 0x16104857);     //GPIO 87
#endif
#endif

	}
#endif

}

static void send_cmd_fixup(struct ls_mci_host *ls_host, struct mmc_command *cmd)
{
	int retry_n = 10000;
	u32 s_flag, t_flag, tmp;
    
#if defined(LOONGSON_2K1500)|| defined(LOONGSON_2K2000)

	if (cmd->opcode == 0x19) {  //for cmd25  multiple blocks write
		while (retry_n--) {
			tmp =  readl(ls_host->base + 0x2c);  
			s_flag = tmp & (0x1 << 14);
			tmp =  readl(ls_host->base + 0x38);
			t_flag = tmp & (0x1 << 11);

			if (s_flag && t_flag)  //tx fifo full
			    	break;
			if (retry_n < 5000)
			    	delay(10);
		}	
		if (retry_n < 0)
		    	mci_dbg("MCI: Warning: may losing int when tx fifo is not full\n");
	}
#endif
}



static inline int ls_check_busy(struct ls_mci_host *ls_host, struct mmc_command *cmd)
{
	int err = 0;
	int retry = 100000;
	u32 resp0;

	if (cmd->flags & MMC_RSP_BUSY) {
		mci_dbg("MCI: ls wait busy: cmd %d\n", cmd->opcode);
		while (retry--) {
			resp0 = readl(ls_host->base + SDIINTMSK);
			if (resp0 & 0x200)
				break;
			if (retry < 50000)
				delay(10);
		}
		if (retry < 0) {
			err = -ETIMEDOUT;
			cmd->error = err;
		}
	}
	return err;
}


static void finalize_request(struct ls_mci_host *ls_host)
{
	struct mmc_request *mrq = ls_host->cur_mrq;
	struct mmc_command *cmd;
	int err;

	if (ls_host->complete_what != COMPLETION_FINALIZE)
		return;
	if (!mrq)
		return;

	cmd = ls_host->cmd_is_stop ? mrq->stop : mrq->cmd;

	mci_dbg("MCI: finalize_request, cmd %d, cmd flags: 0x%x\n", cmd->opcode, cmd->flags);

	err = ls_check_busy(ls_host, cmd);
	if (err) {
	        mci_err("MCI Err: wait busy timeout cmd: %d\n", cmd->opcode);
	        return;
	}

	/* get resp from controler */
	cmd->resp[0] = readl(ls_host->base + SDIRSP0);
	cmd->resp[1] = readl(ls_host->base + SDIRSP1);
	cmd->resp[2] = readl(ls_host->base + SDIRSP2);
	cmd->resp[3] = readl(ls_host->base + SDIRSP3);
	
	clear_imask_all(ls_host);

        if (!ls_host->auto_stop) {  // use auto stop?
	        if (cmd->data && cmd->data->stop && (!ls_host->cmd_is_stop)) {
		        ls_host->cmd_is_stop = 1;
		        ls_mci_send_request(ls_host->host);
		        return;
	        }
        }

	/* If we have no data transfer we are finished here */
	if (!mrq->data)
		goto request_done;

request_done:
	ls_host->complete_what = COMPLETION_NONE;
	ls_host->cur_mrq = NULL;
	mmc_request_done(ls_host->host, mrq);
}


static int cmd_check(struct mmc_host *host)
{
    	int err;
    	return err;
}




static void ls_get_irq_event(struct mmc_host *host)
{
	struct ls_mci_host *ls_host = mmc_priv(host); 
	struct mmc_command *cmd;
	struct mmc_request *mrq;
	u32 mci_imsk;
	int retry;
        int err;


	mrq = ls_host->cur_mrq;
	cmd = mrq->cmd;

	retry = 100000;
	mci_dbg("MCI: get irq event: cmd %d\n", cmd->opcode);

	while(retry--) {
	    mci_imsk = readl(ls_host->base + SDIINTMSK);
            if(mci_imsk & 0x1C0)
		break;
	    if(retry < 50000)
		delay(10);
	}

	if(retry < 0) {
	    mci_err("MCI Err: wait irq event time out: cmd %d, imsk: 0x%x\n", cmd->opcode, mci_imsk);
	    cmd->error = -ETIMEDOUT;
            goto fail_transfer;
	}

        mci_dbg("MCI: complete_what: cmd %d, 0x%x, imsk: 0x%x\n", cmd->opcode, ls_host->complete_what, mci_imsk);

	if ((ls_host->complete_what == COMPLETION_NONE) ||
	    (ls_host->complete_what == COMPLETION_FINALIZE)) {
		goto irq_out;
	}

        /* mrq is null*/
	if (!ls_host->cur_mrq) {
		goto irq_out;
	}

	cmd = ls_host->cmd_is_stop ? ls_host->cur_mrq->stop : ls_host->cur_mrq->cmd;

        /* cmd is null*/
	if (!cmd) 
		goto irq_out;
	

	cmd->error = 0;
        /* cmd timeout */ 
	if (mci_imsk & SDIIMSK_CMDTIMEOUT) {
		mci_err("MCI Err: cmd timeout, cmd %d\n", cmd->opcode);
		cmd->error = -ETIMEDOUT;
		goto fail_transfer;
	}
        
	/* cmd send ok*/
	if ((mci_imsk & SDIIMSK_CMDSENT)) {
		/*if just wait for cmd sent*/
		if (ls_host->complete_what == COMPLETION_CMDSENT) {
			goto close_transfer;
		}
	}
        /* cmd resp crc err */
	if (mci_imsk & SDIIMSK_RESPONSECRC) {
		/* if resp needs rx CRC check*/
		if (cmd->flags & MMC_RSP_CRC) {
			if (ls_host->cur_mrq->cmd->flags & MMC_RSP_136) {
				mci_dbg("MCI Err: fixup: ignore CRC fail with long rsp, cmd %d\n", cmd->opcode);
			}
		}
	}
        /* cmd send ok and have resp or data*/
	if ((mci_imsk & SDIIMSK_CMDSENT)) {
		if (ls_host->complete_what == COMPLETION_RSPFIN) {
			goto close_transfer;
		}
		/* wait for data finish */
		if (ls_host->complete_what == COMPLETION_XFERFINISH_RSPFIN)
			ls_host->complete_what = COMPLETION_XFERFINISH;
	}

        /* data is null, don't need continue to check*/
	if (!cmd->data)
		goto irq_out;

        clear_imask(mci_imsk & 0x1C0, ls_host);	

	retry = 100000;
	while(retry--) {
	        mci_imsk = readl(ls_host->base + SDIINTMSK);
                if(mci_imsk & 0x1F)
		        break;
	        if(retry < 50000)
			delay(10);
	}

	if (retry < 0) {
	        mci_err("MCI Err: wait data irq time out: cmd %d, imsk: 0x%x\n", cmd->opcode, mci_imsk);
	        cmd->error = -ETIMEDOUT;
                goto fail_transfer;
	}

        /* rx crc err*/
	if (mci_imsk & SDIIMSK_RXCRCFAIL) {
		mci_err("MCI Err: bad rx data crc, cmd %d\n", cmd->opcode);
		cmd->data->error = -EILSEQ;
		goto fail_transfer;
	}
        /* tx crc err*/
	if (mci_imsk & SDIIMSK_TXCRCFAIL) {
		mci_err("MCI Err: bad tx data crc, cmd %d\n", cmd->opcode);
		cmd->data->error = -EILSEQ;
		goto fail_transfer;
	}
        /* data timeout */
	if (mci_imsk & SDIIMSK_DATATIMEOUT) {
		mci_err("MCI Err: data timeout, cmd %d\n", cmd->opcode);
		cmd->data->error = -ETIMEDOUT;
		goto fail_transfer;
	}
        /* data finish */
	if ((mci_imsk & SDIIMSK_DATAFINISH)) {
		if (ls_host->complete_what == COMPLETION_XFERFINISH) {
			ls_host->dma_complete = 1;
			goto close_transfer;
		}

		if (ls_host->complete_what == COMPLETION_XFERFINISH_RSPFIN)
			ls_host->complete_what = COMPLETION_RSPFIN;
	}
        
clear_imask_bits:
	goto irq_out;
fail_transfer:
close_transfer:
	ls_host->complete_what = COMPLETION_FINALIZE;
	clear_imask(mci_imsk & 0x1FF, ls_host);

	//if (ls_host->pdata->irq_fixup)
	  //  ls_host->pdata->irq_fixup(host, cmd);

        finalize_request(ls_host);
irq_out:
	clear_imask(mci_imsk & 0x1FF, ls_host);
}


static void set_dll_reg(struct ls_mci_host *ls_host, u32 dll, enum dll_type type)
{
	u32 value = dll & 0xFFFF;
	u32 con   = 0;
        u32 old = readl(ls_host->base + SDIPDELAY);

#if defined(LOONGSON_2K1500) || defined(LOONGSON_2K2000)
	        con = 0x3c000000;
#elif defined(LOONGARCH_2K500)
	        con = 0xf0000000;
#elif defined(LOONGARCH_2P300) || defined(LOONGARCH_2P500)
	        con = 0xf0000000;
#elif defined(LOONGARCH_2K300)
	        con = 0xf0000000;
#endif

	switch(type) {
	case RD_DELAY:
                old &= ~RD_DELAY_MASK;	
                value = dll & RD_DELAY_MASK;
		value |= old;
		break;
	case PAD_DELAY:
		old &= ~PAD_DELAY_MASK;
		value = dll & PAD_DELAY_MASK;
		value |= old;
		break;
	case ALL_DELAY:
                break;
	default:
		mci_err("MCI Err: err dll flag\n");
		return;
	}

	writel(con, ls_host->base + SDIDLLCON);
	writel(value, ls_host->base + SDIPDELAY);
	/* must make clk stable after dll setting */
	mdelay(10);
}



static void ls_mci_set_clk(struct ls_mci_host *ls_host, struct mmc_ios *ios)
{
	u32 mci_psc;
	struct mmc_host *host = ls_host->host;
	/* Set clock */
	for (mci_psc = 1; mci_psc < 255; mci_psc++) {
		host->actual_clock = ls_host->clock_frequency / mci_psc;

		if (host->actual_clock <= ios->clock)
			break;
	}
	if (mci_psc > 255)
		mci_psc = 255;

	writel(mci_psc | SDIPRE_REVCLOCK, ls_host->base + SDIPRE);

}

static void ls_mci_set_ios(struct mmc_host *host, struct mmc_ios *ios)
{
	struct ls_mci_host *ls_host = mmc_priv(host);
	u32 mci_con;

	mci_dbg("MCI: set ios: ios clock %u\n", ios->clock);

	mci_con = readl(ls_host->base + SDICON);

	writel(SDICON_RESET, ls_host->base + SDICON);
	mdelay(10);
	writel(0x3, ls_host->base + SDICON);  //enable clk
	/* this delay must be needed, otherwise the value can't write into register valid*/
	mdelay(10);

	writel(0x3ff, ls_host->base + SDIINTEN);


	ls_mci_set_clk(ls_host, ios);
	
	/* Set CLOCK_ENABLE */
	if (ios->clock)
	   	mci_con |= SDICON_CLOCKENABLE;
	writel(mci_con, ls_host->base + SDICON);

        if (ls_host->sdr_dll)
        	set_dll_reg(ls_host, ls_host->sdr_dll, RD_DELAY);


	mci_dbg("MCI: ls host %d running at %lukHz (requested: %ukHz).\n",
			ls_host->index, host->actual_clock/1000, ios->clock/1000);
}


static int ls_mci_setup_data(struct ls_mci_host *ls_host, struct mmc_data *data)
{
	u32 dcon;
	struct mmc_host *host = ls_host->host;
	struct mmc_ios *ios = &host->ios;

	mci_dbg("MCI: ls_mci_setup_data blocks: %d\n", data->blocks);

	/* write DCON register */
	if (!data) {
		writel(0, ls_host->base + SDIDCON);
		return 0;
	}

        /* check blk size */
	if ((data->blksz & 3) != 0) {
		if (data->blocks > 1)
			return -EINVAL;
	}
	/* blocks*/
	dcon  = data->blocks & SDIDCON_BLKNUM_MASK;

	/* bus width */
	if (ios->bus_width == MMC_BUS_WIDTH_4) {
		dcon |= SDIDCON_4BIT_BUS;
	} else if (ios->bus_width == MMC_BUS_WIDTH_8) {
		dcon |= SDIDCON_8BIT_BUS;
	}

	/* data start and enable dma */
	dcon |= 3 << 14;
	writel(dcon, ls_host->base + SDIDCON);
	
	writel(data->blksz, ls_host->base + SDIBSIZE);

	/* write TIMER register */
	writel(0xFFFFFFFF, ls_host->base + SDITIMER);

	return 0;
}

static void ls_mci_send_command(struct ls_mci_host *ls_host, struct mmc_command *cmd) 
{
	u32 ccon;
	struct ls_mci_pdata *pdata = &ls_host->pdata;

        send_cmd_fixup(ls_host, cmd);	

	if (cmd->data) {
		ls_host->complete_what = COMPLETION_XFERFINISH_RSPFIN;
	} else if (cmd->flags & MMC_RSP_PRESENT)
		ls_host->complete_what = COMPLETION_RSPFIN;
	else
		ls_host->complete_what = COMPLETION_CMDSENT;

        writel(0xFFFFFFFF, ls_host->base + 0x3c);

	writel(cmd->arg, ls_host->base + SDICMDARG);
	ccon  = cmd->opcode & SDICMDCON_INDEX;
	ccon |= SDICMDCON_SENDERHOST | SDICMDCON_CMDSTART;
        /* if support auto stop */
	if (ls_host->auto_stop && cmd->multi_blk_cmd)
	       ccon |= SDICMDCON_AUTOSTOP;

	/* Emmc cmd6 do not need data transfer */
	if (pdata->version > LOONGSON_SDIO_EMMC_VER_1_0 &&
			cmd->opcode == SD_SWITCH && cmd->data)
		ccon |= SDICMDCON_CMD6DATA;

	if (cmd->flags & MMC_RSP_PRESENT)
		ccon |= SDICMDCON_WAITRSP;

	if (cmd->flags & MMC_RSP_136)
		ccon |= SDICMDCON_LONGRSP;

	writel(ccon, ls_host->base + SDICMDCON);

	mci_dbg("MCI: ls_mci_send_command: cmd %d, arg 0x%x\n", cmd->opcode, cmd->arg);
}



static int ls_mci_prepare_dma(struct ls_mci_host *ls_host, struct mmc_data *data)
{
    	u64 wdma_base     = ls_host->wdma;
    	u64 rdma_base     = ls_host->rdma;

        u64 fifo_base     = ((ls_host->base & 0xffffffff) + 0x40);
    	u64 data_phy_addr = (u64)(data->host_data_base);
    	u32 blk_size      = data->blksz;
 
    	u64 dma_desc_addr = ls_host->dma_desc_addr;
    
    	/* flush cache!!!!!!!!!!!!!!!!!!!!!!*/
    	writel(0x0,                dma_desc_addr + 0x0);              //next dma desc addr is invalid
    	writel((u32)data_phy_addr, dma_desc_addr + 0x4);              //addr in mem
    	writel(fifo_base,          dma_desc_addr + 0x8);                         
    	writel((data->blocks * blk_size) / 4, dma_desc_addr + 0xc);   //data length
    	writel(0x90000000 | (data_phy_addr >> 32), (dma_desc_addr + 0x24)); 
    	writel(0x00,               dma_desc_addr + 0x10);             //dma_step_length 
    	writel(0x01,               dma_desc_addr + 0x14);             //dma_step_times
												    
    	if (data->flags == MMC_DATA_READ) {
		writel(0x0, dma_desc_addr + 0x18);                    //read
 		writel_uc(dma_desc_addr | 0x8, rdma_base);            //dma desc addr
    	} else {
		writel(0x1000, dma_desc_addr + 0x18);                 //write
        	writel_uc(dma_desc_addr | 0x8, wdma_base);            //dma desc addr
    	} 
    	return 0;
}


static void ls_mci_send_request(struct mmc_host *host)
{
	int res;
	struct ls_mci_host *ls_host = mmc_priv(host);
	struct mmc_request *mrq = ls_host->cur_mrq;
	struct mmc_command *cmd = ls_host->cmd_is_stop ? mrq->stop : mrq->cmd;
        
	mci_dbg("MCI: ls_mci_send_request: cmd %d\n", cmd->opcode);

#if defined(LOONGARCH_2K500)
        delay(500);   // must delay for 2K500, but don't know why?
#endif

	if (cmd->data) {
		res = ls_mci_setup_data(ls_host, cmd->data);
		if (res) {
		    mci_err("MCI Err: setup data error %d\n", res);
		    cmd->error = res;
		    cmd->data->error = res;
		    mmc_request_done(host, mrq);
		    return;
		}
		res = ls_mci_prepare_dma(ls_host, cmd->data);
		if (res) {
		    mci_err("MCI Err: data prepare error %d\n", res);
		    cmd->error = res;
		    cmd->data->error = res;
		    mmc_request_done(host, mrq);
		    return;
		}
	}
	/* Send command */
	ls_mci_send_command(ls_host, cmd);
}




static void ls_mci_request(struct mmc_host *host, struct mmc_request *mrq)
{
	struct ls_mci_host *ls_host = mmc_priv(host);
 
	ls_host->cmd_is_stop = 0;
	ls_host->cur_mrq = mrq;

	ls_mci_send_request(host);

}

static struct mmc_host_ops ls_mci_ops = {
	.request	 = ls_mci_request,
	.set_ios     	 = ls_mci_set_ios,
	.wait_handle_irq = ls_get_irq_event,
};

static void ls_host_default_set(struct ls_mci_host *ls_host) 
{
	struct mmc_host *host   = ls_host->host;

	ls_host->wdma    = ls_host->base + 0x400;
	ls_host->rdma    = ls_host->base + 0x800;

        ls_host->dma_desc_addr = malloc(40);
        if (!ls_host->dma_desc_addr) {
                mci_err("MCI Err: can not alloc dma desc\n");
                return;
        }

#if defined(LOONGARCH_2P500)
	if (ls_host->index == 0) {
                ls_host->clock_frequency = 125000000,
	        ls_host->p_conf.max_frequency = 42000000;
                ls_host->sdr_dll = 0x7800;
        }
#elif defined(LOONGARCH_2P300)
	if (ls_host->index == 0) {
                ls_host->sdr_dll = 0x7800;
        }
#elif defined(LOONGSON_2K1500)
	if (ls_host->index == 0) {
                ls_host->sdr_dll = 0x7800;
        }
#elif defined(LOONGARCH_2K500)
	if (ls_host->index == 0) {
                ls_host->p_conf.max_frequency = 25000000;
		ls_host->p_conf.bus_width = 4;
		ls_host->p_conf.no_sd = false;
		ls_host->p_conf.no_sdio = true;
        }
#elif defined(LOONGARCH_2K300)
        if (ls_host->index == 1) {
#if defined(AIC8800_WIFI)
#if (NPAI_99_PLUS > 0 || NPAI_99_PLUS_SINGLE_NET > 0)
        /* fix emmc bug for "corrupt object file" */
        ls_host->sdr_dll = 0x7800
#endif
#endif
        }
#endif

	host->ops		= &ls_mci_ops;
	host->ocr_avail		= ls_host->ocr_avail;
	host->f_min		= ls_host->clock_frequency / 256;
	host->f_max		= ls_host->clock_frequency;
        host->index             = ls_host->index;
	host->max_blk_count	= 4095;
	host->max_blk_size	= 4095;
	host->max_req_size	= host->max_blk_count * host->max_blk_size;
	host->max_segs		= 1;
	host->max_seg_size	= host->max_req_size;
	host->caps 		= 0;
	host->caps2 		= 0;

}


static int ls_mci_add_host(struct ls_mci_host *ls_host)
{
    	struct ls_mci_pdata *pdata = &ls_host->pdata;
    	struct mmc_host *host;
    	int ret;

    	mci_dbg("MCI: mci add ls host %d \n", ls_host->index);
    	/*alloc mmc host, and init partial member*/
    	host = mmc_alloc_host(sizeof(struct mmc_host));

    	if (!host) {
		ret = -ENOMEM;
		goto malloc_err;
    	}	

    	ls_host->host = host;
    	host->private = ls_host;

    	/* config mux and hw init*/
    	ls_host_hw_init(ls_host);

    	/* do some default set for ls controler */
    	ls_host_default_set(ls_host);

    	/*parse pre conf by user*/
    	mmc_of_parse(host, &ls_host->p_conf);
    
    	/*register host to core*/
    	ret = mmc_add_host(ls_host->host);
   	if (ret) {
		mci_err("MCI Err: add host to core err %d\n", ret);
		goto add_host_err;
    	}

    	return ret;

add_host_err:
malloc_err:
    	return ret;
}

void ls_mci_probe(void)
{
        int host_index = 0;
    	int err;
    	struct ls_mci_host *ls_host;

    	mci_dbg("MCI: ls mci system init start\n");

    	while (host_index < MAX_HOSTS) {
		ls_host = &common_host_config[host_index++];

		if (!ls_host->enable)
	    		continue;
        
		err = ls_mci_add_host(ls_host);

		if (err)
	    		mci_err("MCI Err: ls add host %d fail, base: 0x%llx\n", ls_host->index, ls_host->base);
        	/* scan cards below this host */
		mmc_rescan(ls_host->host);
    	}
    	/* mci system init only once */
    	mci_init_flag = 1;
}



void ls_mci_resume(void)
{
       int host_index = 0;
       struct ls_mci_host *ls_host;
       while (host_index < MAX_HOSTS) {
               ls_host = &common_host_config[host_index++];

               if (!ls_host->enable)
                       continue;
               ls_host_hw_init(ls_host);
       }
}





