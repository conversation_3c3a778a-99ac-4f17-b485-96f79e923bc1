#include <pmon.h>
#include <stdlib.h>
#include <stdio.h>
#include <ctype.h>
#include <unistd.h>
#include <string.h>
#include <termio.h>
#include <fcntl.h>
#include <file.h>
#include <ramfile.h>
#include <sys/unistd.h>
#undef _KERNEL
#include <errno.h>
#include <linux/types.h>
#include <pmon/dev/loopdev.h>
#include "./ls_mci.h"
#include "./core.h"


struct mmc_card *cur_op_card = NULL;
extern struct mci_host_manager mci_hosts_m;
extern struct mci_card_manager mci_cards_m;
extern struct mci_card_manager mci_sd_m;
extern struct mci_card_manager mci_mmc_m;
extern int mci_init_flag;

static int  sd_init_n = 0;
static int  mmc_init_n = 0;

static void print_u8(unsigned char *p, int s_pos, int num)
{
    	int i = 0, j = 0, s = 0;
    	int show = 0;
    	int last_zero = 0;
    	int n_all_zero = 0;
        int star_flag = 0;

    	if (num <= 0)
		return;

    	for (i; i < num; i++) {
		if (p[i] != 0)
	    		n_all_zero = 1;

		if ((i+1)%16 == 0 || i == num -1) {   
	    	        if (n_all_zero == 1) {     
				show = 1;
				n_all_zero = 0;
				last_zero = 0;
	    		} else {
				if (last_zero == 1) {    
		    			if (i/16 == (num-1)/16 && i!=num-1) { 
						show = 1;
		    			} else {
						if (star_flag == 0) {
                            				printf("*\n");
			    				star_flag = 1;
						}
						show = 0;
		    			}
				} else {                  
		    			show = 1; 
		    			last_zero = 1;
		    		}
	    		}
	   		if (show == 1) {
		    	        star_flag = 0;
                    		s = i - i%16;
		    		printf("%08x  ", s_pos + s);
		    		for (s; s < i+1; s++)
					printf("%02x ", p[s]);
		    		printf("\n");
	    		}
		}
        }
    	if (star_flag == 1)
		printf("%08x  \n", s_pos + num-1);
}




static int common_open(int fd, const char *fname, int mode, int perms)
{
	int ret = 0;
    	_file[fd].posn = 0;
    	_file[fd].data = 0;
	char type[12];
	int index;
    	mci_dbg("MCI: common_open: %s\n", fname);

	strncpy(type, fname +5, 4);

    	if (!strcmp(type, "emmc")) {
		index = strtol(fname + 9, 0, 0); //get index in mci_mmc_m
		cur_op_card = mci_mmc_m.array[index];

    	} else if (!strcmp(type, "tfca")) {
		index = strtol(fname + 11, 0, 0); //get index in mci_sd_m
		cur_op_card = mci_sd_m.array[index];

    	} else {
		ret = -1; //no dev
	}
    	return ret ? -ENODEV : fd;

}

static off_t common_lseek(int fd, off_t offset, int whence)
{
    	switch (whence) {
        case SEEK_SET:
	        _file[fd].posn = offset;
	    	break;
	case SEEK_CUR:
	    	_file[fd].posn += offset;
	   	break;
	case SEEK_END:
	    	_file[fd].posn =  offset;
	    	break;
	default:
	    	printf("########### EINVAL\n");
	    	errno = EINVAL;
	    	return (-1);
    	}
    	return (_file[fd].posn);
}


static int common_close(int fd)
{
    	return(0);
}




#define MAX_BLOCKS_LIMIT	256
static unsigned char cachedbuffer[512 * 256] __attribute__((aligned(32)));
static int common_read(int fd, void *buf, size_t n)
{
	off_t pos = _file[fd].posn;
    	off_t pos_ori;
        u32 blks_send;
    	int left = n;
    	u32 blk_size;
    	u32 once;
    	unsigned char *p = buf;
    	int err;
    	struct mmc_card *card = cur_op_card;
    	unsigned char *indexbuf = cachedbuffer;

    	if (card == NULL) {
		mci_dbg("MCI: common read: card is null\n");
		return -1;
    	}

    	mci_dbg("MCI: common read: pos: %d, n: %d\n", pos, n);
    	while ( left > 0 ) {
        	blks_send = min((left + DEFAULT_BLKSZ - 1) / DEFAULT_BLKSZ, MAX_BLOCKS_LIMIT);

		err = mmc_rw_simple(card, MMC_DATA_READ, blks_send, (card->state & MMC_STATE_BLOCKADDR)?(pos/DEFAULT_BLKSZ):pos, indexbuf);
		if (err)
	    		return -1;

		once = min(DEFAULT_BLKSZ * blks_send - (pos&(DEFAULT_BLKSZ - 1)), left);
		memcpy(buf, indexbuf + (pos&(DEFAULT_BLKSZ - 1)), once);
	
		buf += once;
		pos += once;
		left -= once;
    	}
    	//print_u8(buf, pos_ori, 512);
    	_file[fd].posn = pos;

    	return (n);
}


static int common_write(int fd, const void *buf, size_t n)
{
    	off_t pos = _file[fd].posn;
    	off_t pos_ori;
    	u32 blks_send;
    	int left = n;
    	u32 blk_size;
    	u32 once;
    	unsigned char *p = buf;
    	int err;
    	struct mmc_card *card = cur_op_card;
    	unsigned char *indexbuf = cachedbuffer;

    	if (card == NULL) {
		mci_dbg("MCI: common write: card is null\n");
		return -1;
    	}
    	mci_dbg("MCI: common read: pos: %d, n: %d\n", pos, n);
    
    	blk_size = DEFAULT_BLKSZ;
    	while (left > 0) {
        	blks_send = min((left + blk_size - 1) / blk_size, MAX_BLOCKS_LIMIT);

		once = min(blk_size * blks_send - (pos&(blk_size-1)), left);
		memcpy(indexbuf + (pos&(blk_size - 1)), buf, once);

		err = mmc_rw_simple(card, MMC_DATA_WRITE, blks_send, (card->state & MMC_STATE_BLOCKADDR)?(pos/blk_size):pos, indexbuf);
		if (err)
	    		return -1;

        	buf += once;
		pos += once;
		left -= once;
    	}
    //print_u8(buf, pos_ori, 512);
    	_file[fd].posn = pos;
    	return (n);
}



static void common_attach(struct device *parent, struct device *self, void *aux)
{
    	struct loopdev_softc *priv = (void *)self;
	char name[] = "/dev/";
	char type[13];

    	mci_dbg("MCI: attach: %s\n", self->dv_xname);

	strncpy(type, self->dv_xname, 4);  //get type
        
	//fix up device name	
    	if (!strcmp(type, "emmc")) {
		self->dv_xname[4] = mmc_init_n - 1 + 48;
    	} else if (!strcmp(type, "tfca")) {
		self->dv_xname[6] = sd_init_n - 1 + 48;
    	} else {
		mci_err("MCI Err: invalid device name: %s\n", self->dv_xname);
		return;     //invalid dev
	}

    	strncpy(priv->dev, strcat(name, self->dv_xname), 63);
    	priv->bs = DEV_BSIZE;
    	priv->seek = 0;
    	priv->count = -1;
    	priv->access = O_RDWR;

#if NGZIP > 0
    	priv->unzip=0;
#endif
}



/******** eMMC dev fs ***************************************************************
*************************************************************************************/
static FileSystem emmcfs =
{
    "emmc", FS_MEM,
    common_open,
    common_read,
    common_write,
    common_lseek,
    common_close,
    NULL
};

static void init_emmc_fs __P((void)) __attribute__ ((constructor));
static void init_emmc_fs()
{
    /* Install ram based file system*/ 
	filefs_init(&emmcfs);
}


static int emmc_match(struct device *parent, void *match, void *aux)
{

   	mci_dbg("MCI: emmc match\n");

   	/* if mci system not inited yet*/
    	if (mci_init_flag == 0)
		ls_mci_probe();
	//check probe success?
	if (mci_mmc_m.num > mmc_init_n ) {
		mmc_init_n ++;
		return 1;
	}
   	return 0;
}



struct cfattach emmc_ca = {
    	sizeof(struct loopdev_softc), emmc_match, common_attach,
};


struct cfdriver emmc_cd = {
    	NULL, "emmc", DV_DISK
};





/******** tfcard dev fs ***************************************************************
*************************************************************************************/

static FileSystem tfcardfs =
{
    	"tfcard", FS_MEM,
    	common_open,
    	common_read,
    	common_write,
    	common_lseek,
    	common_close,
    	NULL
};

static void init_tfcard_fs __P((void)) __attribute__ ((constructor));
static void init_tfcard_fs()
{
    /* Install ram based file system*/ 
    	filefs_init(&tfcardfs);
}


static int sdcard_match(struct device *parent, void *match, void *aux)
{
    	mci_dbg("MCI: sd card match\n");

   	/* if mci system not inited yet*/
    	if (mci_init_flag == 0)
		ls_mci_probe();
	//check probe success?
	if (mci_sd_m.num > sd_init_n ) {
		sd_init_n ++;
		return 1;
	}
    	return 0;
}



struct cfattach tfcard_ca = {
    	sizeof(struct loopdev_softc), sdcard_match, common_attach,
};


struct cfdriver tfcard_cd = {
    	NULL, "tfcard", DV_DISK
};



/******** Pmon Cmds ***************************************************************
*************************************************************************************/

static void card_print_type(struct mmc_card *card)
{
        if (card->type == MMC_TYPE_MMC)
		printf("type: mmc card\n");
	else if (card->type == MMC_TYPE_SD)
		printf("type: sd card\n");
	else if (card->type == MMC_TYPE_SDIO)
		printf("type: sdio card\n");
	else if (card->type == MMC_TYPE_SD_COMBO)
		printf("type: sd combo \n");
	else
		printf("type: Unknown type\n");	
}

static void card_print_raw_cxd(struct mmc_card *card)
{
        int i = 0, j = 0;

    	printf("raw cid: ");
    	for (i; i < 4; i++)
		printf("%08x", card->raw_cid[i]);

    	printf("\n");
    	printf("raw csd: ");

    	for (j; j < 4; j++)
		printf("%08x", card->raw_csd[j]);

    	printf("\n");

    	if (card->type == MMC_TYPE_MMC) {
            	printf("raw ext csd: \n");
            	print_u8(card->raw_ext_csd, 0, 512);
    	}
}



static void card_print_ios_info(struct mmc_card *card)
{
    	struct mmc_host *host = card->host;
    	struct mmc_ios *ios = &host->ios;
    	printf("---ios info----\n");
    	printf("clock: %d Hz\n", ios->clock);
    	printf("bus width: %d\n", 1<<ios->bus_width);

    	if (ios->timing == MMC_TIMING_LEGACY)
 	        printf("timing mode: legacy\n");	
    	else if (ios->timing == MMC_TIMING_MMC_HS)
		printf("timing mode: high speed\n");	
    	else
		printf("timing mode: Unkown\n");
}


static int list_cards()
{
    	int i = 0, cards_n;
    	struct mmc_host *host;
    	cards_n = mci_cards_m.num;

    	if (cards_n == 0){
        	printf("MCI: No valid cards registered\n");
    		return 0;
    	}

    	for (i; i<cards_n; i++){
        	struct mmc_card *card = mci_cards_m.array[i];
		host = card->host;

		printf("********************* Card %d *********************\n", card->index);
		card_print_type(card);

		printf("host: %d\n", host->index);

		printf("ocr:  %08x\n", card->ocr);

		printf("state: %08x\n", card->state);

   		card_print_raw_cxd(card);	
 		card_print_ios_info(card);
		printf("\n");
    	}
	printf("\n");
    	return 0;
}


void mci_rw_data(int argc,char **argv)
{
     	u32 card_index, rw_flag, pos, n, pr_en;
	u8 test_char;
     	u32 blks_send;
     	int left;
     	u32 blk_size;
     	u32 once;
     	int err;
	struct mmc_card *card;

     	unsigned char *indexbuf = cachedbuffer;

	if (argc < 5)
		goto arg_err;

	rw_flag = strtoul(argv[2],0,0);

	if (argc != 6)
		goto arg_err;

	card_index = strtoul(argv[1],0,0);
    	pos = strtoul(argv[3],0,0);
     	n = strtoul(argv[4],0,0);

	if (rw_flag == 1)
		test_char = strtoul(argv[5],0,0);
        else 
                pr_en = strtoul(argv[5],0,0);

        if (card_index >= mci_cards_m.num) {
	        mci_err("MCI Err: no card %d\n", card_index);
	        return;
    	}
     	card = mci_cards_m.array[card_index];
     	blk_size = DEFAULT_BLKSZ; 

     	left = n;
     	while ( left > 0 ) {
        	blks_send = min((left + blk_size - 1) / blk_size, MAX_BLOCKS_LIMIT);
        	if (rw_flag == 0) {  //read
	   		err =  mmc_rw_simple(card, MMC_DATA_READ, blks_send, (card->state & MMC_STATE_BLOCKADDR)?(pos/blk_size):pos, indexbuf);
			if (err)
            			return -1;
            		/* actual read byte */
	    		once = min(blk_size * blks_send - (pos&(blk_size-1)), left);
                        if (pr_en)
    	    		        print_u8(indexbuf + (pos&(blk_size - 1)), pos&(blk_size - 1), once);
		} else {   //write 
	    		once = min(blk_size * blks_send - (pos&(blk_size - 1)), left);
            		memset(indexbuf + (pos&(blk_size - 1)), test_char, once);
	    		err =  mmc_rw_simple(card, MMC_DATA_WRITE, blks_send, (card->state & MMC_STATE_BLOCKADDR)?(pos/blk_size):pos, indexbuf);
			if (err)
            			return -1;
		}	

		pos += once;
		left -= once;
    	}
	return;
arg_err:
	printf("cmd args err\n");
	return;
}

void mci_get_status(int argc,char **argv)
{
     	u32 card_index;
	u32 status;
	int err;
	struct mmc_card *card;

	if (argc != 2)
		goto arg_err;

	card_index = strtoul(argv[1],0,0);
    	

        if (card_index >= mci_cards_m.num) {
	        mci_err("MCI Err: no card %d\n", card_index);
	        return;
    	}
     	card = mci_cards_m.array[card_index];
	err = mmc_send_status(card, &status);
	if (err)
		mci_err("MCI Err: get card status err %d\n", err);
	printf("status: 0x%x\n", status);
     	
	return;
arg_err:
	printf("cmd args err\n");
	return;
}

void mci_dbg_ctrl(int argc,char **argv)
{
     	u32 en;

	if (argc != 2)
		goto arg_err;

	mci_debug = strtoul(argv[1],0,0);
	return;
arg_err:
	printf("cmd args err\n");
	return;
}


static const Cmd Cmds[] =
{
    	{ "MCI Cmds"},
    	{ "mci_cards", "list all detected valid mci cards", 0, "use: mci_cards ", list_cards, 0, 99, CMD_REPEAT},
	{ "mci_status", "get cards status", 0, "use: mci_get_status card_index", mci_get_status, 0, 99, CMD_REPEAT},
    	{ "mci_rw_test", "rw test with data", 0, "use: mci_rw_data card_index rw_flag pos byte_n w:char/r:pr_en", mci_rw_data, 0, 99, CMD_REPEAT},
    	{ "mci_dbg", "control mci dbg", 0, "use: mci_dbg [1/0]", mci_dbg_ctrl, 0, 99, CMD_REPEAT},
    	{ 0,0}
};


static void ls_mci_cmds __P((void)) __attribute__ ((constructor));
static void ls_mci_cmds()
{
    	cmdlist_expand(Cmds, 1);
}



