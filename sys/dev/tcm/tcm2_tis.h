/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Copyright (C) 2011 Infineon Technologies
 *
 * Authors: <AUTHORS>
 *
 * Version: 2.1.1
 *
 * Description:
 * Device driver for TCG/TCPA TCM (trusted platform module).
 * Specifications at www.trustedcomputinggroup.org
 *
 * It is based on the Linux kernel driver tcm.c from <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.
 */

#ifndef _TCM_TIS_I2C_H
#define _TCM_TIS_I2C_H

#include <linux/types.h>
#include <linux/byteorder/little_endian.h>

typedef __u16 __le16;
typedef __u16 __be16;
typedef __u32 __le32;
typedef __u32 __be32;
#define __packed __attribute__((packed, aligned(1)))

enum tis_int_flags {
	TCM_GLOBAL_INT_ENABLE = 0x80000000,
	TCM_INTF_BURST_COUNT_STATIC = 0x100,
	TCM_INTF_CMD_READY_INT = 0x080,
	TCM_INTF_INT_EDGE_FALLING = 0x040,
	TCM_INTF_INT_EDGE_RISING = 0x020,
	TCM_INTF_INT_LEVEL_LOW = 0x010,
	TCM_INTF_INT_LEVEL_HIGH = 0x008,
	TCM_INTF_LOCALITY_CHANGE_INT = 0x004,
	TCM_INTF_STS_VALID_INT = 0x002,
	TCM_INTF_DATA_AVAIL_INT = 0x001,
};

#define TCM_ACCESS(l)                   (0x0000 | ((l) << 12))
#define TCM_INT_ENABLE(l)               (0x0008 | ((l) << 12))
#define TCM_STS(l)                      (0x0018 | ((l) << 12))
#define TCM_DATA_FIFO(l)                (0x0024 | ((l) << 12))
#define TCM_DID_VID(l)                  (0x0f00 | ((l) << 12))
#define TCM_RID(l)                      (0x0f04 | ((l) << 12))
#define TCM_INTF_CAPS(l)                (0x0014 | ((l) << 12))

enum tcm_timeout {
	TCM_TIMEOUT_MS			= 5,
	TIS_SHORT_TIMEOUT_MS		= 750,
	TIS_LONG_TIMEOUT_MS		= 2000,
	SLEEP_DURATION_US		= 60,
	SLEEP_DURATION_LONG_US		= 210,
};

/* Size of external transmit buffer (used in tcm_transmit)*/
#define TCM_BUFSIZE 4096

/* Index of Count field in TCM response buffer */
#define TCM_RSP_SIZE_BYTE	2
#define TCM_RSP_RC_BYTE		6

#define true 1
#define false 0
#define BIT(a) (1 << a)

#define max(x, y) ({                            \
        typeof(x) _max1 = (x);                  \
        typeof(y) _max2 = (y);                  \
        (void) (&_max1 == &_max2);              \
        _max1 > _max2 ? _max1 : _max2; })

#define min_t(type, x, y) ({                    \
        type __min1 = (x);                      \
        type __min2 = (y);                      \
        __min1 < __min2 ? __min1: __min2; })

struct tcm_chip {
	int is_open;
	int locality;
	u32 vend_dev;
	u8 rid;
	unsigned long timeout_a, timeout_b, timeout_c, timeout_d;  /* msec */
	ulong chip_type;
};

struct tcm_input_header {
	__be16 tag;
	__be32 length;
	__be32 ordinal;
} __packed;

struct tcm_output_header {
	__be16 tag;
	__be32 length;
	__be32 return_code;
} __packed;

struct timeout_t {
	__be32 a;
	__be32 b;
	__be32 c;
	__be32 d;
} __packed;

struct duration_t {
	__be32 tcm_short;
	__be32 tcm_medium;
	__be32 tcm_long;
} __packed;

union cap_t {
	struct timeout_t timeout;
	struct duration_t duration;
};

struct tcm_getcap_params_in {
	__be32 cap;
	__be32 subcap_size;
	__be32 subcap;
} __packed;

struct tcm_getcap_params_out {
	__be32 cap_size;
	union cap_t cap;
} __packed;

union tcm_cmd_header {
	struct tcm_input_header in;
	struct tcm_output_header out;
};

union tcm_cmd_params {
	struct tcm_getcap_params_out getcap_out;
	struct tcm_getcap_params_in getcap_in;
};

struct tcm_cmd_t {
	union tcm_cmd_header header;
	union tcm_cmd_params params;
} __packed;

/* Max number of iterations after i2c NAK */
#define MAX_COUNT		3

#ifndef __TCM_V2_H
/*
 * Max number of iterations after i2c NAK for 'long' commands
 *
 * We need this especially for sending TCM_READY, since the cleanup after the
 * transtion to the ready state may take some time, but it is unpredictable
 * how long it will take.
 */
#define MAX_COUNT_LONG		50

enum tis_access {
	TCM_ACCESS_VALID		= 0x80,
	TCM_ACCESS_ACTIVE_LOCALITY	= 0x20,
	TCM_ACCESS_REQUEST_PENDING	= 0x04,
	TCM_ACCESS_REQUEST_USE		= 0x02,
};

enum tis_status {
	TCM_STS_VALID			= 0x80,
	TCM_STS_COMMAND_READY		= 0x40,
	TCM_STS_GO			= 0x20,
	TCM_STS_DATA_AVAIL		= 0x10,
	TCM_STS_DATA_EXPECT		= 0x08,
};
#endif

/**
 * tcm_tis_open - Open the device and request locality 0
 *
 * @dev:  TCM device
 *
 * @return: 0 on success, negative on failure
 */
int tcm_tis_open(void);
/**
 * tcm_tis_close - Close the device and release locality
 *
 *
 * @return: 0 on success, negative on failure
 */
int tcm_tis_close();
/** tcm_tis_cleanup - Get the device in ready state and release locality
 *
 *
 * @return: always 0
 */
int tcm_tis_cleanup();
/**
 * tcm_tis_send - send data to the device
 *
 * @buf:  buffer to send
 * @len:  size of the buffer
 *
 * @return: number of bytes sent or negative on failure
 */
int tcm_tis_send(const u8 *buf, size_t len);
/**
 * tcm_tis_recv_data - Receive data from a device. Wrapper for tcm_tis_recv
 *
 * @buf:  buffer to copy data
 * @size: buffer size
 *
 * @return: bytes read or negative on failure
 */
int tcm_tis_recv(u8 *buf, size_t count);
/**
 * tcm_tis_get_desc - Get the TCM description
 *
 * @buf:  buffer to fill data
 * @size: buffer size
 *
 * @return: Number of characters written (or would have been written) in buffer
 */
int tcm_tis_get_desc(char *buf, int size);
/**
 * tcm_tis_init - inititalize the device
 *
 *
 * @return: 0 on success, negative on failure
 */
int tcm_tis_init();

int tcm_xfer(const uint8_t *sendbuf, size_t send_size,uint8_t *recvbuf, size_t *recv_size);

#endif
