/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * drivers/usb/gadget/dwc2_udc.h
 * Designware DWC2 on-chip full/high speed USB device controllers
 * Copyright (C) 2005 for Samsung Electronics
 */

#ifndef __DWC2_USB_GADGET
#define __DWC2_USB_GADGET

#define PHY0_SLEEP              (1 << 5)

struct dwc2_plat_otg_data {
	void		*priv;
	int		phy_of_node;
	int		(*phy_control)(int on);
	unsigned long	regs_phy;
	unsigned long	regs_otg;
	unsigned int    usb_phy_ctrl;
	unsigned int    usb_flags;
	unsigned int	usb_gusbcfg;
	unsigned int	rx_fifo_sz;
	unsigned int	np_tx_fifo_sz;
	unsigned int	tx_fifo_sz;
};

int dwc2_udc_probe(struct dwc2_plat_otg_data *pdata, void *buf);
char *myself_malloc(unsigned int size);

#define udelay delay

#define	CONFIG_SYS_CACHELINE_SIZE	64
#define ROUND(x, y) (((x) + (y) - 1) & ~((y) - 1))

#define unlikely(x) (x)
#define likely(x) (x)

#define SYNC_R  0
#define SYNC_W  1
#define flush_dcache_range(start, stop) pci_sync_cache(NULL, start, stop - start + 1, SYNC_W)
#define invalidate_dcache_range(start, stop) pci_sync_cache(NULL, start, stop - start + 1, SYNC_R)

#define printk	printf
//#define printf	tgt_printf
#define printf

#define	spin_lock_irqsave(...)	{ }
#define spin_unlock_irqrestore(...) { }

#define debug_cond(c, fmt...) if(c) printf(fmt)
#define debug(fmt...)  printf(fmt)

#define le16_to_cpu(x)  (x)
#define cpu_to_le16(x)	(x)
#define virt_to_bus(p) ((long)(p)&0x1fffffff)

#endif	/* __DWC2_USB_GADGET */
