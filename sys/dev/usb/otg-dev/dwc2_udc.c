#include <sys/param.h>
#include <sys/systm.h>
#include <sys/mbuf.h>
#include <sys/malloc.h>
#include <sys/kernel.h>
#include <vm/vm.h>
#include <machine/cpu.h>
#include <machine/bus.h>
#include <machine/intr.h>

#include <dev/pci/pcivar.h>
#include <dev/pci/pcireg.h>
#include <dev/pci/pcidevs.h>
#include <sys/device.h>

#include "gadget.h"
#include "dwc2_udc_otg_regs.h"
#include "dwc2_udc_otg_priv.h"

#include "dwc2_udc.h"
#include <pmon.h>

static struct dwc2_plat_otg_data bcm_otg_data = {
	.regs_otg       = (int)0xbfe00000,
	.usb_gusbcfg = 0x40002700,
	.rx_fifo_sz = 533,
	.np_tx_fifo_sz = 256,
	.tx_fifo_sz = 533,
};

int usb_gadget_handle_interrupts(int index);
static int otg_irq_handler(void *data)
{
	usb_gadget_handle_interrupts(0);
	return 0;
}

int pcd_init(unsigned long base, unsigned int irq, unsigned usb_gusbcfg)
{
	char *p = NULL;

	p = malloc(8);
	if (!p) {
		printf("No memory available for UDC!\n");
		return -ENOMEM;
	}

	bcm_otg_data.regs_otg = base;
	bcm_otg_data.usb_gusbcfg = usb_gusbcfg;
	pci_intr_establish(0, 0, IPL_BIO, otg_irq_handler, 0, 0);
	dwc2_udc_probe(&bcm_otg_data, p);

	return 0;
}

struct usb_request *dwc2_alloc_request(struct usb_ep *ep, int gfp_flags)
{
	struct dwc2_request *req;


	req = malloc(sizeof(*req));
	if (!req)
		return 0;

	memset(req, 0, sizeof *req);
	INIT_LIST_HEAD(&req->queue);

	debug("%s: %s %p req:%p-%p\n", __func__, ep->name, ep, req, &req->req);
	return &req->req;
}

