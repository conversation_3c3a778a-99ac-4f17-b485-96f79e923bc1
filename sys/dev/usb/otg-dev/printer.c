/*
 * printer.c -- Printer gadget driver
 *
 * Copyright (C) 2003-2005 <PERSON>
 * Copyright (C) 2006 <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 */


//#define DEBUG 1
#define VERBOSE
#define CONFIG_USB_GADGET_DUALSPEED
#include <stdio.h>
#include "ch9.h"
#include "usbdescriptors.h"
#include "gadget.h"

#define PRINTER_NOT_ERROR	0x08
#define PRINTER_SELECTED	0x10
#define PRINTER_PAPER_EMPTY	0x20
#include "gadget_chips.h"
#include "dwc2_udc.h"
#include <dev/pci/pcivar.h>
#include <dev/pci/pcireg.h>
#include <dev/pci/pcidevs.h>
#include <sys/device.h>
#include <sys/ioctl.h>
#include <pmon.h>

#include <unistd.h>
#include <file.h>
#include <termio.h>
#include <stdarg.h>

/*-------------------------------------------------------------------------*/

#define DRIVER_DESC		"Printer Gadget"
#define DRIVER_VERSION		"2007 OCT 06"

static const char shortname [] = "printer";
static const char driver_desc [] = DRIVER_DESC;

/*-------------------------------------------------------------------------*/

struct printer_dev {
	struct usb_gadget	*gadget;
	struct usb_request	*req;		/* for control responses */
	u8			config;
	s8			interface;
	struct usb_ep		*in_ep, *out_ep;
	const struct usb_endpoint_descriptor
				*in, *out;
	struct list_head	rx_reqs;	/* List of free RX structs */
	struct list_head	rx_reqs_active;	/* List of Active RX xfers */
	struct list_head	rx_buffers;	/* List of completed xfers */
	/* wait until there is data to be read. */
	struct list_head	tx_reqs;	/* List of free TX structs */
	struct list_head	tx_reqs_active; /* List of Active TX xfers */
	struct usb_request	*current_rx_req;
	/* Wait until there are write buffers available to use. */
	/* Wait until all write buffers have been sent. */
	u8			printer_status;
	u8	 		reset_printer;
	size_t                  current_rx_bytes;
	u8                      *current_rx_buf;
};

static struct printer_dev usb_printer_gadget;

/*-------------------------------------------------------------------------*/

/* DO NOT REUSE THESE IDs with a protocol-incompatible driver!!  Ever!!
 * Instead:  allocate your own, using normal USB-IF procedures.
 */

/* Thanks to NetChip Technologies for donating this product ID.
 */
#define PRINTER_VENDOR_NUM	0x0525		/* NetChip */
#define PRINTER_PRODUCT_NUM	0xa4a8		/* Linux-USB Printer Gadget */

/* Some systems will want different product identifers published in the
 * device descriptor, either numbers or strings or both.  These string
 * parameters are in UTF-8 (superset of ASCII's 7 bit characters).
 */

static ushort  idVendor;
static ushort  idProduct;
static ushort  bcdDevice;
static char * iManufacturer;
static char * iProduct;
static char * iSerialNum;
static char * iPNPstring;

/* Number of requests to allocate per endpoint, not used for ep0. */
static unsigned qlen = 4;
#define QLEN	qlen

#ifdef CONFIG_USB_GADGET_DUALSPEED
#define DEVSPEED	USB_SPEED_HIGH
#else   /* full speed (low speed doesn't do bulk) */
#define DEVSPEED        USB_SPEED_FULL
#endif

/*-------------------------------------------------------------------------*/

#ifdef	DEBUG
#define printk	tgt_printf
#else
#define printk	{ }
#endif

#define xprintk(d, level, fmt, args...) \
	printk(level "%s: " fmt, DRIVER_DESC, ## args)

#ifdef DEBUG
#define DBG(dev, fmt, args...) \
	xprintk(dev, KERN_DEBUG, fmt, ## args)
#else
#define DBG(dev, fmt, args...) \
	do { } while (0)
#endif /* DEBUG */

#ifdef VERBOSE
#define VDBG(dev, fmt, args...) \
	xprintk(dev, KERN_DEBUG, fmt, ## args)
#else
#define VDBG(dev, fmt, args...) \
	do { } while (0)
#endif /* VERBOSE */

#define ERROR(dev, fmt, args...) \
	xprintk(dev, KERN_ERR, fmt, ## args)
#define WARNING(dev, fmt, args...) \
	xprintk(dev, KERN_WARNING, fmt, ## args)
#define INFO(dev, fmt, args...) \
	xprintk(dev, KERN_INFO, fmt, ## args)

/*-------------------------------------------------------------------------*/

/* USB DRIVER HOOKUP (to the hardware driver, below us), mostly
 * ep0 implementation:  descriptors, config management, setup().
 * also optional class-specific notification interrupt transfer.
 */

/*
 * DESCRIPTORS ... most are static, but strings and (full) configuration
 * descriptors are built on demand.
 */

#define STRING_MANUFACTURER		1
#define STRING_PRODUCT			2
#define STRING_SERIALNUM		3

/* holds our biggest descriptor */
#define USB_DESC_BUFSIZE		256
#define USB_BUFSIZE			512

/* This device advertises one configuration. */
#define DEV_CONFIG_VALUE		1
#define	PRINTER_INTERFACE		0

static struct usb_device_descriptor device_desc = {
	.bLength =		sizeof device_desc,
	.bDescriptorType =	USB_DT_DEVICE,
	.bcdUSB =		cpu_to_le16(0x0200),
	.bDeviceClass =		USB_CLASS_PER_INTERFACE,
	.bDeviceSubClass =	0,
	.bDeviceProtocol =	0,
	.idVendor =		cpu_to_le16(PRINTER_VENDOR_NUM),
	.idProduct =		cpu_to_le16(PRINTER_PRODUCT_NUM),
	.iManufacturer =	STRING_MANUFACTURER,
	.iProduct =		STRING_PRODUCT,
	.iSerialNumber =	STRING_SERIALNUM,
	.bNumConfigurations =	1
};

static struct usb_otg_descriptor otg_desc = {
	.bLength =		sizeof otg_desc,
	.bDescriptorType =	USB_DT_OTG,
	.bmAttributes =		USB_OTG_SRP
};

static struct usb_config_descriptor config_desc = {
	.bLength =		sizeof config_desc,
	.bDescriptorType =	USB_DT_CONFIG,

	/* compute wTotalLength on the fly */
	.bNumInterfaces =	1,
	.bConfigurationValue =	DEV_CONFIG_VALUE,
	.iConfiguration =	0,
	.bmAttributes =		USB_CONFIG_ATT_ONE | USB_CONFIG_ATT_SELFPOWER,
	.bMaxPower =		0,
};

static struct usb_interface_descriptor intf_desc = {
	.bLength =		sizeof intf_desc,
	.bDescriptorType =	USB_DT_INTERFACE,
	.bInterfaceNumber =	PRINTER_INTERFACE,
	.bNumEndpoints =	2,
	.bInterfaceClass =	USB_CLASS_PRINTER,
	.bInterfaceSubClass =	1,	/* Printer Sub-Class */
	.bInterfaceProtocol =	2,	/* Bi-Directional */
	.iInterface =		0
};

static struct usb_endpoint_descriptor fs_ep_in_desc = {
	.bLength =		USB_DT_ENDPOINT_SIZE,
	.bDescriptorType =	USB_DT_ENDPOINT,
	.bEndpointAddress =	USB_DIR_IN,
	.bmAttributes =		USB_ENDPOINT_XFER_BULK
};

static struct usb_endpoint_descriptor fs_ep_out_desc = {
	.bLength =		USB_DT_ENDPOINT_SIZE,
	.bDescriptorType =	USB_DT_ENDPOINT,
	.bEndpointAddress =	USB_DIR_OUT,
	.bmAttributes =		USB_ENDPOINT_XFER_BULK
};

static const struct usb_descriptor_header *fs_printer_function [11] = {
	(struct usb_descriptor_header *) &otg_desc,
	(struct usb_descriptor_header *) &intf_desc,
	(struct usb_descriptor_header *) &fs_ep_in_desc,
	(struct usb_descriptor_header *) &fs_ep_out_desc,
	NULL
};

#ifdef	CONFIG_USB_GADGET_DUALSPEED

/*
 * usb 2.0 devices need to expose both high speed and full speed
 * descriptors, unless they only run at full speed.
 */

static struct usb_endpoint_descriptor hs_ep_in_desc = {
	.bLength =		USB_DT_ENDPOINT_SIZE,
	.bDescriptorType =	USB_DT_ENDPOINT,
	.bmAttributes =		USB_ENDPOINT_XFER_BULK,
	.wMaxPacketSize =	cpu_to_le16(512)
};

static struct usb_endpoint_descriptor hs_ep_out_desc = {
	.bLength =		USB_DT_ENDPOINT_SIZE,
	.bDescriptorType =	USB_DT_ENDPOINT,
	.bmAttributes =		USB_ENDPOINT_XFER_BULK,
	.wMaxPacketSize =	cpu_to_le16(512)
};

static struct usb_qualifier_descriptor dev_qualifier = {
	.bLength =		sizeof dev_qualifier,
	.bDescriptorType =	USB_DT_DEVICE_QUALIFIER,
	.bcdUSB =		cpu_to_le16(0x0200),
	.bDeviceClass =		USB_CLASS_PRINTER,
	.bNumConfigurations =	1
};

static const struct usb_descriptor_header *hs_printer_function [11] = {
	(struct usb_descriptor_header *) &otg_desc,
	(struct usb_descriptor_header *) &intf_desc,
	(struct usb_descriptor_header *) &hs_ep_in_desc,
	(struct usb_descriptor_header *) &hs_ep_out_desc,
	NULL
};

/* maxpacket and other transfer characteristics vary by speed. */
#define ep_desc(g, hs, fs) (((g)->speed == USB_SPEED_HIGH)?(hs):(fs))

#else

/* if there's no high speed support, maxpacket doesn't change. */
#define ep_desc(g, hs, fs) (((void)(g)), (fs))

#endif	/* !CONFIG_USB_GADGET_DUALSPEED */

/*-------------------------------------------------------------------------*/

/* descriptors that are built on-demand */

static char				manufacturer [50] = " Printer";
static char				product_desc [40] = DRIVER_DESC;
static char				serial_num [40] = "1";
static char				pnp_string [1024] =
	"XXMFG:linux;MDL:g_printer;CLS:PRINTER;SN:1;";

/* static strings, in UTF-8 */
static struct usb_string		strings [] = {
	{ STRING_MANUFACTURER,	manufacturer, },
	{ STRING_PRODUCT,	product_desc, },
	{ STRING_SERIALNUM,	serial_num, },
	{  }		/* end of list */
};

static struct usb_gadget_strings	stringtab = {
	.language	= 0x0409,	/* en-us */
	.strings	= strings,
};

/*-------------------------------------------------------------------------*/

typedef struct {
#define PTR_FILE_BLOCK_SIZE		(1024 * 1024)			/* File block size: 1MB */
#define PTR_FILE_DATA_MAX		(PTR_FILE_BLOCK_SIZE * 32)	/* Max load file size: 32MB */
	char		*data[PTR_FILE_DATA_MAX / PTR_FILE_BLOCK_SIZE];
#define _ptr_data(file, pos)		(&((file)->data[(pos) / PTR_FILE_BLOCK_SIZE][(pos) % PTR_FILE_BLOCK_SIZE]))

	size_t		data_len;
#define _ptr_block_num(file)		((file)->data_len / PTR_FILE_BLOCK_SIZE)
	size_t		file_len;
} file_data_t;

static file_data_t ptr_file_data;

typedef enum {
	WAIT_TRANSFER = 1,
	START_TRANSFER
} transfer_status_t;

/*-------------------------------------------------------------------------*/

static struct usb_request *
printer_req_alloc(struct usb_ep *ep, unsigned len, gfp_t gfp_flags)
{
	struct usb_request	*req;

	req = usb_ep_alloc_request(ep, gfp_flags);

	if (req != NULL) {
		req->length = len;
		req->buf = malloc(len);
		if (req->buf == NULL) {
			usb_ep_free_request(ep, req);
			return NULL;
		}
	}

	return req;
}

static void
printer_req_free(struct usb_ep *ep, struct usb_request *req)
{
	if (ep != NULL && req != NULL) {
		free(req->buf);
		usb_ep_free_request(ep, req);
	}
}

/*-------------------------------------------------------------------------*/

static void setup_rx_reqs(struct printer_dev *dev);
static int set_printer_interface(struct printer_dev *dev);

int printer_read_data(char *buf, int len)
{
	struct printer_dev		*dev = &usb_printer_gadget;
	unsigned long			flags;
	size_t				size;
	size_t				bytes_copied;
	struct usb_request		*req;
	/* This is a pointer to the current USB rx request. */
	struct usb_request		*current_rx_req;
	/* This is the number of bytes in the current rx buffer. */
	size_t				current_rx_bytes;
	/* This is a pointer to the current rx buffer. */
	u8				*current_rx_buf;

	if (len == 0)
		return -EINVAL;

	DBG(dev, "printer_read trying to read %d bytes\n", (int)len);

	/* We will use this flag later to check if a printer reset happened
	 * after we turn interrupts back on.
	 */
	dev->reset_printer = 0;
	dev->printer_status |= PRINTER_SELECTED;

	setup_rx_reqs(dev);

	bytes_copied = 0;
	current_rx_req = dev->current_rx_req;
	current_rx_bytes = dev->current_rx_bytes;
	current_rx_buf = dev->current_rx_buf;
	dev->current_rx_req = NULL;
	dev->current_rx_bytes = 0;
	dev->current_rx_buf = NULL;

	/* Check if there is any data in the read buffers. Please note that
	 * current_rx_bytes is the number of bytes in the current rx buffer.
	 * If it is zero then check if there are any other rx_buffers that
	 * are on the completed list. We are only out of data if all rx
	 * buffers are empty.
	 */
	if ((current_rx_bytes == 0) &&
			(likely(list_empty(&dev->rx_buffers)))) {

		return -1;
	}

	/* We have data to return then copy it to the caller's buffer.*/
	while ((current_rx_bytes || likely(!list_empty(&dev->rx_buffers))) && len) {
		if (current_rx_bytes == 0) {
			req = container_of(dev->rx_buffers.next,
					struct usb_request, list);
			list_del_init(&req->list);

			if (req->actual && req->buf) {
				current_rx_req = req;
				current_rx_bytes = req->actual;
				current_rx_buf = req->buf;
			} else {
				list_add(&req->list, &dev->rx_reqs);
				continue;
			}
		}

		if (len > current_rx_bytes)
			size = current_rx_bytes;
		else
			size = len;

		memcpy(buf, current_rx_buf, size);
		bytes_copied += size;
		len -= size;
		buf += size;

		/* We've disconnected or reset so return. */
		if (dev->reset_printer) {
			list_add(&current_rx_req->list, &dev->rx_reqs);
			return -EAGAIN;
		}

		/* If we not returning all the data left in this RX request
		 * buffer then adjust the amount of data left in the buffer.
		 * Othewise if we are done with this RX request buffer then
		 * requeue it to get any incoming data from the USB host.
		 */
		if (size < current_rx_bytes) {
			current_rx_bytes -= size;
			current_rx_buf += size;
		} else {
			list_add(&current_rx_req->list, &dev->rx_reqs);
			current_rx_bytes = 0;
			current_rx_buf = NULL;
			current_rx_req = NULL;
		}
	}

	dev->current_rx_req = current_rx_req;
	dev->current_rx_bytes = current_rx_bytes;
	dev->current_rx_buf = current_rx_buf;

	DBG(dev, "printer_read returned %d bytes\n", (int)bytes_copied);

	if (bytes_copied)
		return bytes_copied;
	else
		return -EAGAIN;
}

static void rx_complete(struct usb_ep *ep, struct usb_request *req)
{
	struct printer_dev	*dev = ep->driver_data;
	int			status = req->status;
	unsigned long		flags;

	spin_lock_irqsave(&dev->lock, flags);

	list_del_init(&req->list);	/* Remode from Active List */

	switch (status) {

	/* normal completion */
	case 0:
		if (req->actual > 0) {
			list_add_tail(&req->list, &dev->rx_buffers);
		} else {
			list_add(&req->list, &dev->rx_reqs);
		}

		break;
	/* software-driven interface shutdown */
	case -ECONNRESET:		/* unlink */
	case -ESHUTDOWN:		/* disconnect etc */
		VDBG(dev, "rx shutdown, code %d\n", status);
		list_add(&req->list, &dev->rx_reqs);
		break;

	/* for hardware automagic (such as pxa) */
	case -ECONNABORTED:		/* endpoint reset */
		DBG(dev, "rx %s reset\n", ep->name);
		list_add(&req->list, &dev->rx_reqs);
		break;

	/* data overrun */
	case -EOVERFLOW:
		/* FALLTHROUGH */

	default:
		DBG(dev, "rx status %d\n", status);
		list_add(&req->list, &dev->rx_reqs);
		break;
	}

}

static void tx_complete(struct usb_ep *ep, struct usb_request *req)
{
	struct printer_dev	*dev = ep->driver_data;

	switch (req->status) {
	default:
		VDBG(dev, "tx err %d\n", req->status);
		/* FALLTHROUGH */
	case -ECONNRESET:		/* unlink */
	case -ESHUTDOWN:		/* disconnect etc */
		break;
	case 0:
		break;
	}

	/* Take the request struct off the active list and put it on the
	 * free list.
	 */
	list_del_init(&req->list);
	list_add(&req->list, &dev->tx_reqs);
}

static void
setup_rx_reqs(struct printer_dev *dev)
{
	struct usb_request              *req;

	while (likely(!list_empty(&dev->rx_reqs))) {
		int error;

		req = container_of(dev->rx_reqs.next,
				struct usb_request, list);
		list_del_init(&req->list);

		/* The USB Host sends us whatever amount of data it wants to
		 * so we always set the length field to the full USB_BUFSIZE.
		 * If the amount of data is more than the read() caller asked
		 * for it will be stored in the request buffer until it is
		 * asked for by read().
		 */
		req->length = USB_BUFSIZE;
		req->complete = rx_complete;

		error = usb_ep_queue(dev->out_ep, req, GFP_ATOMIC);
		if (error) {
			DBG(dev, "rx submit --> %d\n", error);
			list_add(&req->list, &dev->rx_reqs);
			break;
		} else {
			list_add(&req->list, &dev->rx_reqs_active);
		}
	}
}


static int
set_printer_interface(struct printer_dev *dev)
{
	int result = 0;

	dev->in = ep_desc(dev->gadget, &hs_ep_in_desc, &fs_ep_in_desc);
	dev->in_ep->driver_data = dev;

	dev->out = ep_desc(dev->gadget, &hs_ep_out_desc, &fs_ep_out_desc);
	dev->out_ep->driver_data = dev;

	result = usb_ep_enable(dev->in_ep, dev->in);
	if (result != 0) {
		DBG(dev, "enable %s --> %d\n", dev->in_ep->name, result);
		goto done;
	}

	result = usb_ep_enable(dev->out_ep, dev->out);
	if (result != 0) {
		DBG(dev, "enable %s --> %d\n", dev->in_ep->name, result);
		goto done;
	}

done:
	/* on error, disable any endpoints  */
	if (result != 0) {
		(void) usb_ep_disable(dev->in_ep);
		(void) usb_ep_disable(dev->out_ep);
		dev->in = NULL;
		dev->out = NULL;
	}
	/* caller is responsible for cleanup on error */
	return result;
}

static void printer_reset_interface(struct printer_dev *dev)
{
	if (dev->interface < 0)
		return;

	DBG(dev, "%s\n", __func__);

	if (dev->in)
		usb_ep_disable(dev->in_ep);

	if (dev->out)
		usb_ep_disable(dev->out_ep);

	dev->interface = -1;
}

/* change our operational config.  must agree with the code
 * that returns config descriptors, and altsetting code.
 */
static int
printer_set_config(struct printer_dev *dev, unsigned number)
{
	int			result = 0;
	struct usb_gadget	*gadget = dev->gadget;

	if (gadget_is_sa1100(gadget) && dev->config) {
		/* tx fifo is full, but we can't clear it...*/
		INFO(dev, "can't change configurations\n");
		return -ESPIPE;
	}

	switch (number) {
	case DEV_CONFIG_VALUE:
		result = 0;
		break;
	default:
		result = -EINVAL;
		/* FALL THROUGH */
	case 0:
		break;
	}

	if (result) {
		usb_gadget_vbus_draw(dev->gadget,
				dev->gadget->is_otg ? 8 : 100);
	} else {
		char *speed;
		unsigned power;

		power = 2 * config_desc.bMaxPower;
		usb_gadget_vbus_draw(dev->gadget, power);

		switch (gadget->speed) {
		case USB_SPEED_FULL:	speed = "full"; break;
#ifdef CONFIG_USB_GADGET_DUALSPEED
		case USB_SPEED_HIGH:	speed = "high"; break;
#endif
		default:		speed = "?"; break;
		}

		dev->config = number;
		INFO(dev, "%s speed config #%d: %d mA, %s\n",
				speed, number, power, driver_desc);
	}

	set_printer_interface(dev);

	return result;
}

static int
config_buf(enum usb_device_speed speed, u8 *buf, u8 type, unsigned index,
		int is_otg)
{
	int					len;
	const struct usb_descriptor_header	**function;
#ifdef CONFIG_USB_GADGET_DUALSPEED
	int					hs = (speed == USB_SPEED_HIGH);

	if (type == USB_DT_OTHER_SPEED_CONFIG)
		hs = !hs;

	if (hs) {
		function = hs_printer_function;
	} else {
		function = fs_printer_function;
	}
#else
	function = fs_printer_function;
#endif

	if (index >= device_desc.bNumConfigurations)
		return -EINVAL;

	/* for now, don't advertise srp-only devices */
	if (!is_otg)
		function++;

	len = usb_gadget_config_buf(&config_desc, buf, USB_DESC_BUFSIZE,
			function);
	if (len < 0)
		return len;
	((struct usb_config_descriptor *) buf)->bDescriptorType = type;
	return len;
}

/* Change our operational Interface. */
static int
set_interface(struct printer_dev *dev, unsigned number)
{
	int			result = 0;

	if (gadget_is_sa1100(dev->gadget) && dev->interface < 0) {
		/* tx fifo is full, but we can't clear it...*/
		INFO(dev, "can't change interfaces\n");
		return -ESPIPE;
	}

	/* Free the current interface */
	printer_reset_interface(dev);

	switch (number) {
	case PRINTER_INTERFACE:
		result = set_printer_interface(dev);
		if (result) {
			printer_reset_interface(dev);
		} else {
			dev->interface = PRINTER_INTERFACE;
		}
		break;
	default:
		result = -EINVAL;
		/* FALL THROUGH */
	}

	if (!result)
		INFO(dev, "Using interface %x\n", number);

	return result;
}

static void printer_setup_complete(struct usb_ep *ep, struct usb_request *req)
{
	if (req->status || req->actual != req->length)
		DBG((struct printer_dev *) ep->driver_data,
				"setup complete --> %d, %d/%d\n",
				req->status, req->actual, req->length);
}

static void printer_soft_reset(struct printer_dev *dev)
{
	struct usb_request	*req;

	INFO(dev, "Received Printer Reset Request\n");

	if (usb_ep_disable(dev->in_ep))
		DBG(dev, "Failed to disable USB in_ep\n");
	if (usb_ep_disable(dev->out_ep))
		DBG(dev, "Failed to disable USB out_ep\n");


	while (likely(!(list_empty(&dev->rx_buffers)))) {
		req = container_of(dev->rx_buffers.next, struct usb_request,
				list);
		list_del_init(&req->list);
		list_add(&req->list, &dev->rx_reqs);
	}

	while (likely(!(list_empty(&dev->rx_reqs_active)))) {
		req = container_of(dev->rx_buffers.next, struct usb_request,
				list);
		list_del_init(&req->list);
		list_add(&req->list, &dev->rx_reqs);
	}

	while (likely(!(list_empty(&dev->tx_reqs_active)))) {
		req = container_of(dev->tx_reqs_active.next,
				struct usb_request, list);
		list_del_init(&req->list);
		list_add(&req->list, &dev->tx_reqs);
	}

	if (usb_ep_enable(dev->in_ep, dev->in))
		DBG(dev, "Failed to enable USB in_ep\n");
	if (usb_ep_enable(dev->out_ep, dev->out))
		DBG(dev, "Failed to enable USB out_ep\n");

}

/*-------------------------------------------------------------------------*/

/*
 * The setup() callback implements all the ep0 functionality that's not
 * handled lower down.
 */
static int
printer_setup(struct usb_gadget *gadget, const struct usb_ctrlrequest *ctrl)
{
	struct printer_dev	*dev = get_gadget_data(gadget);
	struct usb_request	*req = dev->req;
	int			value = -EOPNOTSUPP;
	u16			wIndex = le16_to_cpu(ctrl->wIndex);
	u16			wValue = le16_to_cpu(ctrl->wValue);
	u16			wLength = le16_to_cpu(ctrl->wLength);

	DBG(dev, "ctrl req%02x.%02x v%04x i%04x l%d\n",
		ctrl->bRequestType, ctrl->bRequest, wValue, wIndex, wLength);

	req->complete = printer_setup_complete;

	switch (ctrl->bRequestType&USB_TYPE_MASK) {

	case USB_TYPE_STANDARD:
		switch (ctrl->bRequest) {

		case USB_REQ_GET_DESCRIPTOR:
			if (ctrl->bRequestType != USB_DIR_IN)
				break;
			switch (wValue >> 8) {

			case USB_DT_DEVICE:
				value = min(wLength, (u16) sizeof device_desc);
				memcpy(req->buf, &device_desc, value);
				break;
#ifdef CONFIG_USB_GADGET_DUALSPEED
			case USB_DT_DEVICE_QUALIFIER:
				if (!gadget->is_dualspeed)
					break;
				value = min(wLength,
						(u16) sizeof dev_qualifier);
				memcpy(req->buf, &dev_qualifier, value);
				break;

			case USB_DT_OTHER_SPEED_CONFIG:
				if (!gadget->is_dualspeed)
					break;
				/* FALLTHROUGH */
#endif /* CONFIG_USB_GADGET_DUALSPEED */
			case USB_DT_CONFIG:
				value = config_buf(gadget->speed, req->buf,
						wValue >> 8,
						wValue & 0xff,
						gadget->is_otg);
				if (value >= 0)
					value = min(wLength, (u16) value);
				break;

			case USB_DT_STRING:
				value = usb_gadget_get_string(&stringtab,
						wValue & 0xff, req->buf);
				if (value >= 0)
					value = min(wLength, (u16) value);
				break;
			}
			break;

		case USB_REQ_SET_CONFIGURATION:
			if (ctrl->bRequestType != 0)
				break;
			if (gadget->a_hnp_support)
				DBG(dev, "HNP available\n");
			else if (gadget->a_alt_hnp_support)
				DBG(dev, "HNP needs a different root port\n");
			value = printer_set_config(dev, wValue);
			break;
		case USB_REQ_GET_CONFIGURATION:
			if (ctrl->bRequestType != USB_DIR_IN)
				break;
			*(u8 *)req->buf = dev->config;
			value = min(wLength, (u16) 1);
			break;

		case USB_REQ_SET_INTERFACE:
			if (ctrl->bRequestType != USB_RECIP_INTERFACE ||
					!dev->config)
				break;

			value = set_interface(dev, PRINTER_INTERFACE);
			break;
		case USB_REQ_GET_INTERFACE:
			if (ctrl->bRequestType !=
					(USB_DIR_IN|USB_RECIP_INTERFACE)
					|| !dev->config)
				break;

			*(u8 *)req->buf = dev->interface;
			value = min(wLength, (u16) 1);
			break;

		default:
			goto unknown;
		}
		break;

	case USB_TYPE_CLASS:
		switch (ctrl->bRequest) {
		case 0: /* Get the IEEE-1284 PNP String */
			/* Only one printer interface is supported. */
			if ((wIndex>>8) != PRINTER_INTERFACE)
				break;

			value = (pnp_string[0]<<8)|pnp_string[1];
			memcpy(req->buf, pnp_string, value);
			DBG(dev, "1284 PNP String: %x %s\n", value,
					&pnp_string[2]);
			break;

		case 1: /* Get Port Status */
			DBG(dev, "read status wIndex=%d printer_status=%d", wIndex, dev->printer_status);
			/* Only one printer interface is supported. */
			if (wIndex != PRINTER_INTERFACE)
				break;

			*(u8 *)req->buf = dev->printer_status;
			value = min(wLength, (u16) 1);
			break;

		case 2: /* Soft Reset */
			/* Only one printer interface is supported. */
			if (wIndex != PRINTER_INTERFACE)
				break;

			printer_soft_reset(dev);

			value = 0;
			break;

		default:
			goto unknown;
		}
		break;

	default:
unknown:
		VDBG(dev,
			"unknown ctrl req%02x.%02x v%04x i%04x l%d\n",
			ctrl->bRequestType, ctrl->bRequest,
			wValue, wIndex, wLength);
		break;
	}

	/* respond with data transfer before status phase? */
	if (value >= 0) {
		req->length = value;
		req->zero = value < wLength;
		value = usb_ep_queue(gadget->ep0, req, GFP_ATOMIC);
		if (value < 0) {
			DBG(dev, "ep_queue --> %d\n", value);
			req->status = 0;
			printer_setup_complete(gadget->ep0, req);
		}
	}

	/* host either stalls (value < 0) or reports success */
	return value;
}

static void
printer_disconnect(struct usb_gadget *gadget)
{
	struct printer_dev	*dev = get_gadget_data(gadget);
	unsigned long		flags;

	DBG(dev, "%s\n", __func__);

	spin_lock_irqsave(&dev->lock, flags);

	printer_reset_interface(dev);

	spin_unlock_irqrestore(&dev->lock, flags);
}

static void
printer_unbind(struct usb_gadget *gadget)
{
	struct printer_dev	*dev = get_gadget_data(gadget);
	struct usb_request	*req;


	DBG(dev, "%s\n", __func__);

	/* Free all memory for this driver. */
	while (!list_empty(&dev->tx_reqs)) {
		req = container_of(dev->tx_reqs.next, struct usb_request,
				list);
		list_del(&req->list);
		printer_req_free(dev->in_ep, req);
	}

	if (dev->current_rx_req != NULL)
		printer_req_free(dev->out_ep, dev->current_rx_req);

	while (!list_empty(&dev->rx_reqs)) {
		req = container_of(dev->rx_reqs.next,
				struct usb_request, list);
		list_del(&req->list);
		printer_req_free(dev->out_ep, req);
	}

	while (!list_empty(&dev->rx_buffers)) {
		req = container_of(dev->rx_buffers.next,
				struct usb_request, list);
		list_del(&req->list);
		printer_req_free(dev->out_ep, req);
	}

	if (dev->req) {
		printer_req_free(gadget->ep0, dev->req);
		dev->req = NULL;
	}

	set_gadget_data(gadget, NULL);
}

static int
printer_bind(struct usb_gadget *gadget)
{
	struct printer_dev	*dev;
	struct usb_ep		*in_ep, *out_ep;
	int			status = -ENOMEM;
	int			gcnum;
	size_t			len;
	u32			i;
	struct usb_request	*req;

	dev = &usb_printer_gadget;

	gcnum = usb_gadget_controller_number(gadget);
	if (gcnum >= 0) {
		device_desc.bcdDevice = cpu_to_le16(0x0200 + gcnum);
	} else {
		dev_warn(&gadget->dev, "controller '%s' not recognized\n",
			gadget->name);
		/* unrecognized, but safe unless bulk is REALLY quirky */
		device_desc.bcdDevice =
			cpu_to_le16(0xFFFF);
	}

	device_desc.idVendor =
		cpu_to_le16(PRINTER_VENDOR_NUM);
	device_desc.idProduct =
		cpu_to_le16(PRINTER_PRODUCT_NUM);

	/* support optional vendor/distro customization */
	if (idVendor) {
		if (!idProduct) {
			printf("idVendor needs idProduct!\n");
			return -ENODEV;
		}
		device_desc.idVendor = cpu_to_le16(idVendor);
		device_desc.idProduct = cpu_to_le16(idProduct);
		if (bcdDevice)
			device_desc.bcdDevice = cpu_to_le16(bcdDevice);
	}

	if (iManufacturer)
		strlcpy(manufacturer, iManufacturer, sizeof manufacturer);

	if (iProduct)
		strlcpy(product_desc, iProduct, sizeof product_desc);

	if (iSerialNum)
		strlcpy(serial_num, iSerialNum, sizeof serial_num);

	if (iPNPstring)
		strlcpy(&pnp_string[2], iPNPstring, (sizeof pnp_string)-2);

	len = strlen(pnp_string);
	pnp_string[0] = (len >> 8) & 0xFF;
	pnp_string[1] = len & 0xFF;

	/* all we really need is bulk IN/OUT */
	usb_ep_autoconfig_reset(gadget);
	in_ep = usb_ep_autoconfig(gadget, &fs_ep_in_desc);
	if (!in_ep) {
autoconf_fail:
		printf("can't autoconfigure on %s\n", gadget->name);
		return -ENODEV;
	}
	in_ep->driver_data = in_ep;	/* claim */

	out_ep = usb_ep_autoconfig(gadget, &fs_ep_out_desc);
	if (!out_ep)
		goto autoconf_fail;
	out_ep->driver_data = out_ep;	/* claim */

#ifdef	CONFIG_USB_GADGET_DUALSPEED
	/* assumes ep0 uses the same value for both speeds ... */
	dev_qualifier.bMaxPacketSize0 = device_desc.bMaxPacketSize0;

	/* and that all endpoints are dual-speed */
	hs_ep_in_desc.bEndpointAddress = fs_ep_in_desc.bEndpointAddress;
	hs_ep_out_desc.bEndpointAddress = fs_ep_out_desc.bEndpointAddress;
#endif	/* DUALSPEED */

	device_desc.bMaxPacketSize0 = gadget->ep0->maxpacket;
	usb_gadget_set_selfpowered(gadget);

	if (gadget->is_otg) {
		otg_desc.bmAttributes |= USB_OTG_HNP,
		config_desc.bmAttributes |= USB_CONFIG_ATT_WAKEUP;
	}

	INIT_LIST_HEAD(&dev->tx_reqs);
	INIT_LIST_HEAD(&dev->rx_reqs);
	INIT_LIST_HEAD(&dev->rx_reqs_active);
	INIT_LIST_HEAD(&dev->tx_reqs_active);
	INIT_LIST_HEAD(&dev->rx_buffers);
	dev->config = 0;
	dev->interface = -1;
	dev->printer_status = PRINTER_NOT_ERROR;
	dev->current_rx_req = NULL;

	dev->in_ep = in_ep;
	dev->out_ep = out_ep;

	/* preallocate control message data and buffer */
	dev->req = printer_req_alloc(gadget->ep0, USB_DESC_BUFSIZE,
			GFP_KERNEL);
	if (!dev->req) {
		status = -ENOMEM;
		goto fail;
	}

	for (i = 0; i < QLEN; i++) {
		req = printer_req_alloc(dev->in_ep, USB_BUFSIZE, GFP_KERNEL);
		if (!req) {
			while (!list_empty(&dev->tx_reqs)) {
				req = container_of(dev->tx_reqs.next,
						struct usb_request, list);
				list_del(&req->list);
				printer_req_free(dev->in_ep, req);
			}
			return -ENOMEM;
		}
		list_add(&req->list, &dev->tx_reqs);
	}

	for (i = 0; i < QLEN; i++) {
		req = printer_req_alloc(dev->out_ep, USB_BUFSIZE, GFP_KERNEL);
		if (!req) {
			while (!list_empty(&dev->rx_reqs)) {
				req = container_of(dev->rx_reqs.next,
						struct usb_request, list);
				list_del(&req->list);
				printer_req_free(dev->out_ep, req);
			}
			return -ENOMEM;
		}
		list_add(&req->list, &dev->rx_reqs);
	}

	dev->req->complete = printer_setup_complete;

	/* finish hookup to lower layer ... */
	dev->gadget = gadget;
	set_gadget_data(gadget, dev);
	gadget->ep0->driver_data = dev;

	INFO(dev, "%s, version: " DRIVER_VERSION "\n", driver_desc);
	INFO(dev, "using %s, OUT %s IN %s\n", gadget->name, out_ep->name,
			in_ep->name);
	return 0;

fail:
	printer_unbind(gadget);
	return status;
}

#define TIMEOUT			(100)		/* Time out(ms), used to determine the end of transmission */
#define PTR_RX_LEN		(512)		/* The maximum length of data for each transmission */
static int printer_load(file_data_t *file_data)
{
	transfer_status_t status	= WAIT_TRANSFER;
	void *block			= NULL;
	int len				= -1;
	int time_base;
	int cnt;

	memset(file_data, 0, sizeof(file_data_t));

	tgt_printf("\rWaiting for transmission, press any key to exit.\n");
	while (1) {
		usb_gadget_handle_interrupts(0);

		if (file_data->data_len < (file_data->file_len + PTR_RX_LEN)) {
			if (file_data->data_len + PTR_FILE_BLOCK_SIZE > PTR_FILE_DATA_MAX) {
				tgt_printf("Error: file too large(max %d MB).\n",
					PTR_FILE_DATA_MAX / PTR_FILE_BLOCK_SIZE);
				return (-1);
			}

			block = malloc(PTR_FILE_BLOCK_SIZE);
			if (block) {
				file_data->data[_ptr_block_num(file_data)] = block;
				file_data->data_len += PTR_FILE_BLOCK_SIZE;
			} else {
				tgt_printf("Failed to allocate memory\n");
				return (-1);
			}
		}

		len = printer_read_data(_ptr_data(file_data, file_data->file_len), PTR_RX_LEN);
		if (status == WAIT_TRANSFER) {
			if (len <= 0) {
				ioctl(STDIN, FIONREAD, &cnt);
				if (cnt) {
					getchar();
					return (-1);
				}
			} else {
				status = START_TRANSFER;
			}
		}
		
		if (status == START_TRANSFER) {
			if (len > 0) {
				file_data->file_len += len;

				if (get_timer(0) / 200 != time_base / 200)
					tgt_printf("Loaded %d bytes\r", file_data->file_len);
				time_base = get_timer(0);
			} else {
				if (get_timer(time_base) > TIMEOUT) {
					tgt_printf("\33[2K");
					break;
				}
			}
		}
	}

	return (0);
}

static int printer_open(int fd, const char *path, int flags, int mode)
{
	char *path_suffix = NULL;

	int i, ret = 0;

	for (i = 0; path[i]; i++)
		if (path[i] == '.' && path[i + 1])
			path_suffix = &path[i + 1];

	_file[fd].data = NULL;
	if (path_suffix && !strncmp(path_suffix, "elf", 3)) {
		ret = printer_load(&ptr_file_data);
		if (ret)
			return (-1);

		_file[fd].data = (void *)&ptr_file_data;
	}

	return (fd);
}

static int printer_close(int fd)
{
	File *ptr_file		= NULL;
	file_data_t *file_data	= NULL;

	int i;

	if (fd < 0) {
		return (-1);
	}

	ptr_file  = &_file[fd];
	file_data = (file_data_t *)ptr_file->data;

	if (file_data) {
		for (i = 0; i < PTR_FILE_DATA_MAX / PTR_FILE_BLOCK_SIZE; i++)
			if (file_data->data[i])
				free(file_data->data[i]);
	}

	memset(ptr_file, 0, sizeof(File));

	return (0);
}

static int printer_read(int fd, void *buf, size_t n)
{
	static transfer_status_t status	= 0;
	static int time_base		= 0;
	File *ptr_file			= NULL;
	file_data_t *file_data		= NULL;
	size_t block_pos		= 0;
	size_t ret_len			= 0;
	int len				= 0;
	int cnt;

	if (fd < 0) {
		return (-1);
	}

	if (_file[fd].data) {
		ptr_file  = &_file[fd];
		file_data = (file_data_t *)ptr_file->data;

		if (n <= 0 || ptr_file->posn >= file_data->file_len) {
			return (0);
		} else {
			n = (file_data->file_len - ptr_file->posn) < n ? (file_data->file_len - ptr_file->posn) : n;
		}

		while (ret_len < n) {
			block_pos = ptr_file->posn % PTR_FILE_BLOCK_SIZE;
			len = (PTR_FILE_BLOCK_SIZE - block_pos) > (n - ret_len) ? (n - ret_len) : (PTR_FILE_BLOCK_SIZE - block_pos);
			memcpy(&((char *)buf)[ret_len], _ptr_data(file_data, ptr_file->posn), len);
			ptr_file->posn += len;
			ret_len += len;
		}
	} else {
		if (!status) {
			if (get_timer(time_base) < 3000) {
				status = START_TRANSFER;
			} else {
				tgt_printf("\rWaiting for transmission, press any key to exit.\n");
				status = WAIT_TRANSFER;
			}
			time_base = get_timer(0);
		}

		while (ret_len < n) {
			usb_gadget_handle_interrupts(0);
			len = printer_read_data(&buf[ret_len], n - ret_len);
			if (status == WAIT_TRANSFER) {
				if (len <= 0) {
					ioctl(STDIN, FIONREAD, &cnt);
					if (cnt) {
						getchar();
						status = 0;
						return (0);
					}
				} else {
					status = START_TRANSFER;
				}
			}

			if (status == START_TRANSFER) {
				if (len > 0) {
					ret_len += len;

					time_base = get_timer(0);
				} else {
					if (get_timer(time_base) > TIMEOUT) {
						status = 0;
						break;
					}
				}
			}
		}
	}

	return (ret_len);
}

static off_t printer_lseek(int fd, off_t offset, int whence)
{
	File *ptr_file		= NULL;
	file_data_t *file_data	= NULL;
	off_t new_offset	= 0;

	if (fd < 0 || _file[fd].data == NULL) {
		return (-1);
	}

	ptr_file  = &_file[fd];
	file_data = (file_data_t *)ptr_file->data;

	switch (whence) {
	case SEEK_SET:
		new_offset = offset;
		break;
	case SEEK_CUR:
		new_offset = ptr_file->posn + offset;
		break;
	case SEEK_END:
		new_offset = file_data->file_len + offset;
		break;
	default:
		return -1;
	}

	if (new_offset < 0 || new_offset > file_data->file_len) {
		return (-1);
	}

	ptr_file->posn = new_offset;

	return new_offset;
}

/*-------------------------------------------------------------------------*/
static FileSystem printerfs = {
	.devname = "printer",
	.fstype  = FS_PRINTER,
	.open    = printer_open,
	.read    = printer_read,
	.write   = NULL,
	.lseek   = printer_lseek,
	.close   = printer_close,
	.ioctl   = NULL,
};

static struct usb_gadget_driver printer_driver = {
	.speed		= DEVSPEED,

	.function	= (char *) driver_desc,
	.bind		= printer_bind,
	.unbind		= printer_unbind,

	.setup		= printer_setup,
	.disconnect	= printer_disconnect,

};

int printer_init(void)
{
	static int flag = 0;

	if (flag)
		return (0);
	else
		flag = 1;

	filefs_init(&printerfs);

	return usb_gadget_register_driver(&printer_driver);
}
