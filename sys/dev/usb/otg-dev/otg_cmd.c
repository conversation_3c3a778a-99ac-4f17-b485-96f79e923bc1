#include <pmon.h>
#include <stdlib.h>
#include <stdio.h>
#include <ctype.h>
#include <unistd.h>
#include <string.h>
#include <termio.h>
#include <fcntl.h>
#include <file.h>
#include <ctype.h>
#include <ramfile.h>
#include <sys/unistd.h>
#include <stdlib.h>
#include <errno.h>
#include <linux/types.h>
#include "../dwc2.h"

extern int pcd_init(unsigned long base, unsigned int irq, unsigned usb_gusbcfg);
extern int printer_init(void);

static int otg_start(void)
{
	if (!gdwc2 || !gdwc2->priv || !gdwc2->priv->regs) {
		tgt_printf("execution error, otg register base address unknown\n");
		return (-1);
	}

	pcd_init((unsigned long)gdwc2->priv->regs, 0, 0x40002407);
	printer_init();

	return 0;
}

static const Cmd otg_cmds[] =
{
	{"OTG"},
	{"sotg","NULL", NULL,"start otg device", otg_start, 0, 2, CMD_REPEAT},
	{0, 0}
};
static void init_cmd __P((void)) __attribute__ ((constructor));

static void init_cmd()
{
	cmdlist_expand(otg_cmds, 1);
}

