// SPDX-License-Identifier: GPL-2.0+
/*
 * USB HOST XHCI Controller stack
 *
 * Based on xHCI host controller driver in linux-kernel
 * by <PERSON>.
 *
 * Copyright (C) 2008 Intel Corp.
 * Author: <PERSON>
 *
 * Copyright (C) 2013 Samsung Electronics Co.Ltd
 * Authors: <AUTHORS>
 *	    <PERSON><PERSON><PERSON> <<EMAIL>>
 */

#include <byteorder.h>
#include "usb.h"
#include <linux/errno.h>

#include <sys/buf.h>
#include "xhci.h"

/**
 * Free event node
 *
 * @param event_node The pointer to the event_node structure to be released
 */
void xhci_free_event_node(struct xhci_event_node **event_node)
{
	if (event_node && *event_node) {
		free(*event_node);
		*event_node = NULL;
	}
}

/**
 * Add pending events to the waiting linked list
 *
 * @param event_node	pending event struct
 * @return error code
 */
static int xhci_add_event_to_wait_list(struct xhci_event_node *event_node)
{
	if (!event_node && !event_node->ctrl && !event_node->udev) {
		BUG();
		return -EINVAL;
	}

	list_add(&event_node->node, &event_node->ctrl->xhci_event_list);

	return (0);
}

/**
 * Delete processing events from the waiting linked list.
 *
 * @param ctrl	has handled the event struct
 * @return error code
 */
static int xhci_del_event_from_wait_list(struct xhci_event_node *event_node)
{
	if (!event_node) {
		BUG();
		return -EINVAL;
	}

	list_del(&event_node->node);

	return (0);
}

/**
 * Checks if there is a new event to handle on the event ring.
 *
 * @param ctrl	host controller data structure
 * @return 0 if failure else 1 on success
 */
int event_ready(struct xhci_ctrl *ctrl)
{
	union xhci_trb *event;

	xhci_inval_cache((uintptr_t)ctrl->event_ring->dequeue,
			 sizeof(union xhci_trb));

	event = ctrl->event_ring->dequeue;

	/* Does the HC or OS own the TRB? */
	if ((le32_to_cpu(event->event_cmd.flags) & TRB_CYCLE) !=
		ctrl->event_ring->cycle_state)
		return 0;

	return 1;
}

/**
 * Try to get an event and traverse all nodes in the waiting list.
 *
 * @param ctrl		pointer of controller structure to traverse
 * @param type		expected event types to handle
 * 				__XHCI_BLOCK_TYPE: only handle blocking type events
 * 				__XHCI_NON_BLOCK_TYPE: only handle non blocking type events
 * 				__XHCI_ALL_TYPE: handle all types of events
 * @return none
 */
void xhci_traverse_event_node(struct xhci_ctrl *ctrl, int type)
{
	struct xhci_event_node *current_event_node, *event_node;
	unsigned int current_time = get_timer(0);
	union xhci_trb *event = ctrl->event_ring->dequeue;
	unsigned int field = 0;

	if (!event_ready(ctrl)) {					/* No events, handle timeout node */
		list_for_each_entry(event_node, &ctrl->xhci_event_list, node) {
			if (type && event_node->type != type)
				continue;

			if (event_node->time_out > current_time)
				continue;

			event_node->state = __XHCI_EVENT_TIMEOUT;

			/* Delete node from waiting list */
			current_event_node = event_node;
			event_node = (struct xhci_event_node *)event_node->node.prev;
			xhci_del_event_from_wait_list(current_event_node);

			if (current_event_node->func)
				current_event_node->func(current_event_node, current_event_node->arg);
		}
	} else {							/* Got an event */
		field = le32_to_cpu(event->trans_event.flags);
		list_for_each_entry(event_node, &ctrl->xhci_event_list, node) {
			if (type && event_node->type != type)
				continue;

			if ((uintptr_t)(le64_to_cpu(event->trans_event.buffer)) !=
			    (uintptr_t)vtophys(event_node->trb_addr))
				continue;

			event_node->event = event;
			event_node->state = __XHCI_EVENT_REACH;

			/* Delete node from waiting list */
			xhci_del_event_from_wait_list(event_node);

			if (event_node->func)
				event_node->func(event_node, event_node->arg);

			return;
		}

		/* 
		 * If not all types of events are expected to be processed,
		 * do not delete the events to prevent erroneous deletion of
		 * events from other devices
		 */
		if (type != __XHCI_ALL_TYPE)
			return;

		if (TRB_FIELD_TO_TYPE(field) == TRB_PORT_STATUS) {
			BUG_ON(GET_COMP_CODE(le32_to_cpu(event->generic.field[2])) != COMP_SUCCESS);
		} else {
			printf("Unexpected XHCI event TRB, skipping... "
					"(%08x %08x %08x %08x)\n",
					le32_to_cpu(event->generic.field[0]),
					le32_to_cpu(event->generic.field[1]),
					le32_to_cpu(event->generic.field[2]),
					le32_to_cpu(event->generic.field[3]));
		}

		xhci_acknowledge_event(ctrl);
	}
}

/**
 * Wait for an event to complete based on the trb address.
 *
 * @param udev		equipment structure to which the event belongs
 * @param pipe		contains the DIR_IN or OUT , devnum
 * @param buffer	buffer to be read/written based on the request(callback function can be NULL)
 * @param length	length of the buffer(callback function can be 0)
 * @param trb_addr	trb address(required parameter is used to match the event node)
 * @param type		Waiting type of event node
 * 				__XHCI_BLOCK_TYPE: block waiting for the event to arrive or timeout
 * 				__XHCI_NON_BLOCK_TYPE: only add no waiting
 * @param time_out	timeout for waiting for events
 * @param func		callback function for event timeout or successful matching(can be empty)
 * @param arg		used to transfer private data to callback functions
 * @return event node pointer
 */
static struct xhci_event_node *__xhci_base_trbaddr_wait_event(struct usb_device *udev,
							      unsigned long pipe,
							      void *buffer,
							      int length,
							      void *trb_addr,
							      int type,
							      unsigned int time_out,
							      event_callback_func_t func,
							      void *arg)
{
	struct xhci_event_node *event_node = NULL;

	event_node = (struct xhci_event_node *)malloc(sizeof(struct xhci_event_node));
	if (!event_node || !udev || !trb_addr) {
		BUG();
		return NULL;
	}

	memset(event_node, 0, sizeof(struct xhci_event_node));
	event_node->ctrl     = xhci_get_ctrl(udev);
	event_node->udev     = udev;
	event_node->pipe     = pipe;
	event_node->buffer   = buffer;
	event_node->length   = length;
	event_node->type     = type;
	event_node->time_out = get_timer(0) + time_out;
	event_node->state    = __XHCI_EVENT_WAITING;
	event_node->func     = func;
	event_node->trb_addr = trb_addr;
	event_node->arg      = arg;

	xhci_add_event_to_wait_list(event_node);

	while ((__XHCI_BLOCK_TYPE == type) && (event_node->state == __XHCI_EVENT_WAITING))
		xhci_traverse_event_node(event_node->ctrl, __XHCI_ALL_TYPE);

	return (event_node);
}

/**
 * Add non blocking wait event and wait for completion.
 *
 * @param udev		equipment structure to which the event belongs
 * @param pipe		contains the DIR_IN or OUT , devnum
 * @param buffer	buffer to be read/written based on the request(callback function can be NULL)
 * @param length	length of the buffer(callback function can be 0)
 * @param trb_addr	trb address(required parameter is used to match the event node)
 * @param time_out	timeout for waiting for events
 * @param func		callback function for event timeout or successful matching(can be empty)
 * @param arg		used to transfer private data to callback functions
 * @return event node pointer
 */
struct xhci_event_node *xhci_block_wait_event(struct usb_device *udev,
					      unsigned long pipe,
					      void *buffer,
					      int length,
					      void *trb_addr,
					      unsigned int time_out,
					      event_callback_func_t func,
					      void *arg)
{
	return __xhci_base_trbaddr_wait_event(udev, pipe, buffer, length, trb_addr,
					      __XHCI_BLOCK_TYPE, time_out, func, arg);
}

/**
 * Add non blocking waiting events.
 *
 * @param udev		equipment structure to which the event belongs
 * @param pipe		contains the DIR_IN or OUT , devnum
 * @param buffer	buffer to be read/written based on the request(callback function can be NULL)
 * @param length	length of the buffer(callback function can be 0)
 * @param trb_addr	trb address(required parameter is used to match the event node)
 * @param time_out	timeout for waiting for events
 * @param func		callback function for event timeout or successful matching(can be empty)
 * @param arg		used to transfer private data to callback functions
 * @return event node pointer
 */
struct xhci_event_node *xhci_non_block_wait_event(struct usb_device *udev,
						  unsigned long pipe,
						  void *buffer,
						  int length,
						  void *trb_addr,
						  unsigned int time_out,
						  event_callback_func_t func,
						  void *arg)
{
	return __xhci_base_trbaddr_wait_event(udev, pipe, buffer, length, trb_addr,
					      __XHCI_NON_BLOCK_TYPE, time_out, func, arg);
}

/**
 * Is this TRB a link TRB or was the last TRB the last TRB in this event ring
 * segment?  I.e. would the updated event TRB pointer step off the end of the
 * event seg ?
 *
 * @param ctrl	Host controller data structure
 * @param ring	pointer to the ring
 * @param seg	poniter to the segment to which TRB belongs
 * @param trb	poniter to the ring trb
 * @return 1 if this TRB a link TRB else 0
 */
static int last_trb(struct xhci_ctrl *ctrl, struct xhci_ring *ring,
			struct xhci_segment *seg, union xhci_trb *trb)
{
	if (ring == ctrl->event_ring)
		return trb == &seg->trbs[TRBS_PER_SEGMENT];
	else
		return TRB_TYPE_LINK_LE32(trb->link.control);
}

/**
 * Does this link TRB point to the first segment in a ring,
 * or was the previous TRB the last TRB on the last segment in the ERST?
 *
 * @param ctrl	Host controller data structure
 * @param ring	pointer to the ring
 * @param seg	poniter to the segment to which TRB belongs
 * @param trb	poniter to the ring trb
 * @return 1 if this TRB is the last TRB on the last segment else 0
 */
static bool last_trb_on_last_seg(struct xhci_ctrl *ctrl,
				 struct xhci_ring *ring,
				 struct xhci_segment *seg,
				 union xhci_trb *trb)
{
	if (ring == ctrl->event_ring)
		return ((trb == &seg->trbs[TRBS_PER_SEGMENT]) &&
			(seg->next == ring->first_seg));
	else
		return le32_to_cpu(trb->link.control) & LINK_TOGGLE;
}

/**
 * See Cycle bit rules. SW is the consumer for the event ring only.
 * Don't make a ring full of link TRBs.  That would be dumb and this would loop.
 *
 * If we've just enqueued a TRB that is in the middle of a TD (meaning the
 * chain bit is set), then set the chain bit in all the following link TRBs.
 * If we've enqueued the last TRB in a TD, make sure the following link TRBs
 * have their chain bit cleared (so that each Link TRB is a separate TD).
 *
 * Section ******* of the 0.95 spec says link TRBs cannot have the chain bit
 * set, but other sections talk about dealing with the chain bit set.  This was
 * fixed in the 0.96 specification errata, but we have to assume that all 0.95
 * xHCI hardware can't handle the chain bit being cleared on a link TRB.
 *
 * @param ctrl	Host controller data structure
 * @param ring	pointer to the ring
 * @param more_trbs_coming	flag to indicate whether more trbs
 *				are expected or NOT.
 *				Will you enqueue more TRBs before calling
 *				prepare_ring()?
 * @return none
 */
static void inc_enq(struct xhci_ctrl *ctrl, struct xhci_ring *ring,
						bool more_trbs_coming)
{
	u32 chain;
	union xhci_trb *next;

	chain = le32_to_cpu(ring->enqueue->generic.field[3]) & TRB_CHAIN;
	next = ++(ring->enqueue);

	/*
	 * Update the dequeue pointer further if that was a link TRB or we're at
	 * the end of an event ring segment (which doesn't have link TRBS)
	 */
	while (last_trb(ctrl, ring, ring->enq_seg, next)) {
		if (ring != ctrl->event_ring) {
			/*
			 * If the caller doesn't plan on enqueueing more
			 * TDs before ringing the doorbell, then we
			 * don't want to give the link TRB to the
			 * hardware just yet.  We'll give the link TRB
			 * back in prepare_ring() just before we enqueue
			 * the TD at the top of the ring.
			 */
			if (!chain && !more_trbs_coming)
				break;

			/*
			 * If we're not dealing with 0.95 hardware or
			 * isoc rings on AMD 0.96 host,
			 * carry over the chain bit of the previous TRB
			 * (which may mean the chain bit is cleared).
			 */
			next->link.control &= cpu_to_le32(~TRB_CHAIN);
			next->link.control |= cpu_to_le32(chain);

			next->link.control ^= cpu_to_le32(TRB_CYCLE);
			xhci_flush_cache((uintptr_t)next,
					 sizeof(union xhci_trb));
		}
		/* Toggle the cycle bit after the last ring segment. */
		if (last_trb_on_last_seg(ctrl, ring,
					ring->enq_seg, next))
			ring->cycle_state = (ring->cycle_state ? 0 : 1);

		ring->enq_seg = ring->enq_seg->next;
		ring->enqueue = ring->enq_seg->trbs;
		next = ring->enqueue;
	}
}

/**
 * See Cycle bit rules. SW is the consumer for the event ring only.
 * Don't make a ring full of link TRBs.  That would be dumb and this would loop.
 *
 * @param ctrl	Host controller data structure
 * @param ring	Ring whose Dequeue TRB pointer needs to be incremented.
 * return none
 */
static void inc_deq(struct xhci_ctrl *ctrl, struct xhci_ring *ring)
{
	do {
		/*
		 * Update the dequeue pointer further if that was a link TRB or
		 * we're at the end of an event ring segment (which doesn't have
		 * link TRBS)
		 */
		if (last_trb(ctrl, ring, ring->deq_seg, ring->dequeue)) {
			if (ring == ctrl->event_ring &&
					last_trb_on_last_seg(ctrl, ring,
						ring->deq_seg, ring->dequeue)) {
				ring->cycle_state = (ring->cycle_state ? 0 : 1);
			}
			ring->deq_seg = ring->deq_seg->next;
			ring->dequeue = ring->deq_seg->trbs;
		} else {
			ring->dequeue++;
		}
	} while (last_trb(ctrl, ring, ring->deq_seg, ring->dequeue));
}

/**
 * Generic function for queueing a TRB on a ring.
 * The caller must have checked to make sure there's room on the ring.
 *
 * @param	more_trbs_coming:   Will you enqueue more TRBs before calling
 *				prepare_ring()?
 * @param ctrl	Host controller data structure
 * @param ring	pointer to the ring
 * @param more_trbs_coming	flag to indicate whether more trbs
 * @param trb_fields	pointer to trb field array containing TRB contents
 * @return pointer to the enqueued trb
 */
static struct xhci_generic_trb *queue_trb(struct xhci_ctrl *ctrl,
					  struct xhci_ring *ring,
					  bool more_trbs_coming,
					  unsigned int *trb_fields)
{
	struct xhci_generic_trb *trb;
	int i;

	trb = &ring->enqueue->generic;

	for (i = 0; i < 4; i++)
		trb->field[i] = cpu_to_le32(trb_fields[i]);

	xhci_flush_cache((uintptr_t)trb, sizeof(struct xhci_generic_trb));

	inc_enq(ctrl, ring, more_trbs_coming);

	return trb;
}

/**
 * Does various checks on the endpoint ring, and makes it ready
 * to queue num_trbs.
 *
 * @param ctrl		Host controller data structure
 * @param ep_ring	pointer to the EP Transfer Ring
 * @param ep_state	State of the End Point
 * @return error code in case of invalid ep_state, 0 on success
 */
static int prepare_ring(struct xhci_ctrl *ctrl, struct xhci_ring *ep_ring,
							u32 ep_state)
{
	union xhci_trb *next = ep_ring->enqueue;

	/* Make sure the endpoint has been added to xHC schedule */
	switch (ep_state) {
	case EP_STATE_DISABLED:
		/*
		 * USB core changed config/interfaces without notifying us,
		 * or hardware is reporting the wrong state.
		 */
		puts("WARN urb submitted to disabled ep\n");
		return -ENOENT;
	case EP_STATE_ERROR:
		puts("WARN waiting for error on ep to be cleared\n");
		return -EINVAL;
	case EP_STATE_HALTED:
		puts("WARN endpoint is halted\n");
		return -EINVAL;
	case EP_STATE_STOPPED:
	case EP_STATE_RUNNING:
		debug("EP STATE RUNNING.\n");
		break;
	default:
		puts("ERROR unknown endpoint state for ep\n");
		return -EINVAL;
	}

	while (last_trb(ctrl, ep_ring, ep_ring->enq_seg, next)) {
		/*
		 * If we're not dealing with 0.95 hardware or isoc rings
		 * on AMD 0.96 host, clear the chain bit.
		 */
		next->link.control &= cpu_to_le32(~TRB_CHAIN);

		next->link.control ^= cpu_to_le32(TRB_CYCLE);

		xhci_flush_cache((uintptr_t)next, sizeof(union xhci_trb));

		/* Toggle the cycle bit after the last ring segment. */
		if (last_trb_on_last_seg(ctrl, ep_ring,
					ep_ring->enq_seg, next))
			ep_ring->cycle_state = (ep_ring->cycle_state ? 0 : 1);
		ep_ring->enq_seg = ep_ring->enq_seg->next;
		ep_ring->enqueue = ep_ring->enq_seg->trbs;
		next = ep_ring->enqueue;
	}

	return 0;
}

/**
 * Generic function for queueing a command TRB on the command ring.
 * Check to make sure there's room on the command ring for one command TRB.
 *
 * @param ctrl		Host controller data structure
 * @param ptr		Pointer address to write in the first two fields (opt.)
 * @param slot_id	Slot ID to encode in the flags field (opt.)
 * @param ep_index	Endpoint index to encode in the flags field (opt.)
 * @param cmd		Command type to enqueue
 * @return trb address
 */
union xhci_trb *xhci_queue_command(struct xhci_ctrl *ctrl, u8 *ptr, u32 slot_id,
					    u32 ep_index, trb_type cmd)
{
	u32 fields[4];
	u64 val_64 = vtophys((uintptr_t)ptr);
	union xhci_trb *trb_addr;

	BUG_ON(prepare_ring(ctrl, ctrl->cmd_ring, EP_STATE_RUNNING));

	fields[0] = lower_32_bits(val_64);
	fields[1] = upper_32_bits(val_64);
	fields[2] = 0;
	fields[3] = TRB_TYPE(cmd) | SLOT_ID_FOR_TRB(slot_id) |
		    ctrl->cmd_ring->cycle_state;

	/*
	 * Only 'reset endpoint', 'stop endpoint' and 'set TR dequeue pointer'
	 * commands need endpoint id encoded.
	 */
	if (cmd >= TRB_RESET_EP && cmd <= TRB_SET_DEQ)
		fields[3] |= EP_ID_FOR_TRB(ep_index);

	trb_addr = queue_trb(ctrl, ctrl->cmd_ring, false, fields);

	/* Ring the command ring doorbell */
	xhci_writel(&ctrl->dba->doorbell[0], DB_VALUE_HOST);

	return (trb_addr);
}

/**
 * The TD size is the number of bytes remaining in the TD (including this TRB),
 * right shifted by 10.
 * It must fit in bits 21:17, so it can't be bigger than 31.
 *
 * @param remainder	remaining packets to be sent
 * @return remainder if remainder is less than max else max
 */
static u32 xhci_td_remainder(unsigned int remainder)
{
	u32 max = (1 << (21 - 17 + 1)) - 1;

	if ((remainder >> 10) >= max)
		return max << 17;
	else
		return (remainder >> 10) << 17;
}

/**
 * Finds out the remanining packets to be sent
 *
 * @param running_total	total size sent so far
 * @param trb_buff_len	length of the TRB Buffer
 * @param total_packet_count	total packet count
 * @param maxpacketsize		max packet size of current pipe
 * @param num_trbs_left		number of TRBs left to be processed
 * @return 0 if running_total or trb_buff_len is 0, else remainder
 */
static u32 xhci_v1_0_td_remainder(int running_total,
				int trb_buff_len,
				unsigned int total_packet_count,
				int maxpacketsize,
				unsigned int num_trbs_left)
{
	int packets_transferred;

	/* One TRB with a zero-length data packet. */
	if (num_trbs_left == 0 || (running_total == 0 && trb_buff_len == 0))
		return 0;

	/*
	 * All the TRB queueing functions don't count the current TRB in
	 * running_total.
	 */
	packets_transferred = (running_total + trb_buff_len) / maxpacketsize;

	if ((total_packet_count - packets_transferred) > 31)
		return 31 << 17;
	return (total_packet_count - packets_transferred) << 17;
}

/**
 * Ring the doorbell of the End Point
 *
 * @param udev		pointer to the USB device structure
 * @param ep_index	index of the endpoint
 * @param start_cycle	cycle flag of the first TRB
 * @param start_trb	pionter to the first TRB
 * @return none
 */
static void giveback_first_trb(struct usb_device *udev, int ep_index,
				int start_cycle,
				struct xhci_generic_trb *start_trb)
{
	struct xhci_ctrl *ctrl = xhci_get_ctrl(udev);

	/*
	 * Pass all the TRBs to the hardware at once and make sure this write
	 * isn't reordered.
	 */
	if (start_cycle)
		start_trb->field[3] |= cpu_to_le32(start_cycle);
	else
		start_trb->field[3] &= cpu_to_le32(~TRB_CYCLE);

	xhci_flush_cache((uintptr_t)start_trb, sizeof(struct xhci_generic_trb));

	/* Ringing EP doorbell here */
	xhci_writel(&ctrl->dba->doorbell[udev->slot_id],
				DB_VALUE(ep_index, 0));

	return;
}

/**** POLLING mechanism for XHCI ****/

/**
 * Finalizes a handled event TRB by advancing our dequeue pointer and giving
 * the TRB back to the hardware for recycling. Must call this exactly once at
 * the end of each event handler, and not touch the TRB again afterwards.
 *
 * @param ctrl	Host controller data structure
 * @return none
 */
void xhci_acknowledge_event(struct xhci_ctrl *ctrl)
{
	/* Advance our dequeue pointer to the next event */
	inc_deq(ctrl, ctrl->event_ring);

	/* Inform the hardware */
	xhci_writeq(&ctrl->ir_set->erst_dequeue,
		vtophys((uintptr_t)ctrl->event_ring->dequeue) | ERST_EHB);
}

/**
 * Waits for a specific type of event and returns it. Discards unexpected
 * events. Caller *must* call xhci_acknowledge_event() after it is finished
 * processing the event, and must not access the returned pointer afterwards.
 *
 * @param ctrl		Host controller data structure
 * @param expected	TRB type expected from Event TRB
 * @return pointer to event trb
 */
union xhci_trb *xhci_wait_for_event(struct xhci_ctrl *ctrl, trb_type expected)
{
	trb_type type;
	unsigned long ts = get_timer(0);

	do {
		union xhci_trb *event = ctrl->event_ring->dequeue;

		if (!event_ready(ctrl))
			continue;

		type = TRB_FIELD_TO_TYPE(le32_to_cpu(event->event_cmd.flags));
		if (type == expected)
			return event;

		if (type == TRB_PORT_STATUS)
		/* TODO: remove this once enumeration has been reworked */
			/*
			 * Port status change events always have a
			 * successful completion code
			 */
			BUG_ON(GET_COMP_CODE(
				le32_to_cpu(event->generic.field[2])) !=
								COMP_SUCCESS);
		else
			printf("Unexpected XHCI event TRB, skipping... "
				"(%08x %08x %08x %08x)\n",
				le32_to_cpu(event->generic.field[0]),
				le32_to_cpu(event->generic.field[1]),
				le32_to_cpu(event->generic.field[2]),
				le32_to_cpu(event->generic.field[3]));

		xhci_acknowledge_event(ctrl);
	} while (get_timer(ts) < XHCI_TIMEOUT);

	if (expected == TRB_TRANSFER)
		return NULL;

	printf("XHCI timeout on event type %d... cannot recover.\n", expected);

	return NULL;
}

/*
 * Stops transfer processing for an endpoint and throws away all unprocessed
 * TRBs by setting the xHC's dequeue pointer to our enqueue pointer. The next
 * xhci_bulk_tx/xhci_ctrl_tx on this enpoint will add new transfers there and
 * ring the doorbell, causing this endpoint to start working again.
 * (Careful: This will BUG() when there was no transfer in progress. Shouldn't
 * happen in practice for current uses and is too complicated to fix right now.)
 */
void abort_td(struct usb_device *udev, int ep_index)
{
	struct xhci_ctrl *ctrl = xhci_get_ctrl(udev);
	struct xhci_ring *ring =  ctrl->devs[udev->slot_id]->eps[ep_index].ring;
	union xhci_trb *event;
	u32 field;

	xhci_queue_command(ctrl, NULL, udev->slot_id, ep_index, TRB_STOP_RING);

	event = xhci_wait_for_event(ctrl, TRB_TRANSFER);
	if(event){
	field = le32_to_cpu(event->trans_event.flags);
	BUG_ON(TRB_TO_SLOT_ID(field) != udev->slot_id);
	BUG_ON(TRB_TO_EP_INDEX(field) != ep_index);
	BUG_ON(GET_COMP_CODE(le32_to_cpu(event->trans_event.transfer_len
		!= COMP_STOP)));
	xhci_acknowledge_event(ctrl);
	}
	event = xhci_wait_for_event(ctrl, TRB_COMPLETION);
	if(event){
	BUG_ON(TRB_TO_SLOT_ID(le32_to_cpu(event->event_cmd.flags))
		!= udev->slot_id || GET_COMP_CODE(le32_to_cpu(
		event->event_cmd.status)) != COMP_SUCCESS);
	}
	xhci_acknowledge_event(ctrl);

	xhci_queue_command(ctrl, (void *)((uintptr_t)ring->enqueue |
		ring->cycle_state), udev->slot_id, ep_index, TRB_SET_DEQ);
	event = xhci_wait_for_event(ctrl, TRB_COMPLETION);
	if(event){
	BUG_ON(TRB_TO_SLOT_ID(le32_to_cpu(event->event_cmd.flags))
		!= udev->slot_id || GET_COMP_CODE(le32_to_cpu(
		event->event_cmd.status)) != COMP_SUCCESS);
	}
	xhci_acknowledge_event(ctrl);
}

void record_transfer_result(struct usb_device *udev,
			    union xhci_trb *event, int length)
{
	udev->act_len = min(length, length -
		(int)EVENT_TRB_LEN(le32_to_cpu(event->trans_event.transfer_len)));

	switch (GET_COMP_CODE(le32_to_cpu(event->trans_event.transfer_len))) {
	case COMP_SUCCESS:
		BUG_ON(udev->act_len != length);
		/* fallthrough */
	case COMP_SHORT_TX:
		udev->status = 0;
		break;
	case COMP_STALL:
		udev->status = USB_ST_STALLED;
		break;
	case COMP_DB_ERR:
	case COMP_TRB_ERR:
		udev->status = USB_ST_BUF_ERR;
		break;
	case COMP_BABBLE:
		udev->status = USB_ST_BABBLE_DET;
		break;
	default:
		udev->status = 0x80;  /* USB_ST_TOO_LAZY_TO_MAKE_A_NEW_MACRO */
	}
}

/**
 * Bulk transfer completion or timeout callback function.
 *
 * @param event_node	event nodes that have timed out or completed transmission
 * @param arg		private parameter pointer of event node
 * @return none
 */
static void xhci_bulk_tx_handle(struct xhci_event_node *event_node, void *arg)
{
	union xhci_trb *event = event_node->event;
	struct int_urb_info *urb = (struct int_urb_info *)arg;
	struct usb_device *udev = event_node->udev;
	int pipe = event_node->pipe;
	int ed_num;

	if (event_node->state == __XHCI_EVENT_TIMEOUT) {
		debug("XHCI bulk transfer timed out, aborting...\n");
		if (!urb)
			abort_td(event_node->udev, usb_pipe_ep_index(pipe));
		event_node->udev->status = USB_ST_NAK_REC;
		event_node->udev->act_len = 0;

		xhci_free_event_node(&event_node);
		goto exit;
	}

	record_transfer_result(udev, event, event_node->length);
	xhci_acknowledge_event(event_node->ctrl);
	xhci_inval_cache((uintptr_t)event_node->buffer, event_node->length);

	ed_num = usb_pipeendpoint(pipe) | (usb_pipecontrol(pipe) ? 0 : (usb_pipeout(pipe) << 4));
	if (udev->status != USB_ST_NOT_PROC) {
		udev->irq_act_len = udev->act_len;
		if ((udev->irq_handle_ep[ed_num])) {
			udev->irq_handle_ep[ed_num](udev);
		} else if (udev->irq_handle) {
			udev->irq_handle(udev);
		}
		udev->irq_act_len = 0;
	}
	xhci_free_event_node(&event_node);

exit:
	if (urb) {
		urb->time = get_timer(0);
		urb->b_flags &= ~B_BUSY;
	}
}

/**** Bulk and Control transfer methods ****/
/**
 * Queues up the BULK Request
 *
 * @param udev		pointer to the USB device structure
 * @param pipe		contains the DIR_IN or OUT , devnum
 * @param length	length of the buffer
 * @param buffer	buffer to be read/written based on the request
 * @param urb		interrupt transmission of information
 * @return returns 0 if successful else -1 on failure
 */
int xhci_bulk_tx(struct usb_device *udev, unsigned long pipe,
		 int length, void *buffer, struct int_urb_info *urb)
{
	int num_trbs = 0;
	struct xhci_generic_trb *start_trb;
	bool first_trb = false;
	int start_cycle;
	u32 field = 0;
	u32 length_field = 0;
	struct xhci_ctrl *ctrl = xhci_get_ctrl(udev);
	int slot_id = udev->slot_id;
	int ep_index;
	struct xhci_virt_device *virt_dev;
	struct xhci_ep_ctx *ep_ctx;
	struct xhci_ring *ring;		/* EP transfer ring */
	struct xhci_event_node *event_node;
	void *trb_addr;

	int running_total, trb_buff_len;
	unsigned int total_packet_count;
	int maxpacketsize;
	u64 addr;
	int ret;
	u32 trb_fields[4];
	u64 val_64 = vtophys((uintptr_t)buffer);

	debug("dev=%p, pipe=%lx, buffer=%p, length=%d\n",
		udev, pipe, buffer, length);

	ep_index = usb_pipe_ep_index(pipe);
	virt_dev = ctrl->devs[slot_id];

	xhci_inval_cache((uintptr_t)virt_dev->out_ctx->bytes,
			 virt_dev->out_ctx->size);

	ep_ctx = xhci_get_ep_ctx(ctrl, virt_dev->out_ctx, ep_index);

	ring = virt_dev->eps[ep_index].ring;
	/*
	 * How much data is (potentially) left before the 64KB boundary?
	 * XHCI Spec puts restriction( TABLE 49 and 6.4.1 section of XHCI Spec)
	 * that the buffer should not span 64KB boundary. if so
	 * we send request in more than 1 TRB by chaining them.
	 */
	running_total = TRB_MAX_BUFF_SIZE -
			(lower_32_bits(val_64) & (TRB_MAX_BUFF_SIZE - 1));
	trb_buff_len = running_total;
	running_total &= TRB_MAX_BUFF_SIZE - 1;

	/*
	 * If there's some data on this 64KB chunk, or we have to send a
	 * zero-length transfer, we need at least one TRB
	 */
	if (running_total != 0 || length == 0)
		num_trbs++;

	/* How many more 64KB chunks to transfer, how many more TRBs? */
	while (running_total < length) {
		num_trbs++;
		running_total += TRB_MAX_BUFF_SIZE;
	}

	/*
	 * XXX: Calling routine prepare_ring() called in place of
	 * prepare_trasfer() as there in 'Linux' since we are not
	 * maintaining multiple TDs/transfer at the same time.
	 */
	ret = prepare_ring(ctrl, ring,
			   le32_to_cpu(ep_ctx->ep_info) & EP_STATE_MASK);
	if (ret < 0)
		return ret;

	/*
	 * Don't give the first TRB to the hardware (by toggling the cycle bit)
	 * until we've finished creating all the other TRBs.  The ring's cycle
	 * state may change as we enqueue the other TRBs, so save it too.
	 */
	start_trb = &ring->enqueue->generic;
	start_cycle = ring->cycle_state;

	running_total = 0;
	maxpacketsize = usb_maxpacket(udev, pipe);

	total_packet_count = DIV_ROUND_UP(length, maxpacketsize);

	/* How much data is in the first TRB? */
	/*
	 * How much data is (potentially) left before the 64KB boundary?
	 * XHCI Spec puts restriction( TABLE 49 and 6.4.1 section of XHCI Spec)
	 * that the buffer should not span 64KB boundary. if so
	 * we send request in more than 1 TRB by chaining them.
	 */
	addr = val_64;

	if (trb_buff_len > length)
		trb_buff_len = length;

	first_trb = true;

	/* flush the buffer before use */
	xhci_flush_cache((uintptr_t)buffer, length);

	/* Queue the first TRB, even if it's zero-length */
	do {
		u32 remainder = 0;
		field = 0;
		/* Don't change the cycle bit of the first TRB until later */
		if (first_trb) {
			first_trb = false;
			if (start_cycle == 0)
				field |= TRB_CYCLE;
		} else {
			field |= ring->cycle_state;
		}

		/*
		 * Chain all the TRBs together; clear the chain bit in the last
		 * TRB to indicate it's the last TRB in the chain.
		 */
		if (num_trbs > 1)
			field |= TRB_CHAIN;
		else
			field |= TRB_IOC;

		/* Only set interrupt on short packet for IN endpoints */
		if (usb_pipein(pipe))
			field |= TRB_ISP;

		/* Set the TRB length, TD size, and interrupter fields. */
		if (HC_VERSION(xhci_readl(&ctrl->hccr->cr_capbase)) < 0x100)
			remainder = xhci_td_remainder(length - running_total);
		else
			remainder = xhci_v1_0_td_remainder(running_total,
							   trb_buff_len,
							   total_packet_count,
							   maxpacketsize,
							   num_trbs - 1);

		length_field = ((trb_buff_len & TRB_LEN_MASK) |
				remainder |
				((0 & TRB_INTR_TARGET_MASK) <<
				TRB_INTR_TARGET_SHIFT));

		trb_fields[0] = lower_32_bits(addr);
		trb_fields[1] = upper_32_bits(addr);
		trb_fields[2] = length_field;
		trb_fields[3] = field | (TRB_NORMAL << TRB_TYPE_SHIFT);

		trb_addr = (void *)queue_trb(ctrl, ring, (num_trbs > 1), trb_fields);

		--num_trbs;

		running_total += trb_buff_len;

		/* Calculate length for next transfer */
		addr += trb_buff_len;
		trb_buff_len = min((length - running_total), TRB_MAX_BUFF_SIZE);
	} while (running_total < length);

	giveback_first_trb(udev, ep_index, start_cycle, start_trb);

	if (urb) {
		xhci_non_block_wait_event(udev, pipe, buffer, length, trb_addr,
					  XHCI_TIMEOUT, xhci_bulk_tx_handle, urb);
	} else {
		xhci_block_wait_event(udev, pipe, buffer, length, trb_addr,
				      XHCI_TIMEOUT, xhci_bulk_tx_handle, NULL);
	}

	return (udev->status != USB_ST_NOT_PROC) ? 0 : -1;
}

/**
 * Ctrl transfer completion or timeout callback function.
 *
 * @param event_node	event nodes that have timed out or completed transmission
 * @param arg		private parameter pointer of event node
 * @return none
 */
static void xhci_ctrl_tx_handle(struct xhci_event_node *event_node, void *arg)
{
	union xhci_trb *event = event_node->event;
	struct xhci_event_node *status_event_node;
	struct usb_device *udev;
	int pipe;

	udev    = event_node->udev;
	pipe    = event_node->pipe;

	if (event_node->state == __XHCI_EVENT_TIMEOUT) {
		debug("XHCI control transfer timed out, aborting...\n");
		abort_td(event_node->udev, usb_pipe_ep_index(pipe));
		event_node->udev->status = USB_ST_NAK_REC;
		event_node->udev->act_len = 0;

		xhci_free_event_node(&event_node);
		return;
	}

	record_transfer_result(udev, event, event_node->length);
	xhci_acknowledge_event(event_node->ctrl);
	if (event_node->length > 0) {
		xhci_inval_cache((uintptr_t)event_node->buffer, event_node->length);
	}

	xhci_free_event_node(&event_node);
}

/**
 * Queues up the Control Transfer Request
 *
 * @param udev	pointer to the USB device structure
 * @param pipe		contains the DIR_IN or OUT , devnum
 * @param req		request type
 * @param length	length of the buffer
 * @param buffer	buffer to be read/written based on the request
 * @return returns 0 if successful else error code on failure
 */
int xhci_ctrl_tx(struct usb_device *udev, unsigned long pipe,
		 struct devrequest *req, int length, void *buffer)
{
	int ret;
	int start_cycle;
	int num_trbs;
	u32 field;
	u32 length_field;
	u64 buf_64 = 0;
	struct xhci_generic_trb *start_trb;
	struct xhci_ctrl *ctrl = xhci_get_ctrl(udev);
	int slot_id = udev->slot_id;
	int ep_index;
	u32 trb_fields[4];
	struct xhci_virt_device *virt_dev = ctrl->devs[slot_id];
	struct xhci_ring *ep_ring;
	void *status_trb_ddr = NULL;
	void *data_trb_ddr   = NULL;
	struct xhci_event_node *data_event_node = NULL;

	debug("req=%u (%#x), type=%u (%#x), value=%u (%#x), index=%u\n",
		req->request, req->request,
		req->requesttype, req->requesttype,
		le16_to_cpu(req->value), le16_to_cpu(req->value),
		le16_to_cpu(req->index));

	ep_index = usb_pipe_ep_index(pipe);

	ep_ring = virt_dev->eps[ep_index].ring;
	if (!ep_ring)
		return -EINVAL;

	/*
	 * Check to see if the max packet size for the default control
	 * endpoint changed during FS device enumeration
	 */
	if (udev->speed == USB_SPEED_FULL) {
		ret = xhci_check_maxpacket(udev);
		if (ret < 0)
			return ret;
	}

	xhci_inval_cache((uintptr_t)virt_dev->out_ctx->bytes,
			 virt_dev->out_ctx->size);

	struct xhci_ep_ctx *ep_ctx = NULL;
	ep_ctx = xhci_get_ep_ctx(ctrl, virt_dev->out_ctx, ep_index);

	/* 1 TRB for setup, 1 for status */
	num_trbs = 2;
	/*
	 * Don't need to check if we need additional event data and normal TRBs,
	 * since data in control transfers will never get bigger than 16MB
	 * XXX: can we get a buffer that crosses 64KB boundaries?
	 */

	if (length > 0)
		num_trbs++;
	/*
	 * XXX: Calling routine prepare_ring() called in place of
	 * prepare_trasfer() as there in 'Linux' since we are not
	 * maintaining multiple TDs/transfer at the same time.
	 */
	ret = prepare_ring(ctrl, ep_ring,
				le32_to_cpu(ep_ctx->ep_info) & EP_STATE_MASK);
	if (ret < 0)
		return ret;

	/*
	 * Don't give the first TRB to the hardware (by toggling the cycle bit)
	 * until we've finished creating all the other TRBs.  The ring's cycle
	 * state may change as we enqueue the other TRBs, so save it too.
	 */
	start_trb = &ep_ring->enqueue->generic;
	start_cycle = ep_ring->cycle_state;

	debug("start_trb %p, start_cycle %d\n", start_trb, start_cycle);

	/* Queue setup TRB - see section *******.1 */
	/* FIXME better way to translate setup_packet into two u32 fields? */
	field = 0;
	field |= TRB_IDT | (TRB_SETUP << TRB_TYPE_SHIFT);
	if (start_cycle == 0)
		field |= 0x1;

	/* xHCI 1.0 *******.1: Transfer Type field */
	if (HC_VERSION(xhci_readl(&ctrl->hccr->cr_capbase)) >= 0x100) {
		if (length > 0) {
			if (req->requesttype & USB_DIR_IN)
				field |= (TRB_DATA_IN << TRB_TX_TYPE_SHIFT);
			else
				field |= (TRB_DATA_OUT << TRB_TX_TYPE_SHIFT);
		}
	}

	debug("req->requesttype = %d, req->request = %d,"
		"le16_to_cpu(req->value) = %d,"
		"le16_to_cpu(req->index) = %d,"
		"le16_to_cpu(req->length) = %d\n",
		req->requesttype, req->request, le16_to_cpu(req->value),
		le16_to_cpu(req->index), le16_to_cpu(req->length));

	trb_fields[0] = req->requesttype | req->request << 8 |
				le16_to_cpu(req->value) << 16;
	trb_fields[1] = le16_to_cpu(req->index) |
			le16_to_cpu(req->length) << 16;
	/* TRB_LEN | (TRB_INTR_TARGET) */
	trb_fields[2] = (8 | ((0 & TRB_INTR_TARGET_MASK) <<
			TRB_INTR_TARGET_SHIFT));
	/* Immediate data in pointer */
	trb_fields[3] = field;
	queue_trb(ctrl, ep_ring, true, trb_fields);

	/* Re-initializing field to zero */
	field = 0;
	/* If there's data, queue data TRBs */
	/* Only set interrupt on short packet for IN endpoints */
	if (usb_pipein(pipe))
		field = TRB_ISP | (TRB_DATA << TRB_TYPE_SHIFT);
	else
		field = (TRB_DATA << TRB_TYPE_SHIFT);

	length_field = (length & TRB_LEN_MASK) | xhci_td_remainder(length) |
			((0 & TRB_INTR_TARGET_MASK) << TRB_INTR_TARGET_SHIFT);
	debug("length_field = %d, length = %d,"
		"xhci_td_remainder(length) = %d , TRB_INTR_TARGET(0) = %d\n",
		length_field, (length & TRB_LEN_MASK),
		xhci_td_remainder(length), 0);

	if (length > 0) {
		if (req->requesttype & USB_DIR_IN)
			field |= TRB_DIR_IN;
		buf_64 = vtophys((uintptr_t)buffer);

		trb_fields[0] = lower_32_bits(buf_64);
		trb_fields[1] = upper_32_bits(buf_64);
		trb_fields[2] = length_field;
		trb_fields[3] = field | ep_ring->cycle_state;

		xhci_flush_cache((uintptr_t)buffer, length);
		data_trb_ddr = (void *)queue_trb(ctrl, ep_ring, true, trb_fields);

		data_event_node = xhci_non_block_wait_event(udev, pipe, buffer, length, data_trb_ddr, 
							    XHCI_TIMEOUT, xhci_ctrl_tx_handle, NULL);
	}

	/*
	 * Queue status TRB -
	 * see Table 7 and sections ******** and *******.3
	 */

	/* If the device sent data, the status stage is an OUT transfer */
	field = 0;
	if (length > 0 && req->requesttype & USB_DIR_IN)
		field = 0;
	else
		field = TRB_DIR_IN;

	trb_fields[0] = 0;
	trb_fields[1] = 0;
	trb_fields[2] = ((0 & TRB_INTR_TARGET_MASK) << TRB_INTR_TARGET_SHIFT);
		/* Event on completion */
	trb_fields[3] = field | TRB_IOC |
			(TRB_STATUS << TRB_TYPE_SHIFT) |
			ep_ring->cycle_state;

	status_trb_ddr = (void *)queue_trb(ctrl, ep_ring, false, trb_fields);

	giveback_first_trb(udev, ep_index, start_cycle, start_trb);

	/*
	 * If the status event has already been completed or timed out, then the
	 * data event must have been completed or timed out, so here we just need 
	 * to wait for the status event
	 */
	xhci_block_wait_event(udev, pipe, buffer, length, status_trb_ddr,
			      XHCI_TIMEOUT, xhci_ctrl_tx_handle, NULL);

	if (data_event_node) {
		if (data_event_node->state == __XHCI_EVENT_WAITING)
			xhci_del_event_from_wait_list(data_event_node);
		xhci_free_event_node(&data_event_node);
	}

	return (udev->status != USB_ST_NOT_PROC) ? 0 : -1;
}
