#include <pmon.h>
#include <stdio.h>

/*
 * BYTE control gpio
 * Gpio on the bridge or in misc
 */
#define BYTE_GPIO_OEN       0x800
#define BYTE_GPIO_O         0x900
#define BYTE_GPIO_I         0xa00

#if defined(LOONGSON3A5000) || defined(LOONGSON_LS2K)
#ifdef  LOONGSON3A5000      // LS7A
#ifdef  LS7A2000
#define BYTE_GPIO_NUM       58
#else
#define BYTE_GPIO_NUM       57
#endif
#elif   LOONGSON_LS2K       //LS2K
#define BYTE_GPIO_NUM       64
#endif
#define BYTE_GPIO_BASE      PHYS_TO_UNCACHED((0x10080000 + 0x60000))
#else
#ifdef   LOONGARCH_2K300     //
#define BYTE_GPIO_NUM       106
#define BYTE_GPIO_BASE      PHYS_TO_UNCACHED(0x16104000)
#endif
#endif

#ifdef BYTE_GPIO_BASE
void byte_gpio_out(unsigned int gpio, unsigned char value)
{
    if (BYTE_GPIO_NUM > gpio) {
        writeb(0x00, BYTE_GPIO_BASE + BYTE_GPIO_OEN + gpio);

       if (value)
           writeb(0x01, BYTE_GPIO_BASE + BYTE_GPIO_O + gpio);
       else
           writeb(0x00, BYTE_GPIO_BASE + BYTE_GPIO_O + gpio);
    } else {
        printf("Error: Gpio%d out of range, Max %d\n", gpio, BYTE_GPIO_NUM);
    }
}

unsigned char byte_gpio_in(unsigned int gpio)
{
    unsigned char value;

    if (BYTE_GPIO_NUM > gpio) {
        writeb(0x01, BYTE_GPIO_BASE + BYTE_GPIO_OEN + gpio);

        value = readb(BYTE_GPIO_BASE + BYTE_GPIO_I + gpio) & 0x01;
    } else {
        value = 0xff;
        printf("Error: Gpio%d out of range, Max %d\n", gpio, BYTE_GPIO_NUM);
    }
    return value;
}
#else
void byte_gpio_out(unsigned int gpio, unsigned char value)
{
    printf("No byte type gpio\n");
}
unsigned char byte_gpio_in(unsigned int gpio)
{
    printf("No byte type gpio\n");
}
#endif

/*
 * BIT control gpio
 * GPIO on CPU
 */
#if defined(LOONGSON3A5000) || defined(LOONGSON_LS2K)
#define CPU_GPIO_FUNC_En    0x04
#define CPU_GPIO_OEN        0x00
#define CPU_GPIO_O          0x08
#define CPU_GPIO_I          0x0c
#define CPU_GPIO1           0
#define CPU_GPIO_BASE       PHYS_TO_UNCACHED(0x1fe00500)
#ifdef LOONGSON3A5000
#define CPU_GPIO_NUM       16
#elif   LOONGSON_LS2K       //LS2K
#define CPU_GPIO_NUM       32
#endif
#else
#ifdef  LOONGARCH_2K500
#define CPU_GPIO_OEN        0x00
#define CPU_GPIO_O          0x40
#define CPU_GPIO_I          0x38
#define CPU_GPIO1           4
#define CPU_GPIO_NUM        155
#define CPU_GPIO_BASE       PHYS_TO_UNCACHED(0x1fe10430)
#elif   LOONGARCH_2K1000
#define CPU_GPIO_OEN        0x00
#define CPU_GPIO_O          0x10
#define CPU_GPIO_I          0x20
#define CPU_GPIO1           8
#define CPU_GPIO_NUM        60
#define CPU_GPIO_BASE       PHYS_TO_UNCACHED(0x1fe00500)
#endif
#endif

#ifdef CPU_GPIO_BASE
void cpu_gpio_out(unsigned int gpio, unsigned char value)
{
	unsigned int val;
    if (CPU_GPIO_NUM > gpio) {
        val = readl(CPU_GPIO_BASE + CPU_GPIO_OEN + gpio%32*CPU_GPIO1);
        val &= ~(1<<(gpio%32));
        writel(val, CPU_GPIO_BASE + CPU_GPIO_OEN + gpio%32*CPU_GPIO1);

#ifdef  CPU_GPIO_FUNC_En
        val = readl(CPU_GPIO_BASE + CPU_GPIO_FUNC_En);
        val &= ~(1<<gpio);
        writel(val, CPU_GPIO_BASE + CPU_GPIO_FUNC_En);
#endif
        val = readl(CPU_GPIO_BASE + CPU_GPIO_O + gpio%32*CPU_GPIO1);
        if (value) {
            val |= (1<<(gpio%32));
        } else {
            val &= ~(1<<(gpio%32));
        }
        writel(val, CPU_GPIO_BASE + BYTE_GPIO_O + gpio%32*CPU_GPIO1);
    } else {
        printf("Error: Gpio%d out of range, Max %d\n", gpio, CPU_GPIO_NUM);
    }
}

unsigned char cpu_gpio_in(unsigned int gpio)
{
	unsigned int val;
    unsigned char value;

    if (CPU_GPIO_NUM > gpio) {
        val = readl(CPU_GPIO_BASE + CPU_GPIO_OEN + gpio%32*CPU_GPIO1);
        val |= (1<<(gpio%32));
        writel(val, CPU_GPIO_BASE + CPU_GPIO_OEN + gpio%32*CPU_GPIO1);

#ifdef  CPU_GPIO_FUNC_En
        val = readl(CPU_GPIO_BASE + CPU_GPIO_FUNC_En);
        val &= ~(1<<gpio);
        writel(val, CPU_GPIO_BASE + CPU_GPIO_FUNC_En);
#endif
        val = readl(CPU_GPIO_BASE + CPU_GPIO_I + gpio%32*CPU_GPIO1);
        value = (val & (1<<(gpio%32))) ? 1: 0;
    } else {
        value = 0xff;
        printf("Error: Gpio%d out of range, Max %d\n", gpio, CPU_GPIO_NUM);
    }   
    return value;
}
#else
void cpu_gpio_out(unsigned int gpio, unsigned char value)
{
    printf("No cpu type gpio\n");
}
unsigned char cpu_gpio_in(unsigned int gpio)
{
    printf("No cpu type gpio\n");
}
#endif
