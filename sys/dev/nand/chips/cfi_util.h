#ifndef _CFI_UTIL_H
#define _CFI_UTIL_H

#define __xipram
extern struct mtd_info *lio_mtd;
extern void cfi_udelay(int us);
extern uint32_t cfi_build_cmd_addr(uint32_t cmd_ofs,
				struct map_info *map, struct cfi_private *cfi);
extern map_word cfi_build_cmd(u_long cmd, struct map_info *map, struct cfi_private *cfi);
extern unsigned long cfi_merge_status(map_word val, struct map_info *map,
					   struct cfi_private *cfi);
extern uint32_t cfi_send_gen_cmd(u_char cmd, uint32_t cmd_addr, uint32_t base,
				struct map_info *map, struct cfi_private *cfi,
				int type, map_word *prev_val);
extern int cfi_qry_present(struct map_info *map, __u32 base,
			     struct cfi_private *cfi);
extern int cfi_qry_mode_on(uint32_t base, struct map_info *map,
			     struct cfi_private *cfi);
extern void cfi_qry_mode_off(uint32_t base, struct map_info *map,
			       struct cfi_private *cfi);
extern struct cfi_extquery *
    cfi_read_pri(struct map_info *map, __u16 adr, __u16 size, const char* name);
extern void cfi_fixup(struct mtd_info *mtd, struct cfi_fixup *fixups);
extern int cfi_varsize_frob(struct mtd_info *mtd, varsize_frob_t frob,
				     loff_t ofs, size_t len, void *thunk);

#define GFP_KERNEL			0
#define MTD_WRITEABLE		0x400	/* Device is writeable */
#define MTD_BIT_WRITEABLE	0x800	/* Single bits can be flipped */
#define MTD_NO_ERASE		0x1000	/* No erase necessary */
#define MTD_POWERUP_LOCK	0x2000	/* Always locked after reset */
__inline__ int _set_bit(int nr, long *addr)
{
	int mask, retval;

	addr += nr >> 5;
	mask = 1 << (nr & 0x1f);
	//    cli();
	retval = (mask & *addr) != 0;
	*addr |= mask;
	//    sti();
	return retval;
}

__inline__ int _clear_bit(int nr, long * addr)
{
	int mask, retval;

	addr += nr >> 5;
	mask = 1 << (nr & 0x1f);
	//    cli();
	retval = (mask & *addr) != 0;
	*addr &= ~mask;
	//    sti();
	return retval;
}

__inline__ int _test_bit(int nr, long * addr)
{
	int mask;

	addr += nr >> 5;
	mask = 1 << (nr & 0x1f);
	return ((mask & *addr) != 0);
}

struct otp_info {
	__u32 start;
	__u32 length;
	__u32 locked;
};

#define BANKWIDTH_8     1
#define BANKWIDTH_16    2

#define cond_resched idle
#define msleep(n) mdelay(n)
#define DIV_ROUND_UP(n, d) (((n) + (d) - 1) / (d))

typedef u64 resource_size_t;

struct kvec {
	void *iov_base; /* and that should *never* hold a userland pointer */
	size_t iov_len;
};

static unsigned long __ffs(unsigned long word)
{
	int num = 0;

#if BITS_PER_LONG == 64
	if ((word & 0xffffffff) == 0) {
		num += 32;
		word >>= 32;
	}
#endif
	if ((word & 0xffff) == 0) {
		num += 16;
		word >>= 16;
	}
	if ((word & 0xff) == 0) {
		num += 8;
		word >>= 8;
	}
	if ((word & 0xf) == 0) {
		num += 4;
		word >>= 4;
	}
	if ((word & 0x3) == 0) {
		num += 2;
		word >>= 2;
	}
	if ((word & 0x1) == 0)
		num += 1;
	return num;
}

#ifndef swap
#define swap(a, b) \
	do { typeof(a) __tmp = (a); (a) = (b); (b) = __tmp; } while (0)
#endif

#define ARRAY_SIZE(x) (sizeof(x) / sizeof((x)[0]))

#define LONG_MAX    ((long)(~0UL>>1))
#define MAX_JIFFY_OFFSET ((LONG_MAX >> 1)-1)
#define HZ              250
#define MSEC_PER_SEC	1000L
#define USEC_PER_MSEC	1000L
#define NSEC_PER_USEC	1000L
#define NSEC_PER_MSEC	1000000L
#define USEC_PER_SEC	1000000L
#define NSEC_PER_SEC	1000000000L
#define FSEC_PER_SEC	1000000000000000LL

#define HZ_TO_MSEC_MUL32	U64_C(0x80000000)
#define HZ_TO_MSEC_ADJ32	U64_C(0x0)
#define HZ_TO_MSEC_SHR32	29
#define MSEC_TO_HZ_MUL32	U64_C(0x80000000)
#define MSEC_TO_HZ_ADJ32	U64_C(0x180000000)
#define MSEC_TO_HZ_SHR32	33
#define HZ_TO_MSEC_NUM		4
#define HZ_TO_MSEC_DEN		1
#define MSEC_TO_HZ_NUM		1
#define MSEC_TO_HZ_DEN		4

#define HZ_TO_USEC_MUL32	U64_C(0xFA000000)
#define HZ_TO_USEC_ADJ32	U64_C(0x0)
#define HZ_TO_USEC_SHR32	20
#define USEC_TO_HZ_MUL32	U64_C(0x83126E98)
#define USEC_TO_HZ_ADJ32	U64_C(0x7FF7CED9168)
#define USEC_TO_HZ_SHR32	43
#define HZ_TO_USEC_NUM		4000
#define HZ_TO_USEC_DEN		1
#define USEC_TO_HZ_NUM		1
#define USEC_TO_HZ_DEN		4000
#define HZ_TO_NSEC_NUM		4000000
#define HZ_TO_NSEC_DEN		1
#define NSEC_TO_HZ_NUM		1
#define NSEC_TO_HZ_DEN		4000000

#if HZ <= MSEC_PER_SEC && !(MSEC_PER_SEC % HZ)
/*
 * HZ is equal to or smaller than 1000, and 1000 is a nice round
 * multiple of HZ, divide with the factor between them, but round
 * upwards:
 */
static inline unsigned long _msecs_to_jiffies(const unsigned int m)
{
	return (m + (MSEC_PER_SEC / HZ) - 1) / (MSEC_PER_SEC / HZ);
}
#elif HZ > MSEC_PER_SEC && !(HZ % MSEC_PER_SEC)
/*
 * HZ is larger than 1000, and HZ is a nice round multiple of 1000 -
 * simply multiply with the factor between them.
 *
 * But first make sure the multiplication result cannot overflow:
 */
static inline unsigned long _msecs_to_jiffies(const unsigned int m)
{
	if (m > jiffies_to_msecs(MAX_JIFFY_OFFSET))
		return MAX_JIFFY_OFFSET;
	return m * (HZ / MSEC_PER_SEC);
}
#else
/*
 * Generic case - multiply, round and divide. But first check that if
 * we are doing a net multiplication, that we wouldn't overflow:
 */
static inline unsigned long _msecs_to_jiffies(const unsigned int m)
{
	if (HZ > MSEC_PER_SEC && m > jiffies_to_msecs(MAX_JIFFY_OFFSET))
		return MAX_JIFFY_OFFSET;

	return (MSEC_TO_HZ_MUL32 * m + MSEC_TO_HZ_ADJ32) >> MSEC_TO_HZ_SHR32;
}
#endif

static unsigned int jiffies_to_usecs(const unsigned long j)
{
	/*
	 * Hz usually doesn't go much further MSEC_PER_SEC.
	 * jiffies_to_usecs() and usecs_to_jiffies() depend on that.
	 */

#if !(USEC_PER_SEC % HZ)
	return (USEC_PER_SEC / HZ) * j;
#else
	return (j * HZ_TO_USEC_NUM) / HZ_TO_USEC_DEN;
#endif
}

#if !(USEC_PER_SEC % HZ)
static inline unsigned long usecs_to_jiffies(const unsigned int u)
{
	return (u + (USEC_PER_SEC / HZ) - 1) / (USEC_PER_SEC / HZ);
}
#else
static inline unsigned long usecs_to_jiffies(const unsigned int u)
{
	return (USEC_TO_HZ_MUL32 * u + USEC_TO_HZ_ADJ32)
		>> USEC_TO_HZ_SHR32;
}
#endif

#endif
