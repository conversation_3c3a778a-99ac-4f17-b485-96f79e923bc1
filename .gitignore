#
# NOTE! Don't add files that are generated in specific
# subdirectories here. Add them in the ".gitignore" file
# in that subdirectory instead.
#
# Normal rules (sorted alphabetically)
#
.*
*.asn1.[ch]
*.cfgout
*.dtbo
*.dtb.S
*.elf
*.exe
*.gcda
*.gcno
*.i
*.ii
*.img
*.lex.c
*.lst
*.mod.c
*.o
*.o.*
*.order
*.patch
*.s
*.su
*.swp
*.tab.[ch]

*.log

#
# git files that we don't want to ignore even it they are dot-files
#
!.gitignore
!.mailmap

# gdb files
.gdb_history

# cscope files
cscope.*

# tags files
/tags
/ctags
/etags

# gnu global files
GPATH
GRTAGS
GSYMS
GTAGS

*.orig
*~
\#*#

# compile directory
/*/*/compile

# backup file
*.bak

# generated temporary file
*.tmp
*.aml
*.bin.gz

# pmoncfg
tools/pmoncfg/gram.h
tools/pmoncfg/pmoncfg

cmd.sh