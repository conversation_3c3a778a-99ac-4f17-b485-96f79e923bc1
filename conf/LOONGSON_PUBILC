#
# System Name and Target Name
#
config		pmon 
option		SYSTYPE="\"loongson\""
option		TARGETNAME="\"loongson\""
select		loongarch

#
#  Define target endian
#
makeoptions	ENDIAN=EL		   # Little endian version.

#
# Functional options.
#
option		NOSNOOP			# Caches are no-snooping
option		INET
option		AUTOLOAD
option		VGAROM_IN_BIOS
option		BONITOEL
option		BOOT_PARAM

#option		MCP68_IDE		# Enable the MCP68 IDE 0 channel
option		MY40IO
option		NOPCINAMES		# Save some space for x86emu
option		INPUT_FROM_BOTH
option		OUTPUT_TO_BOTH
option		CONFIG_VIDEO_SW_CURSOR
option		FOR_GXEMUL

#
# HAVE options. What tgt level provide
#
option		HAVE_TOD		# Time-Of-Day clock
option		HAVE_NVENV		# Platform has non-volatile env mem
option		HAVE_LOGO		# Output splash logo
option		CONFIG_VIDEO_LOGO
option		PROGRESS_BAR
#option		RADEON7000
#option		DEBUG_EMU_VGA
#option		CONFIG_PCI0_LARGE_MEM	
#option		CONFIG_PCI0_HUGE_MEM	
#option		CONFIG_PCI0_GAINT_MEM	
option		NVRAM_IN_FLASH	

#
# Platform options
#
select		mod_uart_ns16550	# Standard UART driver
#option		CONS_BAUD=B9600
option		CONS_BAUD=B115200
select		ext4
select		ramfiles
select		fatfs
select		gzip
select		http
#select		nfs
select		tcp
select		inet
select		tftpd
select		iso9660
#select		mod_x86emu		    # X86 emulation for VGA
#select		vt82c686		    # via686a/b code
#option		PCI_IDSEL_VIA686B=17
#select		cmd_lwdhcp
#select		cmd_bootp

select		mod_usb
select		mod_usb_storage
#select		mod_usb_uhci
select		mod_usb_ohci
select		mod_usb_kbd

#
# flash type selection. Selects flash support
#
select		mod_flash_amd		# AMD flash device programming
select		mod_flash_intel		# intel flash device programming
select		mod_flash_sst		# intel flash device programming
select		mod_debugger		# Debugging module
select		mod_symbols		    # Symbol table handling
select		mod_s3load		    # Srecord loading
#select		mod_fastload		# LSI Fastload

#
# Command selection. Selects pmon commands
#
select		cmd_test_spi
select		cmd_setup
select		mod_display
select		mod_elfload		    # ELF loading
select		cmd_about	        # Display info about PMON
select		cmd_boot	        # Boot wrapper
select		cmd_mycmd	        
select		cmd_newmt
select		cmd_cache	        # Cache enabling
#select		cmd_call	        # Call a function command
select		cmd_date	        # Time of day command
select		cmd_env		        # Full blown environment command set
select		cmd_flash	        # Flash programming cmds
select		cmd_hist	        # Command history
select		cmd_ifaddr	        # Interface address command
select		cmd_l		        # Disassemble
select		cmd_mem		        # Memory manipulation commands
select		cmd_more	        # More paginator
select		cmd_misc	        # Reboot & Flush etc.
#select		cmd_stty	        # TTY setings command
select		cmd_tr		        # Host port-through command
select		cmd_devls	        # Device list
select		cmd_set		        # As cmd_env but not req. cmd_hist
select		cmd_testdisk        
select		cmd_shell	        # Shell commands, vers, help, eval
select		cmd_xyzmodem
