/*	$Id: exception.c,v 1.1.1.1 2006/09/14 01:59:08 root Exp $ */

/*
 * Copyright (c) 2000-2002 Opsycon AB
 * Copyright (c) 2002 <PERSON><PERSON> (www.lindergren.com)
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB.
 *	This product includes software developed by <PERSON><PERSON>.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */
#include <stdio.h>
#include <termio.h>
#include <string.h>
#include <setjmp.h>
#include <stdlib.h>
#include <gdb.h>

#include <signal.h>
#include <machine/cpu.h>
#include <machine/frame.h>
#ifdef _KERNEL
#undef _KERNEL
#include <sys/ioctl.h>
#define _KERNEL
#else
#include <sys/ioctl.h>
#endif

#include <pmon.h>
#include <debugger.h>
#include <ri.h>
#include "mod_debugger.h"

#include "../arch/loongarch/loongarchregs.h"

extern void _go __P((void));

/*
 *  For each CPU this table have a pointer to its cpuinfo area.
 */
struct trapframe *cpuinfotab[8];

extern char _start[], _end[];
#define kernel_address(x) ((x) >= _start && (x) < _end)
#define unmap_address(x) ((x) >= PHYS_TO_CACHED(0) && (x) < PHYS_TO_CACHED(0x100000000ULL))
int bt(unsigned long pc, unsigned long sp, unsigned long ra)
{
	#define BTDEPTH 128
	unsigned long saved_sp, saved_pc;
	int depth, i, o;
	char buf[512], *p;
	unsigned int ins;
	unsigned int size, offset;

	saved_sp = sp;
	saved_pc = ra;
	for (depth = 0; depth < BTDEPTH; depth++) {
		if ((pc & 3) || (sp & 3) || !kernel_address(pc))
			goto out;
		p = buf + sprintf(buf, "sp=0x%llx, ", sp);
		ins = *(int *)pc;
		md_disasm(p, pc);
		printf("%s\n", buf);
		for (i = 0; i < 4096; i += 4) {
			ins = *(int *)(pc - i);
			/*
			 * #jr $ra
			 */
			if (ins == 0x4c000020 && pc != saved_pc) {
				break;
			} else if ((ins & 0xffc003ff) == 0x02c00063
				|| (ins & 0xffc003ff) == 0x02800063) {
				/* addi.d $r3, $r3, -o */
				o =((ins >> 10) & 0xfff);
				if (o & 0x800)
					o = - (0xfff - o + 1);
				else
					continue;
				saved_sp = sp - o;
				break;
			} else if ((ins & 0xffc003ff) == 0x29c00061
				|| (ins & 0xffc003ff) == 0x29800061) {
				/*
				 * #st.d $r1, $r3, o
				 */
				o =((ins >> 10) & 0xfff);
				if (o & 0x800) {
					o = - (0xfff - o + 1);
					continue;
				}
				if (!unmap_address(sp + o))
					goto out;
				saved_pc = *(long *) (sp + o);
			}
		}
		if (pc == saved_pc)
			break;
		else {
			sp = saved_sp;
			pc = saved_pc;
		}
	}
out:
	return 0;
}

int handle_bp(void *regs);
#if NMOD_DEBUGGER > 0
/*
 *  exception()
 *      An exception has been generated. Need to sort out from where.
 *      frame is a pointer to cpu data saved on the PMON2000 stack.
 */
void
exception(frame)
	struct trapframe *frame;
{
	/*now pmon do not support interrupt so run to while(1) */
	unsigned long long val;
	unsigned long long epc;
	int i;

#if NGDB
again:
	if (handle_bp(frame) >= 0)
		return;
#endif
	val = read_64bit_csr(0x8a);
	if (val & 0x1) {
		/* tlb refill exception set CRMD to DA mode rewrite it */
		write_64bit_csr(0x0, 0xb0);
		printf("\nTLB refill epc  0x%llx\n", val & (~0x3ULL));
		printf("TLB refill badv 0x%llx\n", read_64bit_csr(0x89));
		epc = val & (~0x3ULL);
	} else {
		if (!(read_64bit_csr(0x5) & 0x3f0000ULL)) {
			if (read_64bit_csr(0x5) & 0x800ULL) {
				irq_timer_handler();
				exception_stack_pop();
			}
		}
		epc = frame->csrepc;
	}
	printf("\nLoongArch frame address 0x%llx\n", frame);
	printf("csr_epc    0x%llx\n", frame->csrepc);

	printf("csr_crmd   0x%llx\n",  frame->csrcrmd);
	printf("csr_prmd   0x%llx\n",  frame->csrprmd);
	printf("csr_exctl  0x%llx\n",  frame->csrectl);
	printf("csr_exstat 0x%llx\n",  frame->csrestat);
	printf("csr_badv   0x%llx\n",  frame->csrbadv);
	printf("csr_badi   0x%llx\n",  frame->csrbadi);
	bt(epc, frame->sp, frame->ra);
	for (i = 0; i < 32; i++) {

		if ((i & 3) == 0)
			printf("\n%02x : ", i);
		printf("%016lx ", ((unsigned long long *)frame)[i]);
	}
	printf("\n");
#if NGDB
	goto again;
#else
	while(1);
#endif
}
#else
void
exception(frame)
	struct trapframe *frame;
{
}
#endif /* NMOD_DEBUGGER */
