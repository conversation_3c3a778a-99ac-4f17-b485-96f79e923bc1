/* $Id: main.c,v 1.1.1.1 2006/09/14 01:59:08 root Exp $ */

/*
 * Copyright (c) 2001-2002 Opsycon AB  (www.opsycon.se / www.opsycon.com)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *      This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 *  This code was created from code released to Public Domain
 *  by LSI Logic and Algorithmics UK.
 */

#include <stdio.h>
#include <string.h>
#include <machine/pio.h>
#include <pmon.h>
#include <termio.h>
#include <endian.h>
#include <signal.h>
#include <setjmp.h>
#include <ctype.h>
#include <unistd.h>
#include <stdlib.h>
#include <efi_api.h>
#ifdef _KERNEL
#undef _KERNEL
#include <sys/ioctl.h>
#define _KERNEL
#else
#include <sys/ioctl.h>
#endif
#include <exec.h>
#include <file.h>

#include <sys/device.h>
#include "mod_debugger.h"
#include "mod_symbols.h"

#include "sd.h"
#include "wd.h"
#include "../cmds/cmd_main/window.h"
#include "../cmds/cmd_main/cmd_main.h"

#include <pflash.h>
#include <flash.h>
#include <diskfs.h>
#include <dev/pflash_tgt.h>
#include <mod_usb_ohci.h>
#ifdef ACPI_SUPPORT
#include <acpi/acpi.h>
#endif
extern void *callvec;

#include "cmd_hist.h"		/* Test if command history selected */
#include "cmd_more.h"		/* Test if more command is selected */

void print_mem_freq(void);
extern int bios_available;
extern int cmd_main_mutex;
extern int ohci_index;
extern int dl_ohci_kbd(void);

jmp_buf jmpb;		/* non-local goto jump buffer */
char line[LINESZ + 1];	/* input line */
struct termio clntterm;	/* client terminal mode */
struct termio consterm;	/* console terminal mode */
register_t initial_sr;

unsigned long long  memorysize_high_n[TOT_NODE_NUM] = {0};
unsigned long long memorysize = 0;
unsigned long long memorysize_total = 0;

char prnbuf[LINESZ + 8];	/* commonly used print buffer */

int repeating_cmd;
unsigned int moresz = 10;
#ifdef AUTOLOAD
static int autoload __P((char *));
#else
static void autorun __P((char *));
#endif
extern void __init __P((void));
extern void _exit (int retval);
extern int efi_at_runtime;

#ifdef INET
static void pmon_intr(int dummy)
{
	sigsetmask(0);
	longjmp(jmpb, 1);
}
#endif

/*FILE *logfp; = stdout; */

#if NCMD_HIST == 0
void get_line(char *line, int how)
{
	int i;

	i = read (STDIN, line, LINESZ);
	if (i > 0) {
		i--;
	}
	line[i] = '\0';
}
#endif

char * fill_path(char *path)
{
	char *tmp;
	char *fsname;
	char tempname[256] = {0};
	DeviceDisk *d = NULL;
	int i;

	tmp = index(path, '@');
	if (tmp > path)
		return path;
	if (tmp == 0) {
		tmp = path;
		if (strncmp(tmp, "/dev/fs/", 8) == 0) {
			tmp += 8;
			if (index(tmp, '/') == 0)
				return NULL;
			for (i = 0; i < strlen(tmp); i++) {
				if (tmp[i] >= '0' && tmp[i] <= '9') {
					strncpy(tempname, tmp, ++i);
					break;
				}
			}
			d = FindDevice(tempname);
			if (d == NULL) {
				return NULL;
			}
			i = tmp[i] == '/' ? 0 : tmp[i] - 'a';
			if (d->dev_fstype == FS_TYPE_ISO9660) {
				fsname = "iso9660";
			} else if (d->part[i] != NULL && d->part[i]->fs != NULL && d->part[i]->fs->fsname != NULL && i < d->dev_fscount)
				fsname = d->part[i]->fs->fsname;
			else {
				memset(path, 0, 256);
				return NULL;
			}
			sprintf(tempname, "/dev/fs/%s@%s%c", fsname, path+8, '\0');
			memset(path, 0, 256);
			strncpy(path, tempname, strlen(tempname));
		}
		return path;
	}
}

static char loop_reboot(void)
{
	char c;
	unsigned int dly;
	unsigned int cnt = 0;
	struct termio sav;
	char numtostr[10];
	char *start_list;
	char *loopreboot;
	unsigned int tmp, flag, loopcount, remaincount;

	loopreboot = getenv("rebootruns");
	if(!loopreboot || !atob (&tmp, loopreboot, 10) || tmp < 0 || tmp > PMON_LOOPREBOOT_MAX) {
		tmp = 0;
		goto Endtest;
	}
	loopcount = tmp + 1;
	btoa(numtostr, loopcount, 10);
	setenv("rebootruns", numtostr);
	printf("The loop reboot has been run %d times\n", loopcount);

	loopreboot = getenv("loopreboot");
	if(!loopreboot || !atob (&tmp, loopreboot, 10) || tmp < 1 || tmp > PMON_LOOPREBOOT_MAX) {
		tmp = 0;
		goto Endtest;
	}
	remaincount = tmp - 1;
	if(remaincount) {
		btoa(numtostr, remaincount, 10);
		setenv("loopreboot", numtostr);
		printf("The loop reboot test remaining %d times\n", remaincount);
	} else {
		goto Endtest;
	}

	printf("Press [c|C] to stop the reboot test\n");
	
	char *d = getenv ("rebootdelay");
	if (!d || !atob (&dly, d, 10) || dly < 0 || dly > 99) {
		dly = 6;
	}

	ioctl (STDIN, CBREAK, &sav);
	do {
		delay(1000000);
		printf ("\b\b%02d", --dly);
		ioctl (STDIN, FIONREAD, &cnt);
	} while (dly != 0 && cnt == 0);

	if(cnt > 0 && strchr("cC", c=getchar())) {
		printf("cnt = %d: input = 0x%08x\n", cnt, c);
		ioctl (STDIN, TCSETAF, &sav);
		putchar ('\n');
		if(c == 'c' || c == 'C') {
			tmp = 1;
			goto Endtest;
		}
	} else {
		do_cmd("reboot");
	}
	return 0;

Endtest:
	printf("\n---------------------------\n");
	if(tmp) {
		printf("Test successfully completed\n");
	} else {
		printf("Abnormal stop test, error!!\n");
	}
	printf("---------------------------\n\n");
	start_list = getenv("savestartlist");
	setenv("startlist", start_list);
	unsetenv("savestartlist");
	unsetenv("loopreboot");
	return 0;
}

static int load_menu_list()
{
	DeviceDisk *p;
	char *start_list;
	char* rootdev = NULL;
	struct device *dev, *next_dev;
	char path[128], load[128];
	unsigned char order[START_MEMU_LIST] = {0};
	unsigned int i, j, z, k, current, part_num;
#ifdef DEFAULT_GRUB_BOOT
	static bool legacy_boot = 0;
#endif
	memset(load, 0, 256);
	memset(path, 0, 256);

	start_list = getenv("startlist");
	if(!start_list || !atob (&k, start_list, 10) || k <= 0 || k > START_DEFAULT_ORDER_MAX) {
		k = START_DEFAULT_ORDER;
	} 

	for(i=0; i<START_MEMU_LIST; i++)
	{
		if(!k) break;
		current = k%10;
		k /= 10;
		for(j=0, z=0; j<START_MEMU_LIST; j++) {
			if((current > START_MEMU_LIST) || (current == order[j])) {
				z = 1; break;
			}
		}
		if(!z) 
			order[START_MEMU_LIST - i - 1] = current;
	}
#if 0
	printf("Boot priority: ");
	for(i=0; i<START_MEMU_LIST; i++)
	{
		if (order[i]) {
			printf("%s > ",startup_menu[order[i]-1]);
		}
	}
	printf("other\n");
#endif
	for(i=0; i<START_MEMU_LIST; i++)
	{
		if (order[i])
			current = order[i] - 1;
		else
			continue;

		if(current == 7) {
			loop_reboot();
		}
		
		if(current == 5) {
			rootdev = getenv("bootdev");
			if (rootdev != NULL) {
				sprintf(load, "bl -d ide %s", rootdev);
				if( do_cmd(load) == 0) {
					return 0;
				}
			}
			continue;
		}
		if(current == 6) {
			//try to read boot.cfg from CD-ROM disk second
			for (dev = TAILQ_FIRST(&alldevs); dev != NULL; dev = next_dev) {
				next_dev = TAILQ_NEXT(dev, dv_list);
				if(dev->dv_class != DV_DISK) {
					continue;
				}
				if (strncmp(dev->dv_xname, "cd", 2) == 0) {
					sprintf(load, "bl -d ide /dev/fs/iso9660@%s/boot/boot.cfg", dev->dv_xname);
					if( do_cmd(load) == 0) {
						return 0;
					}
					sprintf(load, "bl -d ide /dev/fs/iso9660@%s/boot.cfg", dev->dv_xname);
					if( do_cmd(load) == 0) {
						return 0;
					}
				}
			}
			continue;
		}
		p = FindDevice(startup_menu[current]);
		if (p != NULL) {
			if(!current) {
				if (p->dev_fstype == FS_TYPE_ISO9660) {
					sprintf(load, "bl -d ide /dev/fs/iso9660@%s/boot/boot.cfg", startup_menu[current]);
					if( do_cmd(load) == 0) {
						return 0;
					}
					sprintf(load, "bl -d ide /dev/fs/iso9660@%s/boot.cfg", startup_menu[current]);
					if( do_cmd(load) == 0) {
						return 0;
					}
					continue;
				}
			}
		    part_num = p->dev_fscount;
		    if (part_num > 25)
		        part_num = 25;
		    for (j = 0; j < part_num; j++) {
				if (p->part[j]->fs == NULL || p->part[j]->fs->fsname == NULL ||
					p->part[j]->part_fstype == FS_TYPE_SWAP || p->part[j]->part_fstype == FS_TYPE_UNKNOWN) {
					continue;
				}
#ifdef DEFAULT_GRUB_BOOT
				sprintf(load, "/dev/fs/%s@%s%c/EFI/BOOT/BOOTLOONGARCH64.EFI", p->part[j]->fs->fsname, startup_menu[current], 'a' + j);
				if (!legacy_boot++ && check_config((char *)load) == 1) {
					sprintf(path, "bootefi %s", load);
					if( do_cmd(path) == 0) {
						return 0;
					}
				}
#endif
		        sprintf(load, "/dev/fs/%s@%s%c/boot/boot.cfg", p->part[j]->fs->fsname, startup_menu[current], 'a' + j);
		        if (check_config((char *)load) == 1)
		            break;
		        sprintf(load, "/dev/fs/%s@%s%c/boot.cfg", p->part[j]->fs->fsname, startup_menu[current], 'a' + j);
		        if (check_config((char *)load) == 1)
		            break;
		    }
		    if (j == part_num) {
		        printf("Note: not find the boot.cfg in %s. please check the filesystem!\n",startup_menu[current]);
		    } else {
		        sprintf(path, "bl -d ide %s", load);
				if( do_cmd(path) == 0) {
					return 0;
				}
		    }
		}
	}
	return 1;
}

int check_user_password()
{
	char buf[50];
	struct termio tty;
	int i;
	char c;
	if (!pwd_exist() || !pwd_is_set("user"))
		return 0;

	for (i = 0; i < 2; i++) {
		ioctl(i, TCGETA, &tty);
		tty.c_lflag &= ~ECHO;
		ioctl(i, TCSETAW, &tty);
	}


	printf("\nPlease input user password:");
loop0:
	for (i = 0; i < 50; i++) {
		c = getchar();
		if (c != '\n' && c!='\r') {
			printf("*");
			buf[i] = c;
		} else {
			buf[i] = '\0';
			break;
		}
	}

	if (!pwd_cmp("user", buf)) {
		printf("\nPassword error!\n");
		printf("Please input user password:");
		goto loop0;
	}

	for (i = 0; i < 2; i++) {
		tty.c_lflag |=  ECHO;
		ioctl(i,TCSETAW,&tty);
	}

	return 0;
}

int check_admin_password()
{
	char buf[50];
	struct termio tty;
	int i;
	char c;
	if (!pwd_exist() || !pwd_is_set("admin"))
		return 0;

	for (i = 0; i < 2; i++) {
		ioctl(i,TCGETA,&tty);
		tty.c_lflag &= ~ ECHO;
		ioctl(i,TCSETAW,&tty);
	}


	printf("\nPlease input admin password:");
loop1:
	for (i = 0; i < 50; i++) {
		c = getchar();
		if (c != '\n' && c!= '\r') {
			printf("*");
			buf[i] = c;
		} else {
			buf[i] = '\0';
			break;
		}
	}

	if (!pwd_cmp("admin", buf)) {
		printf("\nPassword error!\n");
		printf("Please input admin password:");
		goto loop1;
	}


	for (i = 0; i < 2; i++) {
		tty.c_lflag |=  ECHO;
		ioctl(i, TCSETAW, &tty);
	}

	return 0;
}


int check_sys_password()
{
	char buf[50];
	struct termio tty;
	int i;
	char c;
	int count=0;
	if (!pwd_exist() || !pwd_is_set("sys"))
		return 0;

	for (i = 0; i < 6; i++) {
		ioctl(i,TCGETA,&tty);
		tty.c_lflag &= ~ ECHO;
		ioctl(i,TCSETAW,&tty);
	}

	printf("\nPlease input sys password:");
loop1:
	for (i = 0; i < 50; i++) {
		c = getchar();
		if (c != '\n' && c != '\r') {
			printf("*");
			buf[i] = c;
		} else {
			buf[i]='\0';
			break;
		}
	}

	if (!pwd_cmp("sys", buf)) {
		printf("\nPassword error!\n");
		printf("Please input sys password:");
		count++;
		if (count == 3)
			return -1;
		goto loop1;
	}


	for (i = 0; i < 6; i++) {
		tty.c_lflag |=  ECHO;
		ioctl(i, TCSETAW, &tty);
	}

	return 0;
}

/*
 *  Main interactive command loop
 *  -----------------------------
 *
 *  Clean up removing breakpoints etc and enter main command loop.
 *  Read commands from the keyboard and execute. When executing a
 *  command this is where <crtl-c> takes us back.
 */
extern void (*__scr_clear)();
extern void (*__set_cursor)(unsigned char x,unsigned char y);
void __gccmain(void);
void __gccmain(void)
{
}

int
main()
{
	char prompt[32];
	int i;

	if(cmd_main_mutex == 2)
		;
	else {
		unsigned char *bootmenu_envstr;
		if((bootmenu_envstr = getenv("ShowBootMenu")) && (strcmp("no",bootmenu_envstr) == 0))
			;

		else {
			bios_available = 1;//support usb_kbd in bios
			load_menu_list();
			bios_available = 0;
		}
	}
	if (setjmp(jmpb)) {
		/* Bailing out, restore */
		closelst(0);
		ioctl(STDIN, TCSETAF, &consterm);
		printf(" break!\r\n");
	}

#ifdef INET
	signal(SIGINT, pmon_intr);
#else
	ioctl(STDIN, SETINTR, jmpb);
#endif

#if NMOD_DEBUGGER > 0
	rm_bpts();
#endif

	{
		static int run = 0;
		char *s;
		int ret = -1;
		char buf[LINESZ];
		if (!run)
		{
			run = 1;

#ifndef NO_VIDEO
			__console_init();
			logo_occupy_scr(0);
			__scr_clear();
			__set_cursor(0, 0);
#endif
#ifdef AUTOLOAD

			if (getenv("FR") == NULL) {
				setenv("FR","0");
				setenv("installdelay", "5");
				//setenv("autoinstall", "/dev/fs/iso9660@cd0/vmlinuxb");
				setenv("autoinstall", "/dev/fs/ext2@usb0/vmlinuxboot");
				//setenv("rd", "/sbin/init");
				//autoinstall("/dev/fs/iso9660@cd0/vmlinuxb");
				//autoinstall("/dev/fs/ext2@usb0/vmlinuxboot");
			}
			if (strcmp (getenv("FR"),"0") == 0) {
				unsetenv("al");
				unsetenv("al1");
				unsetenv("append");
				printf("==WARN: First Run\n==WARN: Setup the default boot configure\n");
				setenv("FR", "1");
			}
			//autoinstall("/dev/fs/iso9660@cd0/vmlinuxb");
			//autoinstall("/dev/fs/iso9660@cd0/vmlinuxb");
			//autoinstall("/dev/fs/ext2@usb0/vmlinuxboot");

			if (getenv("al") == NULL) /* CDROM autoload */ {
				setenv("al","/dev/fs/iso9600@cd0/boot/vmlinux");
				setenv("append","console=tty root=/dev/sda1");
			}
			if (getenv("al1") == NULL) { /* HARDDISK autoload */
				setenv("al1","/dev/fs/ext2@wd0/boot/vmlinux");
				setenv("append","console=tty root=/dev/sda1");
			}

			//autoload("/dev/fs/ext2@wd0/boot/vmlinux");
			//autorun("g console=tty root=/dev/sda1 rw");
			//cmd_showwindows();
			//do_cmd("load /dev/fs/ext2@wd0/boot/vmlinux");
			//do_cmd("g console=tty root=/dev/sda1");
#if 1
			s = getenv ("al1");
			ret = autoload (s);
			if (ret == 1) {
				s = getenv("al");
				ret = autoload (s);
			}
#endif

#else
			//			s = getenv ("autoboot");
			//			autorun (s);
#endif
		}
	}

	//#endif
	while(1) {
#if 0
		while(1) {
			char c;
			int i;
			i = term_read(0, &c, 1);
			printf("haha: %d, %02x \n", i, c);
		}
#endif
		strncpy (prompt, getenv ("prompt"), sizeof(prompt));

#if NCMD_HIST > 0
		if (strchr(prompt, '!') != 0) {
			char tmp[8], *p;
			p = strchr(prompt, '!');
			strdchr(p);	/* delete the bang */
			sprintf(tmp, "%d", histno);
			stristr(p, tmp);
		}
#endif

		if(cmd_main_mutex == 2) {
			cmd_main_mutex = 1;
			printf(" break!\r\n");
		}

		printf("%s", prompt);
#if NCMD_HIST > 0
		get_cmd(line);
#else
		get_line(line, 0);
#endif
		do_cmd(line);
		console_state(1);
	}
	DeviceRelease();
	return(0);
}

char * expand(char *cmdline);
#ifdef AUTOLOAD
static int autoload(char *s)
{
	char buf[LINESZ + 8];
	char *pa;
	char *rd;
	unsigned int dly, lastt;
	unsigned int cnt;
	struct termio sav;
	int ret = -1;
	int i;

	if(s != NULL  && strlen(s) != 0) {
		char *d = getenv ("bootdelay");
		if(!d || !atob (&dly, d, 10) || dly < 0 || dly > 99) {
			dly = 8;
		}

		SBD_DISPLAY ("AUTO", CHKPNT_AUTO);
		printf("Press <Enter> to execute loading image:%s\n",s);
		printf("Press any other key to abort.\n");
		ioctl (STDIN, CBREAK, &sav);
 	/*clear STDIN buffer*/
		for(i = 0; i < 1000; i++){
			ioctl (STDIN, FIONREAD, &cnt);
			if(cnt) {
                getchar();
			} else {
                break;
            }
			delay(100);
		}        

		lastt = 0;
		if (!dly)
			ioctl (STDIN, FIONREAD, &cnt);
		else
			do {
				//delay(1000000);
				printf ("\b\b%02d", --dly);
				//printf (".", --dly);
				for (i = 0; i < 9000; i++) {
					ioctl(STDIN, FIONREAD, &cnt);
					if(cnt)
						break;
					delay(100);
				}
			} while (dly != 0 && cnt == 0);

		if (cnt > 0 && strchr("\n\r", getchar())) {
			cnt = 0;
		}

		ioctl (STDIN, TCSETAF, &sav);
		putchar ('\n');

		if (cnt == 0) {
			if (getenv("autocmd")) {
				strncpy(buf, expand(getenv("autocmd")), sizeof(buf));
				do_cmd(buf);
			}
			rd = getenv("rd");
			if (rd != 0) {
				sprintf(buf, "initrd %s", rd);
				if (do_cmd(buf))
					return;
			}

			strcpy(buf, "load ");
			strcat(buf, s);
			if (do_cmd(buf))
				return;
			if ((pa = getenv("append"))) {
				sprintf(buf,"g %s",pa);
			}
			else if ((pa = getenv("karg"))) {
				sprintf(buf,"g %s",pa);
			}
			else {
				pa = getenv("dev");
				strcpy(buf, "g root=/dev/");
				if (pa != NULL && strlen(pa) != 0)
					strcat(buf, pa);
				else
					strcat(buf, "hda1");
				//strcat(buf," console=tty");
				strcat(buf," console=ttyS0,115200 init=/bin/sh rw");
			}
			printf("%s\n",buf);
			delay(100000);
			ret = do_cmd (buf);
		}
	}

	return ret;
}

#else
/*
 *  Handle autoboot execution
 *  -------------------------
 *
 *  Autoboot variable set. Countdown bootdelay to allow manual
 *  intervention. If CR is pressed skip counting. If var bootdelay
 *  is set use the value othervise default to 15 seconds.
 */
static void autorun(char *s)
{
	char buf[LINESZ];
	char *d;
	unsigned int dly, lastt;
	unsigned int cnt;
	struct termio sav;

	if(s != NULL && strlen(s) != 0) {
		d = getenv ("bootdelay");
		if(!d || !atob (&dly, d, 10) || dly < 0 || dly > 99) {
			dly = 15;
		}

		SBD_DISPLAY ("AUTO", CHKPNT_AUTO);
		printf("Autoboot command: \"%.60s\"\n", s);
		printf("Press <Enter> to execute or any other key to abort.\n");
		ioctl(STDIN, CBREAK, &sav);
		lastt = 0;
		dly++;
		do {
#if defined(HAVE_TOD) && defined(DELAY_INACURATE)
			time_t t;
			t = tgt_gettime ();
			if (t != lastt) {
				printf ("\r%2d", --dly);
				lastt = t;
			}
#else
			delay(1000000);
			printf ("\r%2d", --dly);
#endif
			ioctl (STDIN, FIONREAD, &cnt);
		} while (dly != 0 && cnt == 0);

		if (cnt > 0 && strchr("\n\r", getchar())) {
			cnt = 0;
		}

		ioctl (STDIN, TCSETAF, &sav);
		putchar ('\n');

		if (cnt == 0) {
			strcpy (buf, s);
			do_cmd (buf);
		}
	}
}
#endif
static int autoinstall(char *s)
{
	char buf[LINESZ];
	char *pa;
	char *rd;
	unsigned int dly, lastt;
	unsigned int cnt = 0;
	//unsigned int cnt ;
	struct termio sav;
	int ret = -1;
	char c;

	if (s != NULL && strlen(s) != 0) {
		char *d = getenv ("installdelay");
		if (!d || !atob (&dly, d, 10) || dly < 0 || dly > 99) {
			dly = 6;
		}

		SBD_DISPLAY ("AUTO", CHKPNT_AUTO);
		//printf("Press <F2> to execute system installing :%s\n",s);
		//printf("Press <Enter> to execute loading image:%s\n",s);
		printf("Press 'u(usb)' or 'c(cdrom)' to  install image from usb or cdrom:%s\n",s);
		printf("Press any other key to abort.\n");
		ioctl (STDIN, CBREAK, &sav);
		lastt = 0;
		do {
			delay(1000000);
			printf ("\b\b%02d", --dly);
			//printf (".", --dly);
			ioctl (STDIN, FIONREAD, &cnt);
		} while (dly != 0 && cnt == 0);
		//} while (dly != 0);

		//if(cnt > 0! strchr("\0x71", getchar())) {
		//if(cnt > 0 && strchr("\0x71\0x72\0x73", getchar())) {
		//if( cnt > 0 ) {
		if(cnt > 0 && strchr("uUcC", c=getchar())) {
		//if(cnt > 0 && (c=getchar())) {
		//if(cnt > 0) {
		// printf("cnt = %d: input = 0x%08x\n", cnt, getchar());
		//printf("input = 0x%08x\n", c=getchar());
		//putchar();
		ioctl (STDIN, TCSETAF, &sav);
		putchar ('\n');

#if 0
		if(getenv("autoinstall"))
			sprintf(buf, "load %s", getenv("autoinstall"));
		else
			sprintf(buf, "load /dev/fs/iso9660@cd0/vmlinuxb");
#else
		if(c == 'u' || c == 'U')
		{
			//sprintf(buf, "load %s", getenv("autoinstall"));
			//sprintf(buf, "load /dev/fs/ext2@usb0/vmlinuxboot");
			sprintf(buf, "usbinstall");
			ret = do_cmd(buf);
		}
		if(c == 'c' || c == 'C')
		{
			sprintf(buf, "cdinstall");
			ret = do_cmd(buf);
		}
#endif
	}
//	printf( "\n NO <F2> entered in 5 secs\n");
	}
	return ret;
}

extern void get_ec_version(void);
/*
 *  PMON2000 entrypoint. Called after initial setup.
 */
void
dbginit (char *adr)
{
	char	*s;

/*	splhigh();*/


	__init();	/* Do all constructor initialisation */
	SBD_DISPLAY ("ENVI", CHKPNT_ENVI);
	
#ifdef TCM2
	{
		extern int tcm2_autostart(void);
		if (tcm2_autostart() != 0)
			printf("TCM2 automatic startup failed\n");
	}
#endif

#if defined(TCM2) && defined(TCM_ENV_MEASUREMENT)
	{
		extern int tcm2_is_ready(void);
		extern int tcm2_env_auto_measure(void);

		if (tcm2_is_ready())
			tcm2_env_auto_measure();
		else
			printf("TCM is not initialized, skipping environment measurement\n");
	}
#endif
	envinit ();

#if defined(SMP)
	/* Turn on caches unless opted out */
	if (!getenv("nocache"))
		md_cacheon();
#endif

	SBD_DISPLAY ("SBDD", CHKPNT_SBDD);
	tgt_devinit();

#if defined(LS_STR)
	/*if S3, jump to kernel*/
	check_str();
#endif

#ifdef INET
	SBD_DISPLAY ("NETI", CHKPNT_NETI);
	init_net (1);
#endif

#if NCMD_HIST > 0
	SBD_DISPLAY ("HSTI", CHKPNT_HSTI);
	histinit ();
#endif

#if NMOD_SYMBOLS > 0
	SBD_DISPLAY ("SYMI", CHKPNT_SYMI);
	syminit ();
#endif

#ifdef DEMO
	SBD_DISPLAY ("DEMO", CHKPNT_DEMO);
	demoinit ();
#endif

	SBD_DISPLAY ("SBDE", CHKPNT_SBDE);
	initial_sr |= tgt_enable (tgt_getmachtype ());

#ifdef SR_FR
	/* don't confuse naive clients */
#endif
	/* Set up initial console terminal state */
	ioctl(STDIN, TCGETA, &consterm);

#ifndef QUICK_START
#ifdef HAVE_LOGO
	tgt_logo();
#else
	printf ("\n * PMON2000 Professional *");
#endif
	printf ("\nConfiguration [%s,%s", TARGETNAME,
			BYTE_ORDER == BIG_ENDIAN ? "EB" : "EL");
#ifdef INET
	printf (",NET");
#endif
#if NSD > 0
	printf (",SCSI");
#endif
#if NWD > 0
	printf (",IDE");
#endif
	printf ("]\nVersion: %s.\n", vers);
	printf ("Supported loaders [%s]\n", getExecString());
	printf ("Supported filesystems [%s]\n", getFSString());
	printf ("This software may be redistributed under the BSD copyright.\n");

	print_cpu_info();
	print_mem_freq();

	printf ("Memory size %lld MB .\n", memorysize_total);

	tgt_memprint();
#if defined(SMP)
	tgt_smpstartup();
#endif
#endif
	md_clreg(NULL);
	md_setpc(NULL, (int32_t) CLIENTPC);
	md_setsp(NULL, tgt_clienttos ());
	DevicesInit();

#ifdef EFI_BOOT
	/* Initialize system table */
	efi_initialize_system_table();

	/* Initialize EFI drivers */
	if (efi_init_obj_list() != EFI_SUCCESS)
		printf("Error: Cannot initialize EFI sub-system.\n");
#else
	init_systab();
#endif

#ifdef ACPI_SUPPORT
	loongson_acpi_init();
#endif
#ifdef SMBIOS_SUPPORT
	loongson_smbios_init();
#endif
}

/*
 *  closelst(lst) -- Handle client state opens and terminal state.
 */
void
closelst(int lst)
{
	switch (lst) {
	case 0:
		/* XXX Close all LU's opened by client */
		break;

	case 1:
		break;

	case 2:
		/* reset client terminal state to consterm value */
		clntterm = consterm;
		break;
	}
}

/*
 *  console_state(lst) -- switches between PMON2000 and client tty setting.
 */
void
console_state(int lst)
{
	switch (lst) {
	case 1:
		/* save client terminal state and set PMON default */
		ioctl (STDIN, TCGETA, &clntterm);
		ioctl (STDIN, TCSETAW, &consterm);
		break;

	case 2:
		/* restore client terminal state */
		ioctl (STDIN, TCSETAF, &clntterm);
		break;
	}
}

/*************************************************************
 *  dotik(rate,use_ret)
 */
void
dotik (rate, use_ret)
	int             rate, use_ret;
{
static	int             tik_cnt;
static	const char      more_tiks[] = "|/-\\";
static	const char     *more_tik;

	tik_cnt -= rate;
#if NMOD_USB_OHCI
	if(!efi_at_runtime && ohci_index)
		dl_ohci_kbd();
#endif
	if (tik_cnt > 0) {
		return;
	}
	tik_cnt = 256000;
	if (more_tik == 0) {
		more_tik = more_tiks;
	}
	if (*more_tik == 0) {
		more_tik = more_tiks;
	}
	if (use_ret) {
		printf (" %c\r", *more_tik);
	} else {
		printf ("\b%c", *more_tik);
	}
	more_tik++;
}

#if NCMD_MORE == 0
/*
 *  Allow usage of more printout even if more is not compiled in.
 */
int
more (p, cnt, size)
     char           *p;
     int            *cnt, size;
{
	printf("%s\n", p);
	return(0);
}
#endif

/*
 *  Non direct command placeholder. Give info to user.
 */
int 
no_cmd(ac, av)
     int ac;
     char *av[];
{
	printf("Not a direct command! Use 'h %s' for more information.\n", av[0]);
	return (1);
}

/*
 *  Build argument area on 'clientstack' and set up clients
 *  argument registers in preparation for 'launch'.
 *  arg1 = efi flag, arg2 = fdt, arg3 = ver.
 */

void
initstack (ac, av, addenv)
    int ac;
    char **av;
    int addenv;
{
	register_t nsp;
	int	stringlen, i;
	int optind = 1;

	for (i = 0; i < ac; i++) {
		stringlen += strlen(av[i]) + 1;
	}
	stringlen = (stringlen + 7) & ~7;	/* Round to words */

	/*
	 *  Allocate stack and us md code to set args.
	 */
	nsp = md_adjstack(NULL, 0) - stringlen;

	while (optind < ac) {
		strcat (nsp, av[optind++]);
		strcat (nsp, " ");
	}

#ifdef EFI_BOOT
	md_setargs(NULL, EFI_BOOT, nsp, VA_TO_PHYS(&systab), 0);
#else
	md_setargs(NULL, 0, nsp, &systab, 0);
#endif

	envbuild();

#ifdef DTB
#include "target/load_dtb.h"
	setup_dtb(ac, av, nsp);
#endif
	md_setlr(NULL, (register_t)_exit);
}

/* default mem freq print, overrided by board print_mem_freq() */
void __attribute__((weak)) print_mem_freq(void)
{

}

void __attribute__((weak)) print_cpu_info(void)
{
	int  freq;
	char fs[10], *fp;

	tgt_machprint();

	freq = tgt_pipefreq ();
	sprintf(fs, "%d", freq);
	fp = fs + strlen(fs) - 6;
	fp[3] = '\0';
	fp[2] = fp[1];
	fp[1] = fp[0];
	fp[0] = '.';
	printf (" %s MHz", fs);
}

//inline uint32_t read_c0_count()
uint32_t read_c0_count()
{
	return CPU_GetCOUNT();
}

