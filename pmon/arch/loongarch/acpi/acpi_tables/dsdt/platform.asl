/** @file
  Sample ACPI Platform Driver

  Copyright (c) 2008 - 2011, Intel Corporation. All rights reserved.<BR>
  Copyright (c) 2018 Loongson Technology Corporation Limited (www.loongson.cn).
  All intellectual property rights(Copyright, Patent and Trademark) reserved.

  Any violations of copyright or other intellectual property rights of the Loongson
  Technology Corporation Limited will be held accountable in accordance with the law,
  if you (or any of your subsidiaries, corporate affiliates or agents) initiate directly
  or indirectly any Intellectual Property Assertion or Intellectual Property Litigation:
  (i) against Loongson Technology Corporation Limited or any of its subsidiaries or corporate affiliates,
  (ii) against any party if such Intellectual Property Assertion or Intellectual Property Litigation arises
  in whole or in part from any software, technology, product or service of Loongson Technology Corporation Limited
  or any of its subsidiaries or corporate affiliates, or (iii) against any party relating to the Software.

  THIS SOFTWARE IS PROVIDED BY THE AUTHOR "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION).

**/

Scope (\_SB)
{
#ifdef  ls2k2000
  Device (GPO0)
  {
    Name (_HID, "LOON0002")  // _HID: Hardware ID
    Name (_ADR, Zero)  // _ADR: Address
    Name (_UID, One)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x00000000100E0000, // Range Minimum
        0x00000000100E0BFF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000C00, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Exclusive, ,, )
      {
       0x0000007C,
       0x0000007D,
       0x0000007E,
       0x0000007F,
       0x0000007B,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x06)
      {
        Package (0x02)
        {
          "conf_offset",
          0x800
        },

        Package (0x02)
        {
          "out_offset",
          0x900
        },

        Package (0x02)
        {
          "in_offset",
          0xa00
        },

        Package (0x02)
        {
          "gpio_base",
          32
        },

        Package (0x02)
        {
          "ngpios",
          64
        },

        Package (0x02)
        {
          "gsi_idx_map",
          Package (0x40)
          {
            0x0000,
            0x0001,
            0x0002,
            0x0003,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0000,
            0x0001,
            0x0002,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0003,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
			0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004
          }
        }
      }
    })
  }

  Device (GPO1)
  {
    Name (_HID, "LOON0007")  // _HID: Hardware ID
    Name (_ADR, Zero)  // _ADR: Address
    Name (_UID, 1)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x000000001fe00500, // Range Minimum
        0x000000001fe00537, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000038, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Exclusive, ,, )
      {
       0x00000010,
       0x00000011,
       0x00000012,
       0x00000013,
       0x00000014,
       0x00000015,
       0x00000016,
       0x00000017,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x07)
      {
        Package (0x02)
        {
          "conf_offset",
          0x0
        },

        Package (0x02)
        {
          "out_offset",
          0x08
        },

        Package (0x02)
        {
          "in_offset",
          0x0c
        },

        Package (0x02)
        {
          "gpio_base",
          0x00
        },

	Package (0x02)
        {
          "in_start_bit",
          0
        },

        Package (0x02)
        {
          "ngpios",
          32
        },

        Package (0x02)
        {
          "gsi_idx_map",
          Package (0x20)
          {
            0x0000,
            0x0001,
            0x0002,
            0x0003,
            0x0004,
            0x0005,
            0x0006,
            0x0007,
	    0x0000,
            0x0001,
            0x0002,
            0x0003,
            0x0004,
            0x0005,
            0x0006,
            0x0007,
		0x0000,
            0x0001,
            0x0002,
            0x0003,
            0x0004,
            0x0005,
            0x0006,
            0x0007,
		0x0000,
            0x0001,
            0x0002,
            0x0003,
            0x0004,
            0x0005,
            0x0006,
            0x0007
          }
        }
      }
    })
  }
 #else
   Device (GPO0)
  {
    Name (_HID, "LOON0002")  // _HID: Hardware ID
    Name (_ADR, Zero)  // _ADR: Address
    Name (_UID, One)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x00000000100E0000, // Range Minimum
        0x00000000100E0BFF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000C00, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Exclusive, ,, )
      {
       0x0000007C,
       0x0000007D,
       0x0000007E,
       0x0000007F,
       0x0000007B,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x06)
      {
        Package (0x02)
        {
          "conf_offset",
          0x800
        },

        Package (0x02)
        {
          "out_offset",
          0x900
        },

        Package (0x02)
        {
          "in_offset",
          0xa00
        },

        Package (0x02)
        {
          "gpio_base",
          16
        },

        Package (0x02)
        {
          "ngpios",
          57
        },

        Package (0x02)
        {
          "gsi_idx_map",
          Package (0x39)
          {
            0x0000,
            0x0001,
            0x0002,
            0x0003,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004,
            0x0004
          }
        }
      }
    })
  }

#ifdef LS3A_GPIO
  Device (GPO1)
  {
    Name (_HID, "LOON0007")  // _HID: Hardware ID
    Name (_ADR, Zero)  // _ADR: Address
    Name (_UID, 1)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x000000001fe00500, // Range Minimum
        0x000000001fe00517, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000018, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Exclusive, ,, )
      {
       0x00000010,
       0x00000011,
       0x00000012,
       0x00000013,
       0x00000014,
       0x00000015,
       0x00000016,
       0x00000017,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x07)
      {
        Package (0x02)
        {
          "conf_offset",
          0x0
        },

        Package (0x02)
        {
          "out_offset",
          0x08
        },

        Package (0x02)
        {
          "in_offset",
          0x0c
        },

        Package (0x02)
        {
          "gpio_base",
          0x00
        },

	Package (0x02)
        {
          "in_start_bit",
          0
        },

        Package (0x02)
        {
          "ngpios",
          16
        },

        Package (0x02)
        {
          "gsi_idx_map",
          Package (0x10)
          {
            0x0000,
            0x0001,
            0x0002,
            0x0003,
            0x0004,
            0x0005,
            0x0006,
            0x0007,
	    0x0000,
            0x0001,
            0x0002,
            0x0003,
            0x0004,
            0x0005,
            0x0006,
            0x0007,
          }
        }
      }
    })
  }
#endif
#endif

  Device (COMA)
  {
    Name (_HID, "PNP0501" /* 16550A-compatible COM Serial Port */)  // _HID: Hardware ID
    Name (_UID, 0x0)  // _UID: Unique ID
    Name (_CCA, One)  // _CCA: Cache Coherency Attribute
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x000000001fe001e0, // Range Minimum
        0x000000001fe001e7, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        26,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x01)
      {
        Package (0x02)
        {
          "clock-frequency",
          100000000
        }
      }
    })
  }
#ifndef LS7A_CAN 
  Device (COMB)
  {
    Name (_HID, "PNP0501" /* 16550A-compatible COM Serial Port */)  // _HID: Hardware ID
    Name (_UID, 0x1)  // _UID: Unique ID
    Name (_CCA, One)  // _CCA: Cache Coherency Attribute
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010080000, // Range Minimum
        0x00000000100800FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        72,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x01)
      {
        Package (0x02)
        {
          "clock-frequency",
          50000000
        }
      }
    })
  }
  Device (COMC)
  {
    Name (_HID, "PNP0501" /* 16550A-compatible COM Serial Port */)  // _HID: Hardware ID
    Name (_UID, 0x2)  // _UID: Unique ID
    Name (_CCA, One)  // _CCA: Cache Coherency Attribute
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010080100, // Range Minimum
        0x00000000100801FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        72,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x01)
      {
        Package (0x02)
        {
          "clock-frequency",
          50000000
        }
      }
    })
  }
  Device (COMD)
  {
    Name (_HID, "PNP0501" /* 16550A-compatible COM Serial Port */)  // _HID: Hardware ID
    Name (_UID, 0x3)  // _UID: Unique ID
    Name (_CCA, One)  // _CCA: Cache Coherency Attribute
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010080200, // Range Minimum
        0x00000000100802FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        72,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x01)
      {
        Package (0x02)
        {
          "clock-frequency",
          50000000
        }
      }
    })
  }
  Device (COME)
  {
    Name (_HID, "PNP0501" /* 16550A-compatible COM Serial Port */)  // _HID: Hardware ID
    Name (_UID, 0x4)  // _UID: Unique ID
    Name (_CCA, One)  // _CCA: Cache Coherency Attribute
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010080300, // Range Minimum
        0x00000000100803FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        72,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x01)
      {
        Package (0x02)
        {
          "clock-frequency",
          50000000
        }
      }
    })
  }
#endif
#ifdef LS7A_CAN 
  Device (CAN0)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x0)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010080400, // Range Minimum
        0x00000000100804FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x01)
      {
        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
  Device (CAN1)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x1)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010080500, // Range Minimum
        0x00000000100805FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x01)
      {
        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
  Device (CAN2)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x2)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010080600, // Range Minimum
        0x00000000100806FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x01)
      {
        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
  Device (CAN3)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x3)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010080700, // Range Minimum
        0x00000000100807FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x01)
      {
        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
#endif
#ifdef LOONGSON_2K2000
  Device (CAN0)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x0)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010081000, // Range Minimum
        0x00000000100810FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x02)
      {
	Package (0x02)
        {
           "compatible",
           "ls2k1500,sja1000"
        },

        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
  Device (CAN1)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x1)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010081100, // Range Minimum
        0x00000000100811FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x02)
      {
	Package (0x02)
	{
	  "compatible",
	  "ls2k1500,sja1000"
	},

        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
  Device (CAN2)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x2)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010081200, // Range Minimum
        0x00000000100812FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x02)
	{
	Package (0x02)
    	{
          "compatible",
          "ls2k1500,sja1000"
        },

        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
  Device (CAN3)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x3)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010081300, // Range Minimum
        0x00000000100813FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x02)
      {
	Package (0x02)
        {
          "compatible",
          "ls2k1500,sja1000"
        },

        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
Device (CAN4)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x4)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010081400, // Range Minimum
        0x00000000100814FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x02)
      {
	Package (0x02)
        {
          "compatible",
          "ls2k1500,sja1000"
        },

        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
Device (CAN5)
  {
    Name (_HID, "LOON0009") // _HID: Hardware ID
    Name (_UID, 0x5)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010081500, // Range Minimum
        0x00000000100815FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        75,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x02)
      {
	Package (0x02)
        {
          "compatible",
          "ls2k1500,sja1000"
        },

        Package (0x02)
        {
          "clock",
          50000000
        }
      }
    })
  }
#endif
  Device (RTC)
  {
    Name (_HID, "LOON0001" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x00000000100d0100, // Range Minimum
        0x00000000100d01FF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Exclusive, ,, )
      {
        116,
      }
    })
  }
#ifdef LOONGSON_2K2000
  Device (I2C0)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x0)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x000000001fe00120, // Range Minimum
        0x000000001fe00127, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        24,
      }
    })
  }
  Device (I2C1)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x1)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x000000001fe00130, // Range Minimum
        0x000000001fe00137, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        25,
      }
    })
  }
  Device (I2C2)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x2)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010090000, // Range Minimum
        0x0000000010090007, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        73,
      }
    })
  }
  Device (I2C3)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x3)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010090100, // Range Minimum
        0x0000000010090107, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        73,
      }
    })
  }

  Device (SE)
  { 
    Name (_HID, "PRP0001") // _HID: Hardware ID
    Name (_UID, 0x0)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
	Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        73,
	106
      }
    })

	Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
	{
	 ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
	 Package (0x01)
         {
            Package (0x02)
            {
              "compatible",
              "ls,ls2k-se"
            }
	  }
	})
  }
#else
  Device (I2C0)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x0)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010090000, // Range Minimum
        0x0000000010090007, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        73,
      }
    })
  }
  Device (I2C1)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x1)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010090100, // Range Minimum
        0x0000000010090107, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        73,
      }
    })
  }
  Device (I2C2)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x2)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010090200, // Range Minimum
        0x0000000010090207, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        73,
      }
    })
  }
  Device (I2C3)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x3)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010090300, // Range Minimum
        0x0000000010090307, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        73,
      }
    })
  }
  Device (I2C4)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x4)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000010090400, // Range Minimum
        0x0000000010090407, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000008, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        73,
      }
    })
  }
  Device (I2C5)
  {
    Name (_HID, "LOON0004" /* AT Real-Time Clock */)  // _HID: Hardware ID
    Name (_UID, 0x5)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
          0x0000000000000000, // Granularity
          0x0000000010090500, // Range Minimum
          0x0000000010090507, // Range Maximum
          0x0000000000000000, // Translation Offset
          0x0000000000000008, // Length
          ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        73,
      }
    })
  }
#endif
#ifdef USE_I2S_FUNCTION
  Device(CODC)
  {
	/*Name (_HID,"ESSX8388") */
    Name (_HID,"ESSX8323")
    Name (_CRS,ResourceTemplate()
    {
      I2CSerialBus(0x0011, ControllerInitiated, 100000, AddressingMode7Bit,
        "\\_SB.I2C0", 0, ResourceConsumer, , )
    })
  }
  Device (PCA2)
  {
    Name (_HID, "PRP0001")  // _HID: Hardware ID
    Name (_UID, 0x02)  // _UID: Unique ID
	Name (_DEP, Package() {\_SB.PCA0})
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
        ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"), /* Device Properties for _DSD */
        Package (0x02)
        {
            Package (0x02)
            {
                "compatible",
                "loongson,ls-pcm-audio"
            },
            Package (0x02)
            {
                "dma-mask",
                0xFFFFFFFF
            }
        }
    })
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        120,121
      }
    })
  }
  Device (PCA1)
  {
    Name (_HID, "PRP0001")  // _HID: Hardware ID
    Name (_UID, One)  // _UID: Unique ID
	Name (_DEP, Package() {\_SB.PCA0})
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
        ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"), /* Device Properties for _DSD */
        Package (0x02)
        {
            Package (0x02)
            {
                "compatible",
                "loongson,ls-i2s"
            },
            Package (0x02)
            {
                "clock-frequency",
#ifdef LOONGSON_2K2000
                1000000000	//core_freq
#else
                50000000
#endif
            }
        }
    })
  }
  Device (PCA0)
  {
    Name (_HID, "PRP0001")  // _HID: Hardware ID
    Name (_UID, Zero)  // _UID: Unique ID
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
        ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"), /* Device Properties for _DSD */
        Package (0x01)
        {
            Package (0x02)
            {
                "compatible",
                "loongson,ls-sound"   /* ls-sound.c */
            }
        }
    })
  }
#endif
  Device (PWM0)
  {
    Name (_HID, "LOON0006") // _HID: Hardware ID
    Name (_UID, 0x0)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x00000000100a0000, // Range Minimum
        0x00000000100a000F, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000010, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        88,
      }
    })
  }
  Device (PWM1)
  {
    Name (_HID, "LOON0006") // _HID: Hardware ID
    Name (_UID, 0x1)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x00000000100a0100, // Range Minimum
        0x00000000100a010F, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000010, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        89,
      }
    })
  }
  Device (PWM2)
  {
    Name (_HID, "LOON0006") // _HID: Hardware ID
    Name (_UID, 0x2)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x00000000100a0200, // Range Minimum
        0x00000000100a020F, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000010, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        90,
      }
    })
  }
  Device (PWM3)
  {
    Name (_HID, "LOON0006") // _HID: Hardware ID
    Name (_UID, 0x3)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x00000000100a0300, // Range Minimum
        0x00000000100a030F, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000010, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        91,
      }
    })
  }
  Device (NIPM)
  {
    Name (_HID, "IPI0001")  // _HID: Hardware ID
    Name(_STR, Unicode("IPMI_KCS"))
    Method (_CRS, 0, NotSerialized)  // _CRS: Current Resource Settings
    {
      Return (ResourceTemplate ()
      {
        IO (Decode16,
        0x0CA2,             // Range Minimum
        0x0CA2,             // Range Maximum
        0x00,               // Alignment
        0x01,               // Length
        )
        IO (Decode16,
        0x0CA3,             // Range Minimum
        0x0CA3,             // Range Maximum
        0x00,               // Alignment
        0x01,               // Length
        )
      })
    }

    Method (_IFT, 0, NotSerialized)  // _IFT: IPMI Interface Type
    {
      Return (0x01)
    }

    Method (_SRV, 0, NotSerialized)  // _SRV: IPMI Spec Revision
    {
      Return (0x0200)
    }
  }

#ifdef LOONGSON_LS2K
  Device (SDIO)
  {
    Name (_HID, "PRP0001") // _HID: Hardware ID
    Name (_UID, 0x0)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000079991000, // Range Minimum
        0x00000000799910ff, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        114,
      }
      // Interrupt for the device
      GpioInt (Edge, ActiveLow, Shared, PullNone, 0x0000,
          "\\_SB.GPO0", 0x00, ResourceConsumer,,)  { 2 } 
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x08)
      {
        Package (0x02) { "compatible", "loongson,ls2k_sdio_1.2" },
        Package (0x02) { "dma-mask", 0xffffffffffffffff },
        Package (0x02) { "clock-frequency", 100000000 },
        Package (0x02) { "bus-width", 0x8 },
        Package (0x02) { "cap-sd-highspeed", 0x0 },
        Package (    ) { "cd-gpio", Package() {^SDIO, 0, 0, 0 } },
        Package (0x02) { "no-mmc", 0x0 },
        Package (0x02) { "no-sdio", 0x0 }
      }
    })
  }

  Device (EMMC)
  {
    Name (_HID, "PRP0001") // _HID: Hardware ID
    Name (_UID, 0x0)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x0000000079990000, // Range Minimum
        0x00000000799900ff, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000000100, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        115,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x08)
      {
        Package (0x02) { "compatible", "loongson,ls2k_sdio_1.2" },
        Package (0x02) { "dma-mask", 0xffffffffffffffff },
        Package (0x02) { "clock-frequency", 100000000 },
        Package (0x02) { "bus-width", 0x8 },
        Package (0x02) { "cap-mmc-highspeed", 0x0 },
        Package (0x02) { "mmc-hs200-1_8v", 0x0 },
        Package (0x02) { "no-sd", 0x0 },
        Package (0x02) { "no-sdio", 0x0 }
      }
    })
  }
#endif

#if (defined(LOONGSON_3A6000) || defined(LOONGSON_3C6000)) && defined(LS_SE_ENABLE)
  Device (SE)
  {
    Name (_HID, "LOON0011")  // _HID: Hardware ID
    Name (_UID, Zero)  // _UID: Unique ID
    Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
    {
      QWordMemory (ResourceConsumer, PosDecode, MinFixed, MaxFixed, NonCacheable, ReadWrite,
        0x0000000000000000, // Granularity
        0x00000C00E0000000, // Range Minimum
        0x00000C00E0000FFF, // Range Maximum
        0x0000000000000000, // Translation Offset
        0x0000000000001000, // Length
        ,, , AddressRangeMemory, TypeStatic)
      Interrupt (ResourceConsumer, Level, ActiveHigh, Shared, ,, )
      {
        0x00000020,
        0x00000021,
        0x00000022,
        0x00000023,
        0x00000024,
        0x00000025,
        0x00000026,
        0x00000027,
      }
    })
    Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
    {
      ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
      Package (0x02)
      {
        Package (0x02)
        {
          "compatible",
          "loongson-se"
        },
        Package (0x02)
        {
          "dmam_size",
          0x00800000
        }
      }
    })
    Device (SDF)
    {
      Name (_HID, "LOON0012")  // _HID: Hardware ID
      Name (_UID, One)  // _UID: Unique ID
      Name (_DSD, Package (0x02)  // _DSD: Device-Specific Data
      {
        ToUUID ("daffd814-6eba-4d8c-8a91-bc9bbf4aa301") /* Device Properties for _DSD */,
        Package (0x01)
        {
          Package (0x02)
          {
            "channel",
            0x07
          }
        }
      })
    }
  }
#endif
}
