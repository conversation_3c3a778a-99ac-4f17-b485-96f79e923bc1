/* $Id: disassemble.c,v 1.1.1.1 2006/09/14 01:59:08 root Exp $ */

/*
 * Copyright (c) 2001-2002 Opsycon AB  (www.opsycon.se / www.opsycon.com)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *	notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *	notice, this list of conditions and the following disclaimer in the
 *	documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *	must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *	derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */
#undef FLOATINGPT
#include <stdio.h>
#include <termio.h>
#ifdef _KERNEL
#undef _KERNEL
#include <sys/ioctl.h>
#define _KERNEL
#else
#include <sys/ioctl.h>
#endif
#include <string.h>
#include <ctype.h>
#include <stdlib.h>
#include <machine/frame.h>
#include "pmon.h"
#include <stdarg.h>

#define	REGREG	((register_t *)cpuinfotab[whatcpu])
char	*searching = "Searching.. ";

int md_disassemble(int argc,char **argv);
static int cmd_syms(int argc, char **argv);

const Optdesc	 l_opts[] =
{
	{"-b", "list only branches"},
	{"-c", "list only calls"},
	{"-t", "list trace buffer"},
	{"-r", "show register values with trace"},
	{0}};


/*----------------------------------------------------------------*/

int
md_stacktrace(ac, av)
	int ac;
	char **av;
{
extern int optind;
	int vflag = 0;
	int c, siz, cnt;
	void *addr;

	optind = 0;
	while ((c = getopt (ac, av, "v")) != EOF) {
		switch (c) {
		case 'v':
			vflag++;
			break;

		default:
			return(-1);
		}
	}
	cnt = siz = moresz;
	if (optind < ac) {
		if (!get_rsa(&cnt, av[optind++])) {
			return(-1);
		}
		siz = 0;
	}
	if (optind != ac) {
		return(-1);
	}

	ioctl (STDIN, CBREAK, NULL);

	addr = (void *)cpuinfotab[whatcpu]->sp;

	while (addr != NULL) {
		void *nextframe = (void *)load_word(addr);
		void *pc = (void *)load_word((void *)(int)addr + 4);
		char *p = prnbuf;
		int framesize = (int)nextframe - (int)addr;

		if (nextframe == 0) {
			framesize = 0;
		}

		if (!adr2symoff (p, (u_int32_t)pc, 24)) {
			sprintf(p, "		  0x%08x", pc);
		}
		p += strlen(p);
		/* XXX Wind up saved arg regs and print ? */
		/* XXX Useful? Well most code is optimized... */

		if (vflag) {
			p += sprintf(p, " frame=0x%08x size=%-5d",
						nextframe, framesize);
		}
		if (more(prnbuf, &cnt, siz)) {
			break;
		}
		if (addr == nextframe) {
			more("end of stack or selfpointing!", &cnt, siz);
			break;
		}
		addr = nextframe;
	}
	return(0);
}

void *
md_dumpframe (void *pframe)
{
	int nextframe;
	int lr;
	int *access;

	access = (int *)(pframe);
	nextframe = load_word(access);

	access = (int *)(nextframe+4);
	lr = load_word(access);

	printf("lr %x fp %x nfp %x\n", lr, pframe, nextframe);

	return((void *)nextframe);
}
/*
 *	  Frame tracing.
 */
void
md_do_stacktrace(addr, have_addr, count, modif)
	void *addr;
	int have_addr;
	int count;
	char *modif;
{
	void *xadr;
	__asm__ volatile(" or %0, $r3, $r0\n" : "=r"(xadr));
	if (have_addr == -1) {	/* Stacktrace ourself */
		addr = xadr;
	}
	else if (have_addr == 0) {
		addr = (void *)cpuinfotab[whatcpu]->sp;
	}
#ifdef DUMP_FRAME
	while (addr != 0) {
		addr = md_dumpframe(addr);
	}
#endif
}

/*
 *  Command table registration
 *  ==========================
 */
extern const Optdesc md_r_opts[];

static const Cmd MDebugCmd[] =
{
	{"Debugger"},
	{"r",	   "[reg* [val|field val]]",
			md_r_opts,
			"display/set register",
			md_registers, 1, 4, CMD_REPEAT},
	{"l",	   "[-bct][adr [cnt]]",
			l_opts,
			"list (disassemble) memory",
			md_disassemble, 1, 5, CMD_REPEAT},
	{"bt",	  "[-v] [cnt]",
			0,
			"stack backtrace",
			md_stacktrace, 1, 3, CMD_REPEAT},
	{"syms",	  "[name|address]",
			0,
			"show symbol",
			cmd_syms, 1, 2, CMD_REPEAT},
	{0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

static void
init_cmd()
{
	cmdlist_expand(MDebugCmd, 1);
}


#ifndef _LOONGARCHDIS_H_
#define _LOONGARCHDIS_H_
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef uint32_t insn_t;

struct loongarch_opcode
{
  const insn_t match;
  const insn_t mask; /* High 1 byte is main opcode and it must be 0xf. */
#define LARCH_INSN_OPC(insn) ((insn & 0xf0000000) >> 28)
  const char * const name;

  /* 
     ACTUAL PARAMETER:

  // BNF with regular expression. 
args : token* end

  // just few char separate 'iden'
token : ','
| '('
| ')'
| iden             // maybe a label (include at least one alphabet), maybe a number, maybe a expr
| regname

regname : '$' iden

iden : [a-zA-Z0-9\.\+\-]+

end : '\0'


FORMAT: A string to describe the format of actual parameter including bit field infomation.
For example, "r5:5,r0:5,sr10:16<<2" matches "$12,$13,12345" and "$4,$7,a_label".
That 'sr' means the instruction may need relocate. '10:16' means bit field of instruction.
In a 'format', every 'escape's can be replaced to 'iden' or 'regname' acrroding to its meaning.
We fill all information needed by disassembing and assembing to 'format'.

  // BNF with regular expression. 
format : escape (literal+ escape)* literal* end
| (literal+ escape)* literal* end

end : '\0'       // Get here means parse end.

  // The intersection between any two among FIRST (end), FIRST (literal) and FIRST (escape) must be empty.
  // So we can build a simple parser.
literal : ','
| '('
| ')'

  // Double '<'s means the real number is the immediate after shifting left.
escape : esc_ch bit_field '<' '<' dec2
| esc_ch bit_field
| esc_ch    // for MACRO. non-macro format must indicate 'bit_field'

  // '|' means to concatenate nonadjacent bit fields 
  // For example, "10:16|0:4" means 
  // "16 bits starting from the 10th bit concatenating with 4 bits starting from the 0th bit".
  // This is to say "[25..10]||[3..0]" (little endian).
b_field : dec2 ':' dec2
| dec2 ':' dec2 '|' bit_field

esc_ch : 's' 'r'   // signed immediate or label need relocate
| 's'       // signed immediate no need relocate
| 'u'       // unsigned immediate
| 'l'       // label needed relocate
| 'r'       // general purpose registers
| 'f'       // FPU registers
| 'v'       // 128 bit SIMD register
| 'x'       // 256 bit SIMD register

dec2 : [1-9][0-9]?
| 0

*/
  const char * const format;

  /*
MACRO: Indicate how a macro instruction expand for assembling.
The main is to replace the '%num'(means the 'num'th 'escape' in 'format') in 'macro' string to get the real instruction.
As for marco insn "b" in MIPS, we can say its name is "b", format is "l", macro is "j %1". So "b 3f" will be expanded to "j 3f".
As for marco insn "li" in MIPS, we can say its name is "li", format is "s", macro is "ori"


Maybe need 
*/
  const char * const macro;
  const int *include;
  const int *exclude;

  const unsigned long pinfo;
#define USELESS 0x0l

};

struct hash_control;

struct loongarch_ase
{
  const int *enabled;
  struct loongarch_opcode * const opcodes;
  const int *include;
  const int *exclude;

  /* for disassemble to create main opcode hash table. */
  const struct loongarch_opcode *opc_htab[16];
  unsigned char opc_htab_inited;

  /* for GAS to create hash table. */
  struct hash_control *name_hash_entry;
};

extern int is_unsigned (const char *);
extern int is_signed (const char *);
extern int is_label_with_addend (const char *);
extern int is_label (const char *);
extern int is_branch_label (const char *);

extern int
loongarch_get_bit_field_width (const char *bit_field, char **end);
extern int32_t
loongarch_decode_imm (const char *bit_field, insn_t insn, int si);

#define MAX_ARG_NUM_PLUS_2 9

extern size_t
loongarch_split_args_by_comma (char *args, const char *arg_strs[]);
extern char *
loongarch_cat_splited_strs (const char *arg_strs[]);
extern insn_t
loongarch_foreach_args (const char *format, const char *arg_strs[],
			int32_t (*helper) (char esc1, char esc2,
					   const char *bit_field,
					   const char *arg, void *context),
			void *context);

extern int
loongarch_check_format (const char *format);
extern int
loongarch_check_macro (const char *format, const char *macro);

extern char *
loongarch_expand_macro_with_format_map (const char *format, const char *macro,
					const char * const arg_strs[],
					const char * (*map) (
					  char esc1, char esc2,
					  const char *arg),
					char * (*helper) (
					  const char * const arg_strs[],
					  void *context),
					void *context);
extern char *
loongarch_expand_macro (const char *macro, const char * const arg_strs[],
			char * (*helper) (const char * const arg_strs[],
					  void *context),
			void *context);
extern size_t
loongarch_bits_imm_needed (int64_t imm, int si);

/* 将字符串中指定的连续字符化为1个 */
extern void
loongarch_eliminate_adjacent_repeat_char (char *dest, char c);

/* 下面两个函数计划作为libopcode.a拿出来给一些系统软件反汇编用 */
extern int
loongarch_parse_dis_options (const char *opts_in);
extern void
loongarch_disassemble_one (int64_t pc, unsigned int insn,
			   int (*fprintf_func)
			     (void *stream, const char *format, ...),
			   void *stream);

extern const char * const loongarch_r_normal_name[32];
extern const char * const loongarch_r_lp64_name[32];
extern const char * const loongarch_r_lp64_name1[32];
extern const char * const loongarch_f_normal_name[32];
extern const char * const loongarch_f_lp64_name[32];
extern const char * const loongarch_f_lp64_name1[32];
extern const char * const loongarch_c_normal_name[8];
extern const char * const loongarch_cr_normal_name[4];
extern const char * const loongarch_v_normal_name[32];
extern const char * const loongarch_x_normal_name[32];

extern struct loongarch_ase loongarch_ASEs[];

extern struct loongarch_ASEs_option
{
  int ase_test;
  int ase_fix;
  int ase_float;

  int addrwidth_is_32;
  int addrwidth_is_64;
  int rlen_is_32;
  int rlen_is_64;
  int la_local_with_abs;
  int la_global_with_pcrel;
  int la_global_with_abs;

  int abi_is_lp32;
  int abi_is_lp64;
} LARCH_opts;

extern size_t loongarch_insn_length (insn_t insn);

#ifdef __cplusplus
}
#endif

#endif /* _LOONGARCH_H_ */


#ifndef DIS_ASM_H
#define DIS_ASM_H

typedef int (*fprintf_ftype) (void *, const char*, ...);

enum dis_insn_type
{
  dis_noninsn,			/* Not a valid instruction.  */
  dis_nonbranch,		/* Not a branch instruction.  */
  dis_branch,			/* Unconditional branch.  */
  dis_condbranch,		/* Conditional branch.  */
  dis_jsr,			/* Jump to subroutine.  */
  dis_condjsr,			/* Conditional jump to subroutine.  */
  dis_dref,			/* Data reference instruction.  */
  dis_dref2			/* Two data references in instruction.  */
};

/* This struct is passed into the instruction decoding routine,
   and is passed back out into each callback.  The various fields are used
   for conveying information from your main routine into your callbacks,
   for passing information into the instruction decoders (such as the
   addresses of the callback functions), or for passing information
   back from the instruction decoders to their callers.

   It must be initialized before it is first passed; this can be done
   by hand, or using one of the initialization macros below.  */

typedef struct disassemble_info
{
  fprintf_ftype fprintf_func;
  void *stream;
  void *application_data;

  /* Target description.  We could replace this with a pointer to the bfd,
     but that would require one.  There currently isn't any such requirement
     so to avoid introducing one we record these explicitly.  */
  /* The bfd_flavour.  This can be bfd_target_unknown_flavour.  */
  int flavour;
  /* The bfd_arch value.  */
  int arch;
  /* The bfd_mach value.  */
  unsigned long mach;
  /* Endianness (for bi-endian cpus).  Mono-endian cpus can ignore this.  */
  int endian;
  /* Endianness of code, for mixed-endian situations such as ARM BE8.  */
  int endian_code;
  /* An arch/mach-specific bitmask of selected instruction subsets, mainly
     for processors with run-time-switchable instruction sets.  The default,
     zero, means that there is no constraint.  CGEN-based opcodes ports
     may use ISA_foo masks.  */
  void *insn_sets;

  /* Number of symbols in array.  */
  int num_symbols;
  int symtab_pos;
  int symtab_size;

  /* For use by the disassembler.
     The top 16 bits are reserved for public use (and are documented here).
     The bottom 16 bits are for the internal use of the disassembler.  */
  unsigned long flags;
  /* Set if the disassembler has determined that there are one or more
     relocations associated with the instruction being disassembled.  */
#define INSN_HAS_RELOC	 (1 << 31)
  /* Set if the user has requested the disassembly of data as well as code.  */
#define DISASSEMBLE_DATA (1 << 30)
  /* Set if the user has specifically set the machine type encoded in the
     mach field of this structure.  */
#define USER_SPECIFIED_MACHINE_TYPE (1 << 29)

  /* Use internally by the target specific disassembly code.  */
  void *private_data;

  /* Function which should be called if we get an error that we can't
     recover from.  STATUS is the errno value from read_memory_func and
     MEMADDR is the address that we were trying to read.  INFO is a
     pointer to this struct.  */
  void (*memory_error_func)
    (int status, int64_t memaddr, struct disassemble_info *dinfo);

  /* Function called to print ADDR.  */
  void (*print_address_func)
    (int64_t addr, struct disassemble_info *dinfo);

  /* Function called to determine if there is a symbol at the given ADDR.
     If there is, the function returns 1, otherwise it returns 0.
     This is used by ports which support an overlay manager where
     the overlay number is held in the top part of an address.  In
     some circumstances we want to include the overlay number in the
     address, (normally because there is a symbol associated with
     that address), but sometimes we want to mask out the overlay bits.  */
  int (* symbol_at_address_func)
    (int64_t addr, struct disassemble_info *dinfo);

  /* This variable may be set by the instruction decoder.  It suggests
      the number of bytes objdump should display on a single line.  If
      the instruction decoder sets this, it should always set it to
      the same value in order to get reasonable looking output.  */
  int bytes_per_line;

  /* The next two variables control the way objdump displays the raw data.  */
  /* For example, if bytes_per_line is 8 and bytes_per_chunk is 4, the */
  /* output will look like this:
     00:   00000000 00000000
     with the chunks displayed according to "display_endian". */
  int bytes_per_chunk;
  int display_endian;

  /* Number of octets per incremented target address
     Normally one, but some DSPs have byte sizes of 16 or 32 bits.  */
  unsigned int octets_per_byte;

  /* The number of zeroes we want to see at the end of a section before we
     start skipping them.  */
  unsigned int skip_zeroes;

  /* The number of zeroes to skip at the end of a section.  If the number
     of zeroes at the end is between SKIP_ZEROES_AT_END and SKIP_ZEROES,
     they will be disassembled.  If there are fewer than
     SKIP_ZEROES_AT_END, they will be skipped.  This is a heuristic
     attempt to avoid disassembling zeroes inserted by section
     alignment.  */
  unsigned int skip_zeroes_at_end;

  /* Results from instruction decoders.  Not all decoders yet support
     this information.  This info is set each time an instruction is
     decoded, and is only valid for the last such instruction.

     To determine whether this decoder supports this information, set
     insn_info_valid to 0, decode an instruction, then check it.  */

  char insn_info_valid;		/* Branch info has been set. */
  char branch_delay_insns;	/* How many sequential insn's will run before
				   a branch takes effect.  (0 = normal) */
  char data_size;		/* Size of data reference in insn, in bytes */
  enum dis_insn_type insn_type;	/* Type of instruction */
  int64_t target;		/* Target address of branch or dref, if known;
				   zero if unknown.  */
  int64_t target2;		/* Second target address for dref2 */

  /* Command line options specific to the target disassembler.  */
  const char *disassembler_options;

  /* If non-zero then try not disassemble beyond this address, even if
     there are values left in the buffer.  This address is the address
     of the nearest symbol forwards from the start of the disassembly,
     and it is assumed that it lies on the boundary between instructions.
     If an instruction spans this address then this is an error in the
     file being disassembled.  */
  int64_t stop_vma;

} disassemble_info;

#endif /* ! defined (DIS_ASM_H) */


struct loongarch_ASEs_option LARCH_opts =
{
  .ase_test = 0,
  .ase_fix = 0,
  .ase_float = 0,

  .addrwidth_is_32 = 0,
  .addrwidth_is_64 = 0,
  .rlen_is_32 = 0,
  .rlen_is_64 = 0,
  .la_local_with_abs = 0,
  .la_global_with_pcrel = 0,
  .la_global_with_abs = 0,

  .abi_is_lp32 = 0,
  .abi_is_lp64 = 0,
};

const char * const loongarch_r_normal_name[32] =
{
  "$r0", "$r1", "$r2", "$r3", "$r4", "$r5", "$r6", "$r7",
  "$r8", "$r9", "$r10", "$r11", "$r12", "$r13", "$r14", "$r15",
  "$r16", "$r17", "$r18", "$r19", "$r20", "$r21", "$r22", "$r23",
  "$r24", "$r25", "$r26", "$r27", "$r28", "$r29", "$r30", "$r31",
};

const char * const loongarch_r_lp64_name[32] =
{
  "$zero", "$ra", "$tp", "$sp", "$a0", "$a1", "$a2", "$a3",
  "$a4", "$a5", "$a6", "$a7", "$t0", "$t1", "$t2", "$t3",
  "$t4", "$t5", "$t6", "$t7", "$t8", "$x", "$fp", "$s0",
  "$s1", "$s2", "$s3", "$s4", "$s5", "$s6", "$s7", "$s8",
};

const char * const loongarch_f_normal_name[32] =
{
  "$f0", "$f1", "$f2", "$f3", "$f4", "$f5", "$f6", "$f7",
  "$f8", "$f9", "$f10", "$f11", "$f12", "$f13", "$f14", "$f15",
  "$f16", "$f17", "$f18", "$f19", "$f20", "$f21", "$f22", "$f23",
  "$f24", "$f25", "$f26", "$f27", "$f28", "$f29", "$f30", "$f31",
};

const char * const loongarch_f_lp64_name[32] =
{
  "$fa0", "$fa1", "$fa2", "$fa3", "$fa4", "$fa5", "$fa6", "$fa7",
  "$ft0", "$ft1", "$ft2", "$ft3", "$ft4", "$ft5", "$ft6", "$ft7",
  "$ft8", "$ft9", "$ft10", "$ft11", "$ft12", "$ft13", "$ft14", "$ft15",
  "$fs0", "$fs1", "$fs2", "$fs3", "$fs4", "$fs5", "$fs6", "$fs7",
};

const char * const loongarch_c_normal_name[8] =
{
  "$fcc0", "$fcc1", "$fcc2", "$fcc3", "$fcc4", "$fcc5", "$fcc6", "$fcc7",
};


const char * const loongarch_cr_normal_name[4] =
{
  "$scr0", "$scr1", "$scr2", "$scr3",
};

const char * const loongarch_v_normal_name[32] =
{
  "$vr0", "$vr1", "$vr2", "$vr3", "$vr4", "$vr5", "$vr6", "$vr7",
  "$vr8", "$vr9", "$vr10", "$vr11", "$vr12", "$vr13", "$vr14", "$vr15",
  "$vr16", "$vr17", "$vr18", "$vr19", "$vr20", "$vr21", "$vr22", "$vr23",
  "$vr24", "$vr25", "$vr26", "$vr27", "$vr28", "$vr29", "$vr30", "$vr31",
};

const char * const loongarch_x_normal_name[32] =
{
  "$xr0", "$xr1", "$xr2", "$xr3", "$xr4", "$xr5", "$xr6", "$xr7",
  "$xr8", "$xr9", "$xr10", "$xr11", "$xr12", "$xr13", "$xr14", "$xr15",
  "$xr16", "$xr17", "$xr18", "$xr19", "$xr20", "$xr21", "$xr22", "$xr23",
  "$xr24", "$xr25", "$xr26", "$xr27", "$xr28", "$xr29", "$xr30", "$xr31",
};

#define SOME_INFORMATION 0
const int ALWAYS_EXCLUSION = 1;
static int FIX_FOR_SOME_ARCH = 1;

static struct loongarch_opcode loongarch_test_opcodes[] = {
/* match,    mask,       name, format, macro, include, exclude, pinfo */
{
0, 0, "a insn always excluded",	"r0:5,r5:5,r10:5",
"insn_1 args_1"
"insn_2 args_2"
, 0, &ALWAYS_EXCLUSION, SOME_INFORMATION
},
{
0, 0,
"never disassemble when mask is 0"
"and we only expand marco when mask is 0 for assemble",
"r", "macro expand %1"
, 0, 0, 0
},
{0, 0, "wrong_insn", "r",
"throw_error use_fake_insn.for_example.something_goes_wrong_with_%1"
, 0, 0, 0},

{0, 0, "normal_insn", "r",
"overloading of insn for your arch", &FIX_FOR_SOME_ARCH, 0, 0},
{0, 0, "normal_insn", "l",
"throw_error if_you_dont_want", &FIX_FOR_SOME_ARCH, 0, 0},

{0, 0, "normal_insn", "r", "", 0, 0, 0},
{0, 0, "normal_insn", "l", "", 0, 0, 0},

{0, 0, "dla", "r,la", "la %1,%2", 0, 0, 0},

{0} /* Terminate the list.  */
};

static struct loongarch_opcode loongarch_macro_opcodes[] = {
/* match,    mask,       name, format, macro, include, exclude, pinfo */
{0, 0, "throw_error", "l", "%f", 0, 0, 0},
{0, 0, "li", "r,sc", "%f", 0, 0, 0},
{0, 0, "dli", "r,sc", "%f", 0, 0, 0},

{0, 0, "la", "r,la", "la.global %1,%2", 0, 0, 0},

{0, 0, "la.global", "r,la", "la.pcrel %1,%2", &LARCH_opts.la_global_with_pcrel, 0, 0},
{0, 0, "la.global", "r,r,la", "la.pcrel %1,%2,%3", &LARCH_opts.la_global_with_pcrel, 0, 0},
{0, 0, "la.global", "r,la", "la.abs %1,%2", &LARCH_opts.la_global_with_abs, 0, 0},
{0, 0, "la.global", "r,r,la", "la.abs %1,%3", &LARCH_opts.la_global_with_abs, 0, 0},
{0, 0, "la.global", "r,l", "la.got %1,%2", 0, 0, 0},
{0, 0, "la.global", "r,r,l", "la.got %1,%2,%3", 0, 0, 0},

{0, 0, "la.local", "r,la", "la.abs %1,%2", &LARCH_opts.la_local_with_abs, 0, 0},
{0, 0, "la.local", "r,r,la", "la.abs %1,%3", &LARCH_opts.la_local_with_abs, 0, 0},
{0, 0, "la.local", "r,la", "la.pcrel %1,%2", 0, 0, 0},
{0, 0, "la.local", "r,r,la", "la.pcrel %1,%2,%3", 0, 0, 0},

{0, 0, "la.abs", "r,la",
"lu12i.w %1,%%abs(%2)>>12;"
"ori %1,%1,%%abs(%2)&0xfff;"
, &LARCH_opts.addrwidth_is_32, 0, 0},
{0, 0, "la.abs", "r,la",
"lu12i.w %1,%%abs(%2)<<32>>44;"
"ori %1,%1,%%abs(%2)&0xfff;"
"lu32i.d %1,%%abs(%2)<<12>>44;"
"lu52i.d %1,%1,%%abs(%2)>>52;"
, &LARCH_opts.addrwidth_is_64, 0, 0},

{0, 0, "la.pcrel", "r,la",
"pcaddu12i %1,(%%pcrel(%2)+0x800)<<32>>44;"
"addi.w %1,%1,%%pcrel(%2)+4-((%%pcrel(%2)+(4+0x800))>>12<<12);"
, &LARCH_opts.addrwidth_is_32, 0, 0},

{0, 0, "la.pcrel", "r,la",
"pcaddu12i %1,(%%pcrel(%2)+0x800)>>12;"
"addi.d %1,%1,%%pcrel(%2)+4-((%%pcrel(%2)+(4+0x800))>>12<<12);"
, &LARCH_opts.addrwidth_is_64, 0, 0},
{0, 0, "la.pcrel", "r,r,la",
"pcaddu12i %1,(%%pcrel(%3)-((%%pcrel(%3)+0x80000000)>>32<<32))<<32>>44;"
"ori %2,$r0,(%%pcrel(%3)+4-((%%pcrel(%3)+(4+0x80000000))>>32<<32))&0xfff;"
"lu32i.d %2,(%%pcrel(%3)+(8+0x80000000))<<12>>44;"
"lu52i.d %2,%2,(%%pcrel(%3)+(12+0x80000000))>>52;"
"add.d %1,%1,%2;"
, &LARCH_opts.addrwidth_is_64, 0, 0},

{0, 0, "la.got", "r,l",
"pcaddu12i %1,(%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%gprel(%2)+0x800)<<32>>44;"
"ld.w %1,%1,%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%gprel(%2)-((%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%gprel(%2)+0x800)>>12<<12);"
, &LARCH_opts.addrwidth_is_32, 0, 0},

{0, 0, "la.got", "r,l",
"pcaddu12i %1,(%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%gprel(%2)+0x800)>>12;"
"ld.d %1,%1,%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%gprel(%2)-((%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%gprel(%2)+0x800)>>12<<12);"
, &LARCH_opts.addrwidth_is_64, 0, 0},
{0, 0, "la.got", "r,r,l",
"pcaddu12i %1,(%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%gprel(%3)-((%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%gprel(%3)+0x80000000)>>32<<32))<<32>>44;"
"ori %2,$r0,(%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%gprel(%3)-((%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%gprel(%3)+0x80000000)>>32<<32))&0xfff;"
"lu32i.d %2,(%%pcrel(_GLOBAL_OFFSET_TABLE_+8)+%%gprel(%3)+0x80000000)<<12>>44;"
"lu52i.d %2,%2,(%%pcrel(_GLOBAL_OFFSET_TABLE_+12)+%%gprel(%3)+0x80000000)>>52;"
"ldx.d %1,%1,%2;"
, &LARCH_opts.addrwidth_is_64, 0, 0},

{0, 0, "la.tls.le", "r,la",
"lu12i.w %1,%%tprel(%2)>>12;"
"ori %1,%1,%%tprel(%2)&0xfff"
, &LARCH_opts.addrwidth_is_32, 0, 0},
//{0, 0, "la.tls.le", "r,la",
//"lu12i.w %1,%%tprel(%2)>>12;"
//"ori %1,%1,%%tprel(%2)&0xfff"
//, &LARCH_opts.addrwidth_is_64, 0, 0},
{0, 0, "la.tls.le", "r,la",
"lu12i.w %1,%%tprel(%2)<<32>>44;"
"ori %1,%1,%%tprel(%2)&0xfff;"
"lu32i.d %1,%%tprel(%2)<<12>>44;"
"lu52i.d %1,%1,%%tprel(%2)>>52;"
, &LARCH_opts.addrwidth_is_64, 0, 0},

{0, 0, "la.tls.ie", "r,l",
"pcaddu12i %1,(%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%tlsgot(%2)+0x800)<<32>>44;"
"ld.w %1,%1,%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgot(%2)-((%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgot(%2)+0x800)>>12<<12);"
, &LARCH_opts.addrwidth_is_32, 0, 0},

{0, 0, "la.tls.ie", "r,l",
"pcaddu12i %1,(%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%tlsgot(%2)+0x800)>>12;"
"ld.d %1,%1,%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgot(%2)-((%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgot(%2)+0x800)>>12<<12);"
, &LARCH_opts.addrwidth_is_64, 0, 0},
{0, 0, "la.tls.ie", "r,r,l",
"pcaddu12i %1,(%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%tlsgot(%3)-((%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%tlsgot(%3)+0x80000000)>>32<<32))<<32>>44;"
"ori %2,$r0,(%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgot(%3)-((%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgot(%3)+0x80000000)>>32<<32))&0xfff;"
"lu32i.d %2,(%%pcrel(_GLOBAL_OFFSET_TABLE_+8)+%%tlsgot(%3)+0x80000000)<<12>>44;"
"lu52i.d %2,%2,(%%pcrel(_GLOBAL_OFFSET_TABLE_+12)+%%tlsgot(%3)+0x80000000)>>52;"
"ldx.d %1,%1,%2;"
, &LARCH_opts.addrwidth_is_64, 0, 0},

{0, 0, "la.tls.ld", "r,l", "la.tls.gd %1,%2", 0, 0, 0},

{0, 0, "la.tls.gd", "r,l",
"pcaddu12i %1,(%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%tlsgd(%2)+0x800)<<32>>44;"
"addi.w %1,%1,%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgd(%2)-((%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgd(%2)+0x800)>>12<<12);"
, &LARCH_opts.addrwidth_is_32, 0, 0},

{0, 0, "la.tls.gd", "r,l",
"pcaddu12i %1,(%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%tlsgd(%2)+0x800)>>12;"
"addi.d %1,%1,%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgd(%2)-((%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgd(%2)+0x800)>>12<<12);"
, &LARCH_opts.addrwidth_is_64, 0, 0},
{0, 0, "la.tls.gd", "r,r,l",
"pcaddu12i %1,(%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%tlsgd(%3)-((%%pcrel(_GLOBAL_OFFSET_TABLE_)+%%tlsgd(%3)+0x80000000)>>32<<32))<<32>>44;"
"ori %2,$r0,(%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgd(%3)-((%%pcrel(_GLOBAL_OFFSET_TABLE_+4)+%%tlsgd(%3)+0x80000000)>>32<<32))&0xfff;"
"lu32i.d %2,(%%pcrel(_GLOBAL_OFFSET_TABLE_+8)+%%tlsgd(%3)+0x80000000)<<12>>44;"
"lu52i.d %2,%2,(%%pcrel(_GLOBAL_OFFSET_TABLE_+12)+%%tlsgd(%3)+0x80000000)>>52;"
"add.d %1,%1,%2;"
, &LARCH_opts.addrwidth_is_64, 0, 0},

{0} /* Terminate the list.  */
};

static struct loongarch_opcode loongarch_fix_opcodes[] = {
/* match,    mask,       name, format, macro, include, exclude, pinfo */
{0x00000800, 0xfffffc1c, "gr2scr", "cr0:2,r5:5", 0, 0, 0, 0},
{0x00000c00, 0xffffff80, "scr2gr", "r0:5,cr5:2", 0, 0, 0, 0},
{0x00001000, 0xfffffc00, "clo.w", "r0:5,r5:5", 0, 0, 0, 0},
{0x00001400, 0xfffffc00, "clz.w", "r0:5,r5:5", 0, 0, 0, 0},
{0x00001800, 0xfffffc00, "cto.w", "r0:5,r5:5", 0, 0, 0, 0},
{0x00001c00, 0xfffffc00, "ctz.w", "r0:5,r5:5", 0, 0, 0, 0},
{0x00002000, 0xfffffc00, "clo.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x00002400, 0xfffffc00, "clz.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x00002800, 0xfffffc00, "cto.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x00002c00, 0xfffffc00, "ctz.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x00003000, 0xfffffc00, "revb.2h", "r0:5,r5:5", 0, 0, 0, 0},
{0x00003400, 0xfffffc00, "revb.4h", "r0:5,r5:5", 0, 0, 0, 0},
{0x00003800, 0xfffffc00, "revb.2w", "r0:5,r5:5", 0, 0, 0, 0},
{0x00003c00, 0xfffffc00, "revb.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x00004000, 0xfffffc00, "revh.2w", "r0:5,r5:5", 0, 0, 0, 0},
{0x00004400, 0xfffffc00, "revh.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x00004800, 0xfffffc00, "bitrev.4b", "r0:5,r5:5", 0, 0, 0, 0},
{0x00004c00, 0xfffffc00, "bitrev.8b", "r0:5,r5:5", 0, 0, 0, 0},
{0x00005000, 0xfffffc00, "bitrev.w", "r0:5,r5:5", 0, 0, 0, 0},
{0x00005400, 0xfffffc00, "bitrev.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x00005800, 0xfffffc00, "ext.w.h", "r0:5,r5:5", 0, 0, 0, 0},
{0x00005c00, 0xfffffc00, "ext.w.b", "r0:5,r5:5", 0, 0, 0, 0},
{0x00006000, 0xfffffc00, "rdtimel.w", "r0:5,r5:5", 0, 0, 0, 0},
{0x00006400, 0xfffffc00, "rdtimeh.w", "r0:5,r5:5", 0, 0, 0, 0},
{0x00006800, 0xfffffc00, "rdtime.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x00006c00, 0xfffffc00, "cpucfg", "r0:5,r5:5", 0, 0, 0, 0},
{0x00010000, 0xffff801f, "asrtle.d", "r5:5,r10:5", 0, 0, 0, 0},
{0x00018000, 0xffff801f, "asrtgt.d", "r5:5,r10:5", 0, 0, 0, 0},
{0x00040000, 0xfffe0000, "alsl.w", "r0:5,r5:5,r10:5,u15:2+1", 0, 0, 0, 0},
{0x00060000, 0xfffe0000, "alsl.wu", "r0:5,r5:5,r10:5,u15:2+1", 0, 0, 0, 0},
{0x00080000, 0xfffe0000, "bytepick.w", "r0:5,r5:5,r10:5,u15:2", 0, 0, 0, 0},
{0x000c0000, 0xfffc0000, "bytepick.d", "r0:5,r5:5,r10:5,u15:3", 0, 0, 0, 0},
{0x00100000, 0xffff8000, "add.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00108000, 0xffff8000, "add.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00110000, 0xffff8000, "sub.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00118000, 0xffff8000, "sub.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00120000, 0xffff8000, "slt", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00128000, 0xffff8000, "sltu", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00130000, 0xffff8000, "maskeqz", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00138000, 0xffff8000, "masknez", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00140000, 0xffff8000, "nor", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00148000, 0xffff8000, "and", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00150000, 0xfffffc00, "move", "r0:5,r5:5", 0/* or %1,%2,$r0 */, 0, 0, 0},
{0x00150000, 0xffff8000, "or", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00158000, 0xffff8000, "xor", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00160000, 0xffff8000, "orn", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00168000, 0xffff8000, "andn", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00170000, 0xffff8000, "sll.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00178000, 0xffff8000, "srl.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00180000, 0xffff8000, "sra.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00188000, 0xffff8000, "sll.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00190000, 0xffff8000, "srl.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00198000, 0xffff8000, "sra.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001a0000, 0xffff8000, "rotr.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001a8000, 0xffff8000, "rotr.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001b0000, 0xffff8000, "rotr.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001b8000, 0xffff8000, "rotr.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001c0000, 0xffff8000, "mul.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001c8000, 0xffff8000, "mulh.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001d0000, 0xffff8000, "mulh.wu", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001d8000, 0xffff8000, "mul.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001e0000, 0xffff8000, "mulh.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001e8000, 0xffff8000, "mulh.du", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001f0000, 0xffff8000, "mulw.d.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x001f8000, 0xffff8000, "mulw.d.wu", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00200000, 0xffff8000, "div.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00208000, 0xffff8000, "mod.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00210000, 0xffff8000, "div.wu", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00218000, 0xffff8000, "mod.wu", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00220000, 0xffff8000, "div.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00228000, 0xffff8000, "mod.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00230000, 0xffff8000, "div.du", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00238000, 0xffff8000, "mod.du", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00240000, 0xffff8000, "crc.w.b.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00248000, 0xffff8000, "crc.w.h.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00250000, 0xffff8000, "crc.w.w.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00258000, 0xffff8000, "crc.w.d.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00260000, 0xffff8000, "crcc.w.b.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00268000, 0xffff8000, "crcc.w.h.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00270000, 0xffff8000, "crcc.w.w.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00278000, 0xffff8000, "crcc.w.d.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00290000, 0xffff8000, "addu12i.w", "r0:5,r5:5,s10:5", 0, 0, 0, 0},
{0x00298000, 0xffff8000, "addu12i.d", "r0:5,r5:5,s10:5", 0, 0, 0, 0},
{0x002a0000, 0xffff8000, "break", "u0:15", 0, 0, 0, 0},
{0x002a8000, 0xffff8000, "dbgcall", "u0:15", 0, 0, 0, 0},
{0x002b0000, 0xffff8000, "syscall", "u0:15", 0, 0, 0, 0},
{0x002b8000, 0xffff8000, "hypcall", "u0:15", 0, 0, 0, 0},
{0x002c0000, 0xfffe0000, "alsl.d", "r0:5,r5:5,r10:5,u15:2+1", 0, 0, 0, 0},
{0x00300000, 0xffff8000, "adc.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00308000, 0xffff8000, "adc.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00310000, 0xffff8000, "adc.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00318000, 0xffff8000, "adc.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00320000, 0xffff8000, "sbc.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00328000, 0xffff8000, "sbc.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00330000, 0xffff8000, "sbc.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00338000, 0xffff8000, "sbc.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00340000, 0xffff8000, "rcr.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00348000, 0xffff8000, "rcr.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00350000, 0xffff8000, "rcr.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00358000, 0xffff8000, "rcr.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x00408000, 0xffff8000, "slli.w", "r0:5,r5:5,u10:5", 0, 0, 0, 0},
{0x00410000, 0xffff0000, "slli.d", "r0:5,r5:5,u10:6", 0, 0, 0, 0},
{0x00448000, 0xffff8000, "srli.w", "r0:5,r5:5,u10:5", 0, 0, 0, 0},
{0x00450000, 0xffff0000, "srli.d", "r0:5,r5:5,u10:6", 0, 0, 0, 0},
{0x00488000, 0xffff8000, "srai.w", "r0:5,r5:5,u10:5", 0, 0, 0, 0},
{0x00490000, 0xffff0000, "srai.d", "r0:5,r5:5,u10:6", 0, 0, 0, 0},
{0x004c2000, 0xffffe000, "rotri.b", "r0:5,r5:5,u10:3", 0, 0, 0, 0},
{0x004c4000, 0xffffc000, "rotri.h", "r0:5,r5:5,u10:4", 0, 0, 0, 0},
{0x004c8000, 0xffff8000, "rotri.w", "r0:5,r5:5,u10:5", 0, 0, 0, 0},
{0x004d0000, 0xffff0000, "rotri.d", "r0:5,r5:5,u10:6", 0, 0, 0, 0},
{0x00502000, 0xffffe000, "rcri.b", "r0:5,r5:5,u10:3", 0, 0, 0, 0},
{0x00504000, 0xffffc000, "rcri.h", "r0:5,r5:5,u10:4", 0, 0, 0, 0},
{0x00508000, 0xffff8000, "rcri.w", "r0:5,r5:5,u10:5", 0, 0, 0, 0},
{0x00510000, 0xffff0000, "rcri.d", "r0:5,r5:5,u10:6", 0, 0, 0, 0},
{0x00600000, 0xffe08000, "bstrins.w", "r0:5,r5:5,u16:5,u10:5", 0, 0, 0, 0},
{0x00608000, 0xffe08000, "bstrpick.w", "r0:5,r5:5,u16:5,u10:5", 0, 0, 0, 0},
{0x00800000, 0xffc00000, "bstrins.d", "r0:5,r5:5,u16:6,u10:6", 0, 0, 0, 0},
{0x00c00000, 0xffc00000, "bstrpick.d", "r0:5,r5:5,u16:6,u10:6", 0, 0, 0, 0},
{0} /* Terminate the list.  */
};

static struct loongarch_opcode loongarch_float_opcodes[] = {
/* match,    mask,       name, format, macro, include, exclude, pinfo */
{0x01000000, 0xffff8000, "fadd.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01008000, 0xffff8000, "fadd.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01010000, 0xffff8000, "fadd.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01020000, 0xffff8000, "fsub.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01028000, 0xffff8000, "fsub.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01030000, 0xffff8000, "fsub.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01040000, 0xffff8000, "fmul.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01048000, 0xffff8000, "fmul.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01050000, 0xffff8000, "fmul.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01060000, 0xffff8000, "fdiv.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01068000, 0xffff8000, "fdiv.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01070000, 0xffff8000, "fdiv.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01080000, 0xffff8000, "fmax.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01088000, 0xffff8000, "fmax.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01090000, 0xffff8000, "fmax.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x010a0000, 0xffff8000, "fmin.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x010a8000, 0xffff8000, "fmin.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x010b0000, 0xffff8000, "fmin.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x010c0000, 0xffff8000, "fmaxa.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x010c8000, 0xffff8000, "fmaxa.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x010d0000, 0xffff8000, "fmaxa.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x010e0000, 0xffff8000, "fmina.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x010e8000, 0xffff8000, "fmina.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x010f0000, 0xffff8000, "fmina.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01100000, 0xffff8000, "fscaleb.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01108000, 0xffff8000, "fscaleb.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01110000, 0xffff8000, "fscaleb.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01120000, 0xffff8000, "fcopysign.h", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01128000, 0xffff8000, "fcopysign.s", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01130000, 0xffff8000, "fcopysign.d", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01140000, 0xfffffc00, "fabs.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01140400, 0xfffffc00, "fabs.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01140800, 0xfffffc00, "fabs.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01141000, 0xfffffc00, "fneg.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01141400, 0xfffffc00, "fneg.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01141800, 0xfffffc00, "fneg.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01142000, 0xfffffc00, "flogb.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01142400, 0xfffffc00, "flogb.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01142800, 0xfffffc00, "flogb.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01143000, 0xfffffc00, "fclass.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01143400, 0xfffffc00, "fclass.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01143800, 0xfffffc00, "fclass.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01144000, 0xfffffc00, "fsqrt.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01144400, 0xfffffc00, "fsqrt.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01144800, 0xfffffc00, "fsqrt.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01145000, 0xfffffc00, "frecip.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01145400, 0xfffffc00, "frecip.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01145800, 0xfffffc00, "frecip.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01146000, 0xfffffc00, "frsqrt.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01146400, 0xfffffc00, "frsqrt.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01146800, 0xfffffc00, "frsqrt.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01147000, 0xfffffc00, "frecipe.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01147400, 0xfffffc00, "frecipe.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01147800, 0xfffffc00, "frecipe.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01148000, 0xfffffc00, "frsqrte.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01148400, 0xfffffc00, "frsqrte.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01148800, 0xfffffc00, "frsqrte.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01149000, 0xfffffc00, "fmov.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01149400, 0xfffffc00, "fmov.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01149800, 0xfffffc00, "fmov.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x0114a000, 0xfffffc00, "movgr2fr.h", "f0:5,r5:5", 0, 0, 0, 0},
{0x0114a400, 0xfffffc00, "movgr2fr.w", "f0:5,r5:5", 0, 0, 0, 0},
{0x0114a800, 0xfffffc00, "movgr2fr.d", "f0:5,r5:5", 0, 0, 0, 0},
{0x0114ac00, 0xfffffc00, "movgr2frh.w", "f0:5,r5:5", 0, 0, 0, 0},
{0x0114b000, 0xfffffc00, "movfr2gr.h", "r0:5,f5:5", 0, 0, 0, 0},
{0x0114b400, 0xfffffc00, "movfr2gr.s", "r0:5,f5:5", 0, 0, 0, 0},
{0x0114b800, 0xfffffc00, "movfr2gr.d", "r0:5,f5:5", 0, 0, 0, 0},
{0x0114bc00, 0xfffffc00, "movfrh2gr.s", "r0:5,f5:5", 0, 0, 0, 0},
{0x0114c000, 0xfffffc00, "movgr2fcsr", "r0:5,r5:5", 0, 0, 0, 0},
{0x0114c800, 0xfffffc00, "movfcsr2gr", "r0:5,r5:5", 0, 0, 0, 0},
{0x0114d000, 0xfffffc18, "movfr2cf", "c0:3,f5:5", 0, 0, 0, 0},
{0x0114d400, 0xffffff00, "movcf2fr", "f0:5,c5:3", 0, 0, 0, 0},
{0x0114d800, 0xfffffc18, "movgr2cf", "c0:3,r5:5", 0, 0, 0, 0},
{0x0114dc00, 0xffffff00, "movcf2gr", "r0:5,c5:3", 0, 0, 0, 0},
{0x0114e000, 0xfffffc00, "fcvt.ld.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x0114e400, 0xfffffc00, "fcvt.ud.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01150000, 0xffff8000, "fcvt.d.ld", "f0:5,f5:5,f10:5", 0, 0, 0, 0},
{0x01180400, 0xfffffc00, "fcvtrm.h.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01180800, 0xfffffc00, "fcvtrm.h.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01180c00, 0xfffffc00, "fcvtrm.h.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01181000, 0xfffffc00, "fcvtrm.s.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01181800, 0xfffffc00, "fcvtrm.s.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01181c00, 0xfffffc00, "fcvtrm.s.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01182000, 0xfffffc00, "fcvtrm.d.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01182400, 0xfffffc00, "fcvtrm.d.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01182c00, 0xfffffc00, "fcvtrm.d.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01183000, 0xfffffc00, "fcvtrm.q.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01183400, 0xfffffc00, "fcvtrm.q.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01183800, 0xfffffc00, "fcvtrm.q.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01184400, 0xfffffc00, "fcvtrp.h.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01184800, 0xfffffc00, "fcvtrp.h.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01184c00, 0xfffffc00, "fcvtrp.h.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01185000, 0xfffffc00, "fcvtrp.s.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01185800, 0xfffffc00, "fcvtrp.s.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01185c00, 0xfffffc00, "fcvtrp.s.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01186000, 0xfffffc00, "fcvtrp.d.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01186400, 0xfffffc00, "fcvtrp.d.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01186c00, 0xfffffc00, "fcvtrp.d.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01187000, 0xfffffc00, "fcvtrp.q.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01187400, 0xfffffc00, "fcvtrp.q.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01187800, 0xfffffc00, "fcvtrp.q.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01188400, 0xfffffc00, "fcvtrz.h.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01188800, 0xfffffc00, "fcvtrz.h.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01188c00, 0xfffffc00, "fcvtrz.h.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01189000, 0xfffffc00, "fcvtrz.s.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01189800, 0xfffffc00, "fcvtrz.s.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01189c00, 0xfffffc00, "fcvtrz.s.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118a000, 0xfffffc00, "fcvtrz.d.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118a400, 0xfffffc00, "fcvtrz.d.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118ac00, 0xfffffc00, "fcvtrz.d.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118b000, 0xfffffc00, "fcvtrz.q.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118b400, 0xfffffc00, "fcvtrz.q.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118b800, 0xfffffc00, "fcvtrz.q.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118c400, 0xfffffc00, "fcvtrne.h.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118c800, 0xfffffc00, "fcvtrne.h.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118cc00, 0xfffffc00, "fcvtrne.h.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118d000, 0xfffffc00, "fcvtrne.s.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118d800, 0xfffffc00, "fcvtrne.s.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118dc00, 0xfffffc00, "fcvtrne.s.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118e000, 0xfffffc00, "fcvtrne.d.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118e400, 0xfffffc00, "fcvtrne.d.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118ec00, 0xfffffc00, "fcvtrne.d.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118f000, 0xfffffc00, "fcvtrne.q.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118f400, 0xfffffc00, "fcvtrne.q.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x0118f800, 0xfffffc00, "fcvtrne.q.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01190400, 0xfffffc00, "fcvt.h.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01190800, 0xfffffc00, "fcvt.h.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01190c00, 0xfffffc00, "fcvt.h.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01191000, 0xfffffc00, "fcvt.s.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01191800, 0xfffffc00, "fcvt.s.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x01191c00, 0xfffffc00, "fcvt.s.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01192000, 0xfffffc00, "fcvt.d.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01192400, 0xfffffc00, "fcvt.d.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01192c00, 0xfffffc00, "fcvt.d.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x01193000, 0xfffffc00, "fcvt.q.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x01193400, 0xfffffc00, "fcvt.q.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x01193800, 0xfffffc00, "fcvt.q.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a0000, 0xfffffc00, "ftintrm.w.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a0400, 0xfffffc00, "ftintrm.w.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a0800, 0xfffffc00, "ftintrm.w.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a0c00, 0xfffffc00, "ftintrm.w.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a1000, 0xfffffc00, "ftintrm.wu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a1400, 0xfffffc00, "ftintrm.wu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a1800, 0xfffffc00, "ftintrm.wu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a1c00, 0xfffffc00, "ftintrm.wu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a2000, 0xfffffc00, "ftintrm.l.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a2400, 0xfffffc00, "ftintrm.l.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a2800, 0xfffffc00, "ftintrm.l.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a2c00, 0xfffffc00, "ftintrm.l.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a3000, 0xfffffc00, "ftintrm.lu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a3400, 0xfffffc00, "ftintrm.lu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a3800, 0xfffffc00, "ftintrm.lu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a3c00, 0xfffffc00, "ftintrm.lu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a4000, 0xfffffc00, "ftintrp.w.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a4400, 0xfffffc00, "ftintrp.w.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a4800, 0xfffffc00, "ftintrp.w.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a4c00, 0xfffffc00, "ftintrp.w.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a5000, 0xfffffc00, "ftintrp.wu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a5400, 0xfffffc00, "ftintrp.wu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a5800, 0xfffffc00, "ftintrp.wu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a5c00, 0xfffffc00, "ftintrp.wu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a6000, 0xfffffc00, "ftintrp.l.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a6400, 0xfffffc00, "ftintrp.l.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a6800, 0xfffffc00, "ftintrp.l.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a6c00, 0xfffffc00, "ftintrp.l.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a7000, 0xfffffc00, "ftintrp.lu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a7400, 0xfffffc00, "ftintrp.lu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a7800, 0xfffffc00, "ftintrp.lu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a7c00, 0xfffffc00, "ftintrp.lu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a8000, 0xfffffc00, "ftintrz.w.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a8400, 0xfffffc00, "ftintrz.w.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a8800, 0xfffffc00, "ftintrz.w.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a8c00, 0xfffffc00, "ftintrz.w.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a9000, 0xfffffc00, "ftintrz.wu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a9400, 0xfffffc00, "ftintrz.wu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a9800, 0xfffffc00, "ftintrz.wu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011a9c00, 0xfffffc00, "ftintrz.wu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011aa000, 0xfffffc00, "ftintrz.l.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011aa400, 0xfffffc00, "ftintrz.l.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011aa800, 0xfffffc00, "ftintrz.l.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011aac00, 0xfffffc00, "ftintrz.l.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ab000, 0xfffffc00, "ftintrz.lu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ab400, 0xfffffc00, "ftintrz.lu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ab800, 0xfffffc00, "ftintrz.lu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011abc00, 0xfffffc00, "ftintrz.lu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ac000, 0xfffffc00, "ftintrne.w.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ac400, 0xfffffc00, "ftintrne.w.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ac800, 0xfffffc00, "ftintrne.w.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011acc00, 0xfffffc00, "ftintrne.w.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ad000, 0xfffffc00, "ftintrne.wu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ad400, 0xfffffc00, "ftintrne.wu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ad800, 0xfffffc00, "ftintrne.wu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011adc00, 0xfffffc00, "ftintrne.wu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ae000, 0xfffffc00, "ftintrne.l.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ae400, 0xfffffc00, "ftintrne.l.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ae800, 0xfffffc00, "ftintrne.l.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011aec00, 0xfffffc00, "ftintrne.l.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011af000, 0xfffffc00, "ftintrne.lu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011af400, 0xfffffc00, "ftintrne.lu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011af800, 0xfffffc00, "ftintrne.lu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011afc00, 0xfffffc00, "ftintrne.lu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b0000, 0xfffffc00, "ftint.w.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b0400, 0xfffffc00, "ftint.w.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b0800, 0xfffffc00, "ftint.w.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b0c00, 0xfffffc00, "ftint.w.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b1000, 0xfffffc00, "ftint.wu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b1400, 0xfffffc00, "ftint.wu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b1800, 0xfffffc00, "ftint.wu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b1c00, 0xfffffc00, "ftint.wu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b2000, 0xfffffc00, "ftint.l.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b2400, 0xfffffc00, "ftint.l.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b2800, 0xfffffc00, "ftint.l.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b2c00, 0xfffffc00, "ftint.l.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b3000, 0xfffffc00, "ftint.lu.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b3400, 0xfffffc00, "ftint.lu.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b3800, 0xfffffc00, "ftint.lu.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011b3c00, 0xfffffc00, "ftint.lu.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c0000, 0xfffffc00, "ffintrm.h.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c0400, 0xfffffc00, "ffintrm.h.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c0800, 0xfffffc00, "ffintrm.h.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c0c00, 0xfffffc00, "ffintrm.h.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c1000, 0xfffffc00, "ffintrm.s.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c1400, 0xfffffc00, "ffintrm.s.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c1800, 0xfffffc00, "ffintrm.s.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c1c00, 0xfffffc00, "ffintrm.s.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c2000, 0xfffffc00, "ffintrm.d.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c2400, 0xfffffc00, "ffintrm.d.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c2800, 0xfffffc00, "ffintrm.d.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c2c00, 0xfffffc00, "ffintrm.d.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c3000, 0xfffffc00, "ffintrm.q.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c3400, 0xfffffc00, "ffintrm.q.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c3800, 0xfffffc00, "ffintrm.q.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c3c00, 0xfffffc00, "ffintrm.q.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c4000, 0xfffffc00, "ffintrp.h.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c4400, 0xfffffc00, "ffintrp.h.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c4800, 0xfffffc00, "ffintrp.h.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c4c00, 0xfffffc00, "ffintrp.h.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c5000, 0xfffffc00, "ffintrp.s.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c5400, 0xfffffc00, "ffintrp.s.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c5800, 0xfffffc00, "ffintrp.s.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c5c00, 0xfffffc00, "ffintrp.s.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c6000, 0xfffffc00, "ffintrp.d.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c6400, 0xfffffc00, "ffintrp.d.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c6800, 0xfffffc00, "ffintrp.d.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c6c00, 0xfffffc00, "ffintrp.d.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c7000, 0xfffffc00, "ffintrp.q.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c7400, 0xfffffc00, "ffintrp.q.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c7800, 0xfffffc00, "ffintrp.q.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c7c00, 0xfffffc00, "ffintrp.q.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c8000, 0xfffffc00, "ffintrz.h.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c8400, 0xfffffc00, "ffintrz.h.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c8800, 0xfffffc00, "ffintrz.h.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c8c00, 0xfffffc00, "ffintrz.h.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c9000, 0xfffffc00, "ffintrz.s.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c9400, 0xfffffc00, "ffintrz.s.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c9800, 0xfffffc00, "ffintrz.s.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011c9c00, 0xfffffc00, "ffintrz.s.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ca000, 0xfffffc00, "ffintrz.d.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ca400, 0xfffffc00, "ffintrz.d.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ca800, 0xfffffc00, "ffintrz.d.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cac00, 0xfffffc00, "ffintrz.d.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cb000, 0xfffffc00, "ffintrz.q.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cb400, 0xfffffc00, "ffintrz.q.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cb800, 0xfffffc00, "ffintrz.q.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cbc00, 0xfffffc00, "ffintrz.q.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cc000, 0xfffffc00, "ffintrne.h.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cc400, 0xfffffc00, "ffintrne.h.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cc800, 0xfffffc00, "ffintrne.h.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ccc00, 0xfffffc00, "ffintrne.h.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cd000, 0xfffffc00, "ffintrne.s.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cd400, 0xfffffc00, "ffintrne.s.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cd800, 0xfffffc00, "ffintrne.s.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cdc00, 0xfffffc00, "ffintrne.s.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ce000, 0xfffffc00, "ffintrne.d.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ce400, 0xfffffc00, "ffintrne.d.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011ce800, 0xfffffc00, "ffintrne.d.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cec00, 0xfffffc00, "ffintrne.d.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cf000, 0xfffffc00, "ffintrne.q.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cf400, 0xfffffc00, "ffintrne.q.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cf800, 0xfffffc00, "ffintrne.q.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011cfc00, 0xfffffc00, "ffintrne.q.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d0000, 0xfffffc00, "ffint.h.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d0400, 0xfffffc00, "ffint.h.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d0800, 0xfffffc00, "ffint.h.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d0c00, 0xfffffc00, "ffint.h.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d1000, 0xfffffc00, "ffint.s.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d1400, 0xfffffc00, "ffint.s.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d1800, 0xfffffc00, "ffint.s.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d1c00, 0xfffffc00, "ffint.s.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d2000, 0xfffffc00, "ffint.d.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d2400, 0xfffffc00, "ffint.d.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d2800, 0xfffffc00, "ffint.d.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d2c00, 0xfffffc00, "ffint.d.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d3000, 0xfffffc00, "ffint.q.w", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d3400, 0xfffffc00, "ffint.q.wu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d3800, 0xfffffc00, "ffint.q.l", "f0:5,f5:5", 0, 0, 0, 0},
{0x011d3c00, 0xfffffc00, "ffint.q.lu", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e0000, 0xfffffc00, "frintrm.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e0400, 0xfffffc00, "frintrm.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e0800, 0xfffffc00, "frintrm.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e0c00, 0xfffffc00, "frintrm.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e1000, 0xfffffc00, "frintrp.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e1400, 0xfffffc00, "frintrp.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e1800, 0xfffffc00, "frintrp.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e1c00, 0xfffffc00, "frintrp.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e2000, 0xfffffc00, "frintrz.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e2400, 0xfffffc00, "frintrz.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e2800, 0xfffffc00, "frintrz.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e2c00, 0xfffffc00, "frintrz.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e3000, 0xfffffc00, "frintrne.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e3400, 0xfffffc00, "frintrne.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e3800, 0xfffffc00, "frintrne.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e3c00, 0xfffffc00, "frintrne.q", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e4000, 0xfffffc00, "frint.h", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e4400, 0xfffffc00, "frint.s", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e4800, 0xfffffc00, "frint.d", "f0:5,f5:5", 0, 0, 0, 0},
{0x011e4c00, 0xfffffc00, "frint.q", "f0:5,f5:5", 0, 0, 0, 0},
{0} /* Terminate the list.  */
};

static struct loongarch_opcode loongarch_lmm_opcodes[] = {
/* match,    mask,       name, format, macro, include, exclude, pinfo */
{0x02000000, 0xffc00000, "slti", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x02400000, 0xffc00000, "sltui", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x02800000, 0xffc00000, "addi.w", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x02c00000, 0xffc00000, "addi.d", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x03000000, 0xffc00000, "lu52i.d", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0, 0, "nop", "", "andi $r0,$r0,0", 0, 0, 0},
{0x03400000, 0xffc00000, "andi", "r0:5,r5:5,u10:12", 0, 0, 0, 0},
{0x03800000, 0xffc00000, "ori", "r0:5,r5:5,u10:12", 0, 0, 0, 0},
{0x03c00000, 0xffc00000, "xori", "r0:5,r5:5,u10:12", 0, 0, 0, 0},
{0x10000000, 0xfc000000, "addu16i.d", "r0:5,r5:5,s10:16", 0, 0, 0, 0},
{0x14000000, 0xfe000000, "lu12i.w", "r0:5,s5:20", 0, 0, 0, 0},
{0x16000000, 0xfe000000, "lu32i.d", "r0:5,s5:20", 0, 0, 0, 0},
{0x18000000, 0xfe000000, "pcaddi", "r0:5,s5:20", 0, 0, 0, 0},
{0x1a000000, 0xfe000000, "pcalau12i", "r0:5,s5:20", 0, 0, 0, 0},
{0x1c000000, 0xfe000000, "pcaddu12i", "r0:5,s5:20", 0, 0, 0, 0},
{0x1e000000, 0xfe000000, "pcaddu18i", "r0:5,s5:20", 0, 0, 0, 0},
{0} /* Terminate the list.  */
};

static struct loongarch_opcode loongarch_privilege_opcodes[] = {
/* match,    mask,       name, format, macro, include, exclude, pinfo */
{0x04000000, 0xff0003e0, "csrrd", "r0:5,u10:14", 0, 0, 0, 0},
{0x04000020, 0xff0003e0, "csrwr", "r0:5,u10:14", 0, 0, 0, 0},
{0x04000000, 0xff000000, "csrxchg", "r0:5,r5:5,u10:14", 0, 0, 0, 0},
{0x05000000, 0xff0003e0, "gcsrrd", "r0:5,u10:14", 0, 0, 0, 0},
{0x05000020, 0xff0003e0, "gcsrwr", "r0:5,u10:14", 0, 0, 0, 0},
{0x05000000, 0xff000000, "gcsrxchg", "r0:5,r5:5,u10:14", 0, 0, 0, 0},
{0x06000000, 0xffc00000, "cache", "u0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x06400000, 0xfffc0000, "lddir", "r0:5,r5:5,u10:8", 0, 0, 0, 0},
{0x06440000, 0xfffc001f, "ldpte", "r5:5,u10:8", 0, 0, 0, 0},
{0x06480000, 0xfffffc00, "iocsrrd.b", "r0:5,r5:5", 0, 0, 0, 0},
{0x06480400, 0xfffffc00, "iocsrrd.h", "r0:5,r5:5", 0, 0, 0, 0},
{0x06480800, 0xfffffc00, "iocsrrd.w", "r0:5,r5:5", 0, 0, 0, 0},
{0x06480c00, 0xfffffc00, "iocsrrd.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x06481000, 0xfffffc00, "iocsrwr.b", "r0:5,r5:5", 0, 0, 0, 0},
{0x06481400, 0xfffffc00, "iocsrwr.h", "r0:5,r5:5", 0, 0, 0, 0},
{0x06481800, 0xfffffc00, "iocsrwr.w", "r0:5,r5:5", 0, 0, 0, 0},
{0x06481c00, 0xfffffc00, "iocsrwr.d", "r0:5,r5:5", 0, 0, 0, 0},
{0x06482000, 0xffffffff, "tlbinv", "", 0, 0, 0, 0},
{0x06482001, 0xffffffff, "gtlbinv", "", 0, 0, 0, 0},
{0x06482400, 0xffffffff, "tlbflush", "", 0, 0, 0, 0},
{0x06482401, 0xffffffff, "gtlbflush", "", 0, 0, 0, 0},
{0x06482800, 0xffffffff, "tlbp", "", 0, 0, 0, 0},
{0x06482801, 0xffffffff, "gtlbp", "", 0, 0, 0, 0},
{0x06482c00, 0xffffffff, "tlbr", "", 0, 0, 0, 0},
{0x06482c01, 0xffffffff, "gtlbr", "", 0, 0, 0, 0},
{0x06483000, 0xffffffff, "tlbwi", "", 0, 0, 0, 0},
{0x06483001, 0xffffffff, "gtlbwi", "", 0, 0, 0, 0},
{0x06483400, 0xffffffff, "tlbwr", "", 0, 0, 0, 0},
{0x06483401, 0xffffffff, "gtlbwr", "", 0, 0, 0, 0},
{0x06483800, 0xffffffff, "eret", "", 0, 0, 0, 0},
{0x06483c00, 0xffffffff, "deret", "", 0, 0, 0, 0},
{0x06488000, 0xffff8000, "wait", "u0:15", 0, 0, 0, 0},
{0x06498000, 0xffff8000, "invtlb", "u0:5,r5:5,r10:5", 0, 0, 0, 0},
{0} /* Terminate the list.  */
};

static struct loongarch_opcode loongarch_4opt_opcodes[] = {
/* match,    mask,       name, format, macro, include, exclude, pinfo */
{0x08000000, 0xfff00000, "fmadd.h", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08100000, 0xfff00000, "fmadd.s", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08200000, 0xfff00000, "fmadd.d", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08400000, 0xfff00000, "fmsub.h", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08500000, 0xfff00000, "fmsub.s", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08600000, 0xfff00000, "fmsub.d", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08800000, 0xfff00000, "fnmadd.h", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08900000, 0xfff00000, "fnmadd.s", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08a00000, 0xfff00000, "fnmadd.d", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08c00000, 0xfff00000, "fnmsub.h", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08d00000, 0xfff00000, "fnmsub.s", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x08e00000, 0xfff00000, "fnmsub.d", "f0:5,f5:5,f10:5,f15:5", 0, 0, 0, 0},
{0x0c000000, 0xffff8018, "fcmp.caf.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c008000, 0xffff8018, "fcmp.saf.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c010000, 0xffff8018, "fcmp.clt.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c018000, 0xffff8018, "fcmp.slt.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c020000, 0xffff8018, "fcmp.ceq.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c028000, 0xffff8018, "fcmp.seq.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c030000, 0xffff8018, "fcmp.cle.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c038000, 0xffff8018, "fcmp.sle.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c040000, 0xffff8018, "fcmp.cun.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c048000, 0xffff8018, "fcmp.sun.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c050000, 0xffff8018, "fcmp.cult.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c058000, 0xffff8018, "fcmp.sult.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c060000, 0xffff8018, "fcmp.cueq.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c068000, 0xffff8018, "fcmp.sueq.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c070000, 0xffff8018, "fcmp.cule.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c078000, 0xffff8018, "fcmp.sule.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c080000, 0xffff8018, "fcmp.cne.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c088000, 0xffff8018, "fcmp.sne.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c0a0000, 0xffff8018, "fcmp.cor.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c0a8000, 0xffff8018, "fcmp.sor.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c0c0000, 0xffff8018, "fcmp.cune.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c0c8000, 0xffff8018, "fcmp.sune.h", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c100000, 0xffff8018, "fcmp.caf.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c108000, 0xffff8018, "fcmp.saf.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c110000, 0xffff8018, "fcmp.clt.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c118000, 0xffff8018, "fcmp.slt.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c120000, 0xffff8018, "fcmp.ceq.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c128000, 0xffff8018, "fcmp.seq.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c130000, 0xffff8018, "fcmp.cle.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c138000, 0xffff8018, "fcmp.sle.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c140000, 0xffff8018, "fcmp.cun.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c148000, 0xffff8018, "fcmp.sun.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c150000, 0xffff8018, "fcmp.cult.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c158000, 0xffff8018, "fcmp.sult.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c160000, 0xffff8018, "fcmp.cueq.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c168000, 0xffff8018, "fcmp.sueq.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c170000, 0xffff8018, "fcmp.cule.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c178000, 0xffff8018, "fcmp.sule.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c180000, 0xffff8018, "fcmp.cne.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c188000, 0xffff8018, "fcmp.sne.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c1a0000, 0xffff8018, "fcmp.cor.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c1a8000, 0xffff8018, "fcmp.sor.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c1c0000, 0xffff8018, "fcmp.cune.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c1c8000, 0xffff8018, "fcmp.sune.s", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c200000, 0xffff8018, "fcmp.caf.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c208000, 0xffff8018, "fcmp.saf.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c210000, 0xffff8018, "fcmp.clt.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c218000, 0xffff8018, "fcmp.slt.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c220000, 0xffff8018, "fcmp.ceq.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c228000, 0xffff8018, "fcmp.seq.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c230000, 0xffff8018, "fcmp.cle.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c238000, 0xffff8018, "fcmp.sle.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c240000, 0xffff8018, "fcmp.cun.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c248000, 0xffff8018, "fcmp.sun.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c250000, 0xffff8018, "fcmp.cult.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c258000, 0xffff8018, "fcmp.sult.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c260000, 0xffff8018, "fcmp.cueq.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c268000, 0xffff8018, "fcmp.sueq.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c270000, 0xffff8018, "fcmp.cule.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c278000, 0xffff8018, "fcmp.sule.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c280000, 0xffff8018, "fcmp.cne.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c288000, 0xffff8018, "fcmp.sne.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c2a0000, 0xffff8018, "fcmp.cor.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c2a8000, 0xffff8018, "fcmp.sor.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c2c0000, 0xffff8018, "fcmp.cune.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0c2c8000, 0xffff8018, "fcmp.sune.d", "c0:3,f5:5,f10:5", 0, 0, 0, 0},
{0x0d000000, 0xfffc0000, "fsel", "f0:5,f5:5,f10:5,c15:3", 0, 0, 0, 0},
{0} /* Terminate the list.  */
};

static struct loongarch_opcode loongarch_load_store_opcodes[] = {
/* match,    mask,       name, format, macro, include, exclude, pinfo */
{0x20000000, 0xff000000, "ll.w", "r0:5,r5:5,s10:14<<2", 0, 0, 0, 0},
{0x21000000, 0xff000000, "sc.w", "r0:5,r5:5,s10:14<<2", 0, 0, 0, 0},
{0x22000000, 0xff000000, "ll.d", "r0:5,r5:5,s10:14<<2", 0, 0, 0, 0},
{0x23000000, 0xff000000, "sc.d", "r0:5,r5:5,s10:14<<2", 0, 0, 0, 0},
{0x24000000, 0xff000000, "ldptr.w", "r0:5,r5:5,s10:14<<2", 0, 0, 0, 0},
{0x25000000, 0xff000000, "stptr.w", "r0:5,r5:5,s10:14<<2", 0, 0, 0, 0},
{0x26000000, 0xff000000, "ldptr.d", "r0:5,r5:5,s10:14<<2", 0, 0, 0, 0},
{0x27000000, 0xff000000, "stptr.d", "r0:5,r5:5,s10:14<<2", 0, 0, 0, 0},
{0x28000000, 0xffc00000, "ld.b", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x28400000, 0xffc00000, "ld.h", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x28800000, 0xffc00000, "ld.w", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x28c00000, 0xffc00000, "ld.d", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x29000000, 0xffc00000, "st.b", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x29400000, 0xffc00000, "st.h", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x29800000, 0xffc00000, "st.w", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x29c00000, 0xffc00000, "st.d", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2a000000, 0xffc00000, "ld.bu", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2a400000, 0xffc00000, "ld.hu", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2a800000, 0xffc00000, "ld.wu", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2ac00000, 0xffc00000, "preld", "u0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2b000000, 0xffc00000, "fld.s", "f0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2b400000, 0xffc00000, "fst.s", "f0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2b800000, 0xffc00000, "fld.d", "f0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2bc00000, 0xffc00000, "fst.d", "f0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2e000000, 0xffc00000, "ldl.w", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2e400000, 0xffc00000, "ldr.w", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2e800000, 0xffc00000, "ldl.d", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2ec00000, 0xffc00000, "ldr.d", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2f000000, 0xffc00000, "stl.w", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2f400000, 0xffc00000, "str.w", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2f800000, 0xffc00000, "stl.d", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x2fc00000, 0xffc00000, "str.d", "r0:5,r5:5,s10:12", 0, 0, 0, 0},
{0x38000000, 0xffff8000, "ldx.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38040000, 0xffff8000, "ldx.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38080000, 0xffff8000, "ldx.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x380c0000, 0xffff8000, "ldx.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38100000, 0xffff8000, "stx.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38140000, 0xffff8000, "stx.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38180000, 0xffff8000, "stx.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x381c0000, 0xffff8000, "stx.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38200000, 0xffff8000, "ldx.bu", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38240000, 0xffff8000, "ldx.hu", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38280000, 0xffff8000, "ldx.wu", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x382c0000, 0xffff8000, "preldx", "u0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38300000, 0xffff8000, "fldx.s", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38340000, 0xffff8000, "fldx.d", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38380000, 0xffff8000, "fstx.s", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x383c0000, 0xffff8000, "fstx.d", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0, 0, "amswap.w", "r,r,r,u0:0", "amswap.w %1,%2,%3", 0, 0, 0},
{0x38600000, 0xffff8000, "amswap.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amswap.d", "r,r,r,u0:0", "amswap.d %1,%2,%3", 0, 0, 0},
{0x38608000, 0xffff8000, "amswap.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amadd.w", "r,r,r,u0:0", "amadd.w %1,%2,%3", 0, 0, 0},
{0x38610000, 0xffff8000, "amadd.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amadd.d", "r,r,r,u0:0", "amadd.d %1,%2,%3", 0, 0, 0},
{0x38618000, 0xffff8000, "amadd.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amand.w", "r,r,r,u0:0", "amand.w %1,%2,%3", 0, 0, 0},
{0x38620000, 0xffff8000, "amand.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amand.d", "r,r,r,u0:0", "amand.d %1,%2,%3", 0, 0, 0},
{0x38628000, 0xffff8000, "amand.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amor.w", "r,r,r,u0:0", "amor.w %1,%2,%3", 0, 0, 0},
{0x38630000, 0xffff8000, "amor.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amor.d", "r,r,r,u0:0", "amor.d %1,%2,%3", 0, 0, 0},
{0x38638000, 0xffff8000, "amor.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amxor.w", "r,r,r,u0:0", "amxor.w %1,%2,%3", 0, 0, 0},
{0x38640000, 0xffff8000, "amxor.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amxor.d", "r,r,r,u0:0", "amxor.d %1,%2,%3", 0, 0, 0},
{0x38648000, 0xffff8000, "amxor.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammax.w", "r,r,r,u0:0", "ammax.w %1,%2,%3", 0, 0, 0},
{0x38650000, 0xffff8000, "ammax.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammax.d", "r,r,r,u0:0", "ammax.d %1,%2,%3", 0, 0, 0},
{0x38658000, 0xffff8000, "ammax.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammin.w", "r,r,r,u0:0", "ammin.w %1,%2,%3", 0, 0, 0},
{0x38660000, 0xffff8000, "ammin.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammin.d", "r,r,r,u0:0", "ammin.d %1,%2,%3", 0, 0, 0},
{0x38668000, 0xffff8000, "ammin.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammax.wu", "r,r,r,u0:0", "ammax.wu %1,%2,%3", 0, 0, 0},
{0x38670000, 0xffff8000, "ammax.wu", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammax.du", "r,r,r,u0:0", "ammax.du %1,%2,%3", 0, 0, 0},
{0x38678000, 0xffff8000, "ammax.du", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammin.wu", "r,r,r,u0:0", "ammin.wu %1,%2,%3", 0, 0, 0},
{0x38680000, 0xffff8000, "ammin.wu", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammin.du", "r,r,r,u0:0", "ammin.du %1,%2,%3", 0, 0, 0},
{0x38688000, 0xffff8000, "ammin.du", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amswap_db.w", "r,r,r,u0:0", "amswap_db.w %1,%2,%3", 0, 0, 0},
{0x38690000, 0xffff8000, "amswap_db.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amswap_db.d", "r,r,r,u0:0", "amswap_db.d %1,%2,%3", 0, 0, 0},
{0x38698000, 0xffff8000, "amswap_db.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amadd_db.w", "r,r,r,u0:0", "amadd_db.w %1,%2,%3", 0, 0, 0},
{0x386a0000, 0xffff8000, "amadd_db.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amadd_db.d", "r,r,r,u0:0", "amadd_db.d %1,%2,%3", 0, 0, 0},
{0x386a8000, 0xffff8000, "amadd_db.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amand_db.w", "r,r,r,u0:0", "amand_db.w %1,%2,%3", 0, 0, 0},
{0x386b0000, 0xffff8000, "amand_db.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amand_db.d", "r,r,r,u0:0", "amand_db.d %1,%2,%3", 0, 0, 0},
{0x386b8000, 0xffff8000, "amand_db.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amor_db.w", "r,r,r,u0:0", "amor_db.w %1,%2,%3", 0, 0, 0},
{0x386c0000, 0xffff8000, "amor_db.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amor_db.d", "r,r,r,u0:0", "amor_db.d %1,%2,%3", 0, 0, 0},
{0x386c8000, 0xffff8000, "amor_db.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amxor_db.w", "r,r,r,u0:0", "amxor_db.w %1,%2,%3", 0, 0, 0},
{0x386d0000, 0xffff8000, "amxor_db.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "amxor_db.d", "r,r,r,u0:0", "amxor_db.d %1,%2,%3", 0, 0, 0},
{0x386d8000, 0xffff8000, "amxor_db.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammax_db.w", "r,r,r,u0:0", "ammax_db.w %1,%2,%3", 0, 0, 0},
{0x386e0000, 0xffff8000, "ammax_db.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammax_db.d", "r,r,r,u0:0", "ammax_db.d %1,%2,%3", 0, 0, 0},
{0x386e8000, 0xffff8000, "ammax_db.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammin_db.w", "r,r,r,u0:0", "ammin_db.w %1,%2,%3", 0, 0, 0},
{0x386f0000, 0xffff8000, "ammin_db.w", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammin_db.d", "r,r,r,u0:0", "ammin_db.d %1,%2,%3", 0, 0, 0},
{0x386f8000, 0xffff8000, "ammin_db.d", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammax_db.wu", "r,r,r,u0:0", "ammax_db.wu %1,%2,%3", 0, 0, 0},
{0x38700000, 0xffff8000, "ammax_db.wu", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammax_db.du", "r,r,r,u0:0", "ammax_db.du %1,%2,%3", 0, 0, 0},
{0x38708000, 0xffff8000, "ammax_db.du", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammin_db.wu", "r,r,r,u0:0", "ammin_db.wu %1,%2,%3", 0, 0, 0},
{0x38710000, 0xffff8000, "ammin_db.wu", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0, 0, "ammin_db.du", "r,r,r,u0:0", "ammin_db.du %1,%2,%3", 0, 0, 0},
{0x38718000, 0xffff8000, "ammin_db.du", "r0:5,r10:5,r5:5", 0, 0, 0, 0},
{0x38720000, 0xffff8000, "dbar", "u0:15", 0, 0, 0, 0},
{0x38728000, 0xffff8000, "ibar", "u0:15", 0, 0, 0, 0},
{0x38740000, 0xffff8000, "fldgt.s", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38748000, 0xffff8000, "fldgt.d", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38750000, 0xffff8000, "fldle.s", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38758000, 0xffff8000, "fldle.d", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38760000, 0xffff8000, "fstgt.s", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38768000, 0xffff8000, "fstgt.d", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38770000, 0xffff8000, "fstle.s", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38778000, 0xffff8000, "fstle.d", "f0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38780000, 0xffff8000, "ldgt.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38788000, 0xffff8000, "ldgt.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38790000, 0xffff8000, "ldgt.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x38798000, 0xffff8000, "ldgt.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387a0000, 0xffff8000, "ldle.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387a8000, 0xffff8000, "ldle.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387b0000, 0xffff8000, "ldle.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387b8000, 0xffff8000, "ldle.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387c0000, 0xffff8000, "stgt.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387c8000, 0xffff8000, "stgt.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387d0000, 0xffff8000, "stgt.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387d8000, 0xffff8000, "stgt.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387e0000, 0xffff8000, "stle.b", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387e8000, 0xffff8000, "stle.h", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387f0000, 0xffff8000, "stle.w", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0x387f8000, 0xffff8000, "stle.d", "r0:5,r5:5,r10:5", 0, 0, 0, 0},
{0} /* Terminate the list.  */
};

static struct loongarch_opcode loongarch_jmp_opcodes[] = {
/* match,    mask,       name, format, macro, include, exclude, pinfo */
{0, 0, "beqz", "r,la", "beqz %1,%%pcrel(%2)", 0, 0, 0},
{0x40000000, 0xfc000000, "beqz", "r5:5,sb0:5|10:16<<2", 0, 0, 0, 0},
{0, 0, "bnez", "r,la", "bnez %1,%%pcrel(%2)", 0, 0, 0},
{0x44000000, 0xfc000000, "bnez", "r5:5,sb0:5|10:16<<2", 0, 0, 0, 0},
{0, 0, "bceqz", "c,la", "bceqz %1,%%pcrel(%2)", 0, 0, 0},
{0x48000000, 0xfc000300, "bceqz", "c5:3,sb0:5|10:16<<2", 0, 0, 0, 0},
{0, 0, "bcnez", "c,la", "bcnez %1,%%pcrel(%2)", 0, 0, 0},
{0x48000100, 0xfc000300, "bcnez", "c5:3,sb0:5|10:16<<2", 0, 0, 0, 0},
{0x48000200, 0xfc0003e0, "jiscr0", "s0:5|10:16<<2", 0, 0, 0, 0},
{0x48000300, 0xfc0003e0, "jiscr1", "s0:5|10:16<<2", 0, 0, 0, 0},
{0, 0, "jr", "r", "jirl $r0,%1,0", 0, 0, 0},
{0x4c000000, 0xfc000000, "jirl", "r0:5,r5:5,s10:16<<2", 0, 0, 0, 0},
{0, 0, "b", "la", "b %%pcrel(%1)", 0, 0, 0},
{0x50000000, 0xfc000000, "b", "sb0:10|10:16<<2", 0, 0, 0, 0},
{0, 0, "bl", "la", "bl %%pcrel(%1)", 0, 0, 0},
{0x54000000, 0xfc000000, "bl", "sb0:10|10:16<<2", 0, 0, 0, 0},
{0, 0, "beq", "r,r,la", "beq %1,%2,%%pcrel(%3)", 0, 0, 0},
{0x58000000, 0xfc000000, "beq", "r5:5,r0:5,sb10:16<<2", 0, 0, 0, 0},
{0, 0, "bne", "r,r,la", "bne %1,%2,%%pcrel(%3)", 0, 0, 0},
{0x5c000000, 0xfc000000, "bne", "r5:5,r0:5,sb10:16<<2", 0, 0, 0, 0},
{0, 0, "blt", "r,r,la", "blt %1,%2,%%pcrel(%3)", 0, 0, 0},
{0x60000000, 0xfc000000, "blt", "r5:5,r0:5,sb10:16<<2", 0, 0, 0, 0},
{0, 0, "bge", "r,r,la", "bge %1,%2,%%pcrel(%3)", 0, 0, 0},
{0x64000000, 0xfc000000, "bge", "r5:5,r0:5,sb10:16<<2", 0, 0, 0, 0},
{0, 0, "bltu", "r,r,la", "bltu %1,%2,%%pcrel(%3)", 0, 0, 0},
{0x68000000, 0xfc000000, "bltu", "r5:5,r0:5,sb10:16<<2", 0, 0, 0, 0},
{0, 0, "bgeu", "r,r,la", "bgeu %1,%2,%%pcrel(%3)", 0, 0, 0},
{0x6c000000, 0xfc000000, "bgeu", "r5:5,r0:5,sb10:16<<2", 0, 0, 0, 0},
{0} /* Terminate the list.  */
};

struct loongarch_ase loongarch_ASEs[] = {
{&LARCH_opts.ase_test, loongarch_test_opcodes, 0, 0, {0}, 0, 0},
{&LARCH_opts.ase_fix, loongarch_macro_opcodes, 0, 0, {0}, 0, 0},
{&LARCH_opts.ase_fix, loongarch_lmm_opcodes, 0, 0, {0}, 0, 0},
{&LARCH_opts.ase_fix, loongarch_privilege_opcodes, 0, 0, {0}, 0, 0},
{&LARCH_opts.ase_fix, loongarch_jmp_opcodes, 0, 0, {0}, 0, 0},
{&LARCH_opts.ase_fix, loongarch_load_store_opcodes, 0, 0, {0}, 0, 0},
{&LARCH_opts.ase_fix, loongarch_fix_opcodes, 0, 0, {0}, 0, 0},
{&LARCH_opts.ase_float, loongarch_4opt_opcodes, 0, 0, {0}, 0, 0},
{&LARCH_opts.ase_float, loongarch_float_opcodes, 0, 0, {0}, 0, 0},

{0},
};

int
loongarch_get_bit_field_width (const char *bit_field, char **end)
{
  int width = 0;
  char has_specify = 0, *bit_field_1 = (char *) bit_field;
  if (bit_field_1 && *bit_field_1 != '\0')
    while (1)
      {
	strtol (bit_field_1, &bit_field_1, 10);

	if (*bit_field_1 != ':')
	  break;
	bit_field_1++;

	width += strtol (bit_field_1, &bit_field_1, 10);
	has_specify = 1;

	if (*bit_field_1 != '|')
	  break;
	bit_field_1++;
      }
  if (end)
    *end = bit_field_1;
  return has_specify ? width : -1;
}

int32_t
loongarch_decode_imm (const char *bit_field, insn_t insn, int si)
{
  int32_t ret = 0;
  uint32_t t;
  int len = 0, width, b_start;
  char *bit_field_1 = (char *) bit_field;
  while (1)
    {
      b_start = strtol (bit_field_1, &bit_field_1, 10);
      if (*bit_field_1 != ':')
	break;
      width = strtol (bit_field_1 + 1, &bit_field_1, 10);
      len += width;

      t = insn;
      t <<= sizeof (t) * 8 - width - b_start;
      t >>= sizeof (t) * 8 - width;
      ret <<= width;
      ret |= t;

      if (*bit_field_1 != '|')
	break;
      bit_field_1++;
    }

  if (*bit_field_1 == '<' && *(++bit_field_1) == '<')
    {
      width = atoi(bit_field_1 + 1);
      ret <<= width;
      len += width;
    }
  else if (*bit_field_1 == '+')
    ret += atoi(bit_field_1 + 1);

  if (si)
    {
      ret <<= sizeof (ret) * 8 - len;
      ret >>= sizeof (ret) * 8 - len;
    }
  return ret;
}

static insn_t
loongarch_encode_imm (const char *bit_field, int32_t imm)
{
  char *bit_field_1 = (char *) bit_field;
  char *t = bit_field_1;
  int width, b_start;
  insn_t ret = 0, i;

  width = loongarch_get_bit_field_width (t, &t);
  if (width == -1)
    return ret;

  if (*t == '<' && *(++t) == '<')
    width += atoi (t + 1);
  else if (*t == '+')
    imm -= atoi (t + 1);

  imm <<= sizeof (imm) * 8 - width;
  while (1)
    {
      b_start = strtol (bit_field_1, &bit_field_1, 10);
      if (*bit_field_1 != ':')
	break;
      width = strtol (bit_field_1 + 1, &bit_field_1, 10);
      i = imm;
      i >>= sizeof (i) * 8 - width;
      i <<= b_start;
      ret |= i;
      imm <<= width;

      if (*bit_field_1 != '|')
	break;
      bit_field_1++;
    }
  return ret;
}

/* parse such FORMAT
     ""
     "u"
     "v0:5,r5:5,s10:10<<2"
     "r0:5,r5:5,r10:5,u15:2+1"
     "r,r,u0:5+32,u0:5+1"
*/
static int
loongarch_parse_format (const char *format,
			char *esc1s, char *esc2s, const char **bit_fields)
{
  size_t arg_num = 0;

  if (*format == '\0')
    goto end;

  while (1)
    {
      /*        esc1    esc2
	 for "[a-zA-Z][a-zA-Z]?" */
      if (('a' <= *format && *format <= 'z')
	  || ('A' <= *format && *format <= 'Z'))
	{
	  *esc1s++ = *format++;
	  if (('a' <= *format && *format <= 'z')
	      || ('A' <= *format && *format <= 'Z'))
	    *esc2s++ = *format++;
	  else
	    *esc2s++ = '\0';
	}
      else
	return -1;

      arg_num++;
      if (MAX_ARG_NUM_PLUS_2 - 2 < arg_num)
	/* need larger MAX_ARG_NUM_PLUS_2 */
	return -1;

      *bit_fields++ = format;

      if ('0' <= *format && *format <= '9')
	{
	  /* for "[0-9]+:[0-9]+(\|[0-9]+:[0-9]+)*" */
	  while (1)
	    {
	      while ('0' <= *format && *format <= '9')
		format++;

	      if (*format != ':')
		return -1;
	      format++;

	      if (!('0' <= *format && *format <= '9'))
		return -1;
	      while ('0' <= *format && *format <= '9')
		format++;

	      if (*format != '|')
		break;
	      format++;
	    }

	  /* for "((\+|<<)[1-9][0-9]*)?" */
	  do
	    {
	      if (*format == '+')
		format++;
	      else if (format[0] == '<' && format[1] == '<')
		format += 2;
	      else
		break;

	      if (!('1' <= *format && *format <= '9'))
		return -1;
	      while ('0' <= *format && *format <= '9')
		format++;
	    }
	  while (0);
	}

      if (*format == ',')
	format++;
      else if (*format == '\0')
	break;
      else
	return -1;
    }

end:
  *esc1s = '\0';
  return 0;
}

size_t
loongarch_split_args_by_comma (char *args, const char * arg_strs[])
{
  size_t num = 0;

  if (*args)
    arg_strs[num++] = args;
  for (; *args; args++)
    if (*args == ',')
      {
	if (MAX_ARG_NUM_PLUS_2 - 1 == num)
	  break;
	else
	  *args = '\0', arg_strs[num++] = args + 1;
      }
  arg_strs[num] = NULL;
  return num;
}

insn_t
loongarch_foreach_args (const char *format, const char *arg_strs[],
			int32_t (*helper) (char esc1, char esc2,
					   const char *bit_field,
					   const char *arg, void *context),
			void *context)
{
  char esc1s[MAX_ARG_NUM_PLUS_2 - 1], esc2s[MAX_ARG_NUM_PLUS_2 - 1];
  const char *bit_fields[MAX_ARG_NUM_PLUS_2 - 1];
  size_t i;
  insn_t ret = 0;
  int ok;

  ok = loongarch_parse_format (format, esc1s, esc2s, bit_fields) == 0;

  /* make sure the num of actual args is equal to the num of escape */
  for (i = 0; esc1s[i] && arg_strs[i]; i++);
  ok = ok && !esc1s[i] && !arg_strs[i];

  if (ok && helper)
    {
      for (i = 0; arg_strs[i]; i++)
	ret |= loongarch_encode_imm (bit_fields[i],
		 helper (esc1s[i], esc2s[i], bit_fields[i],
			 arg_strs[i], context));
      ret |= helper ('\0', '\0', NULL, NULL, context);
    }

  return ret;
}

static const struct loongarch_opcode *
get_loongarch_opcode_by_binfmt (insn_t insn)
{
  const struct loongarch_opcode *it;
  struct loongarch_ase *ase;
  size_t i;
  for (ase = loongarch_ASEs; ase->enabled; ase++)
    {
      if (!*ase->enabled
          || (ase->include && !*ase->include)
	  || (ase->exclude && *ase->exclude))
	continue;

      if (!ase->opc_htab_inited)
	{
	  for (it = ase->opcodes; it->mask; it++)
	    if (!ase->opc_htab[LARCH_INSN_OPC (it->match)]
		&& it->macro == NULL)
	    ase->opc_htab[LARCH_INSN_OPC (it->match)] = it;
	  for (i = 0; i < 16; i++)
	    if (!ase->opc_htab[i])
	      ase->opc_htab[i] = it;
	  ase->opc_htab_inited = 1;
	}

      it = ase->opc_htab[LARCH_INSN_OPC(insn)];
      for (; it->name; it++)
	if ((insn & it->mask) == it->match
	    && it->mask
	    && !(it->include && !*it->include)
	    && !(it->exclude && *it->exclude))
	  return it;
    }
  return NULL;
}

static const char * const *loongarch_r_disname = NULL;
static const char * const *loongarch_f_disname = NULL;
static const char * const *loongarch_c_disname = NULL;
static const char * const *loongarch_cr_disname = NULL;
static const char * const *loongarch_v_disname = NULL;
static const char * const *loongarch_x_disname = NULL;

static void
set_default_loongarch_dis_options (void)
{
  LARCH_opts.ase_test = 1;
  LARCH_opts.ase_fix = 1;
  LARCH_opts.ase_float = 1;

  loongarch_r_disname = loongarch_r_lp64_name;
  loongarch_f_disname = loongarch_f_normal_name;
  loongarch_c_disname = loongarch_c_normal_name;
  loongarch_cr_disname = loongarch_cr_normal_name;
  loongarch_v_disname = loongarch_v_normal_name;
  loongarch_x_disname = loongarch_x_normal_name;
}

static int
parse_loongarch_dis_option (const char *option)
{
  return -1;
}

static int
parse_loongarch_dis_options (const char *opts_in)
{
  set_default_loongarch_dis_options ();

  if (opts_in == NULL)
    return 0;

  char opts[strlen (opts_in) + 1], *opt, *opt_end;
  strcpy (opts, opts_in);

  for (opt = opt_end = opts; opt_end != NULL; opt = opt_end + 1)
    {
      if ((opt_end = strchr (opt, ',')) != NULL)
	*opt_end = 0;
      if (parse_loongarch_dis_option (opt) != 0)
	return -1;
    }
  return 0;
}

static int32_t
dis_one_arg (char esc1, char esc2, const char *bit_field,
	     const char *arg, void *context)
{
  static int need_comma = 0;
  struct disassemble_info *info = context;
  insn_t insn = *(insn_t *) info->private_data;
  int32_t imm, u_imm;

  if (esc1)
    {
      if (need_comma)
	info->fprintf_func (info->stream, ",");
      need_comma = 1;
      imm = loongarch_decode_imm (bit_field, insn, 1);
      u_imm = loongarch_decode_imm (bit_field, insn, 0);
    }

  switch (esc1)
    {
    case 'r':
      info->fprintf_func (info->stream, "%s", loongarch_r_disname[u_imm]);
      break;
    case 'f':
      info->fprintf_func (info->stream, "%s", loongarch_f_disname[u_imm]);
      break;
    case 'c':
      switch (esc2)
	{
	case 'r':
	  info->fprintf_func (info->stream, "%s", loongarch_cr_disname[u_imm]);
	  break;
	default:
	  info->fprintf_func (info->stream, "%s", loongarch_c_disname[u_imm]);
	}
      break;
    case 'v':
      info->fprintf_func (info->stream, "%s", loongarch_v_disname[u_imm]);
      break;
    case 'x':
      info->fprintf_func (info->stream, "%s", loongarch_x_disname[u_imm]);
      break;
    case 'u':
      info->fprintf_func (info->stream, "0x%x", u_imm);
      break;
    case 's':
      if (imm == 0)
	info->fprintf_func (info->stream, "%d", imm);
      else
	info->fprintf_func (info->stream, "%d(0x%x)", imm, u_imm);
      switch (esc2)
	{
	case 'b':
	  info->insn_type = dis_branch;
	  info->target += imm;
	}
      break;
    case '\0':
      need_comma = 0;
    }
  return 0;
}

static void
disassemble_one (insn_t insn, struct disassemble_info *info)
{
  const struct loongarch_opcode *opc = get_loongarch_opcode_by_binfmt (insn);
  if (!opc)
    {
      info->insn_type = dis_noninsn;
      info->fprintf_func (info->stream, "0x%08x", insn);
      return;
    }

  info->insn_type = dis_nonbranch;
  info->fprintf_func (info->stream, "%s", opc->name);

  {
    char fake_args[strlen (opc->format) + 1];
    const char *fake_arg_strs[MAX_ARG_NUM_PLUS_2];
    strcpy (fake_args, opc->format);
    if (0 < loongarch_split_args_by_comma (fake_args, fake_arg_strs))
      info->fprintf_func (info->stream, "\t");
    info->private_data = &insn;
    loongarch_foreach_args (opc->format, fake_arg_strs, dis_one_arg, info);
  }

  if (info->insn_type == dis_branch || info->insn_type == dis_condbranch
      /* || someother if we have extra info to print */)
    info->fprintf_func (info->stream, " #");

  if (info->insn_type == dis_branch || info->insn_type == dis_condbranch)
    {
      info->fprintf_func (info->stream, " ");
      info->print_address_func (info->target, info);
    }
}

int
loongarch_parse_dis_options (const char *opts_in)
{
  return parse_loongarch_dis_options (opts_in);
}

static void
my_print_address_func (int64_t addr, struct disassemble_info *dinfo)
{
  dinfo->fprintf_func (dinfo->stream, "0x%llx", (long long) addr);
}

void
loongarch_disassemble_one (int64_t pc, unsigned int insn,
			   int (*fprintf_func)
			     (void *stream, const char *format, ...),
			   void *stream)
{
  static struct disassemble_info my_disinfo = {
    .print_address_func = my_print_address_func,
  };
  static int not_init_yet = 1;
  if (not_init_yet)
    {
      loongarch_parse_dis_options (NULL);
      not_init_yet = 0;
    }

  my_disinfo.fprintf_func = fprintf_func;
  my_disinfo.stream = stream;
  my_disinfo.target = pc;
  disassemble_one (insn, &my_disinfo);
}

static int disas_rawreg;

typedef struct CPUDebug {
    char *buf;
} CPUDebug;

/**************************************************************/
static int disas_printf(void *out, const char *fmt, ...)
{
	CPUDebug *s = out;
	va_list ap;
	int n;
	va_start(ap, fmt);
	n = vsprintf(s->buf, fmt, ap);
	va_end(ap);
	s->buf += n;
	return n;
}

typedef struct elf_sym {
  const char *st_name;		/* Symbol name, index in string tbl */
 unsigned long st_value;		/* Value of the symbol */
  long st_size;		/* Associated symbol size */
} elf_sym;
elf_sym __attribute__((weak)) *symtab;
char *sprintf_ip_sym(unsigned long address)
{
	int i;
	int ret;
	int idx = -1;
	unsigned long diff = -1UL;
#define NAMELEN 256
        static char name[NAMELEN];

	for (i = 0; symtab && symtab[i].st_name; i++) {

		if (address >= symtab[i].st_value
			&& address < symtab[i].st_value + symtab[i].st_size) {
			idx = i;
			diff = 0;
			break;
		} else if (address >= symtab[i].st_value
			&& address - symtab[i].st_value < diff) {
			idx = i;
			diff = address - symtab[i].st_value;
		}
}

	if (idx >= 0)
			snprintf(name, NAMELEN, "0x%08lx <%s+0x%lx>", address,
				symtab[idx].st_name,
				address - symtab[idx].st_value);
	else
		snprintf(name, NAMELEN, "0x%08lx");
	return name;
}

void * 
md_disasm (
     char *dest,
     void *addr
     )
{
	CPUDebug s;
	unsigned int instcode = *(int *)addr;
	s.buf = dest;
	s.buf += sprintf(s.buf, "%s", sprintf_ip_sym(addr));
	s.buf += sprintf(s.buf, ": ");
	s.buf += sprintf(s.buf, " %08x ", instcode);
	loongarch_disassemble_one(addr, instcode, disas_printf, &s);
	return addr + 4;
}

int disas_printbuf(unsigned long long lastaddr, void *codebuf, int len)
{
	int i;
	unsigned int *pui_module = codebuf;
	char buf[128];
	for(i = 0; i < len; i += 4,lastaddr += 4)
	{
	if (!adr2symoff (buf, (long)lastaddr, 12))
		sprintf(buf, "%016lx", (long)lastaddr);
		printf("%-40s ", buf);
		printf("%08x   ", pui_module[i/4]);
		loongarch_disassemble_one(lastaddr, pui_module[i/4], (void *)fprintf, stdout);
		printf("\r\n");
	}
	return 0;
}

static int
loongarch_insn_is_branch_and_must_branch (int insn)
{
  if ((insn & 0xfc000000) == 0x4c000000		/* jirl r0:5,r5:5,s10:16<<2 */
      || (insn & 0xfc000000) == 0x50000000	/* b sb0:10|10:16<<2 */
      || (insn & 0xfc000000) == 0x54000000	/* bl sb0:10|10:16<<2 */
      || (insn & 0xfc0003e0) == 0x48000200	/* jiscr0 s0:5|10:16<<2 */
      || (insn & 0xfc0003e0) == 0x48000300)	/* jiscr1 s0:5|10:16<<2 */
    return 1;
  return 0;
}

static int
loongarch_insn_is_branch_and_cond_branch (int insn)
{
  if ((insn & 0xfc000000) == 0x40000000	/* beqz r5:5,sb0:5|10:16<<2 */
      || (insn & 0xfc000000) == 0x44000000	/* bnez r5:5,sb0:5|10:16<<2 */
      || (insn & 0xfc000300) == 0x48000000	/* bceqz c5:3,sb0:5|10:16<<2 */
      || (insn & 0xfc000300) == 0x48000100	/* bcnez c5:3,sb0:5|10:16<<2 */
      || (insn & 0xfc000000) == 0x58000000	/* beq r5:5,r0:5,sb10:16<<2 */
      || (insn & 0xfc000000) == 0x5c000000	/* bne r5:5,r0:5,sb10:16<<2 */
      || (insn & 0xfc000000) == 0x60000000	/* blt r5:5,r0:5,sb10:16<<2 */
      || (insn & 0xfc000000) == 0x64000000	/* bge r5:5,r0:5,sb10:16<<2 */
      || (insn & 0xfc000000) == 0x68000000	/* bltu r5:5,r0:5,sb10:16<<2 */
      || (insn & 0xfc000000) == 0x6c000000)	/* bgeu r5:5,r0:5,sb10:16<<2 */
    return 1;
  return 0;
}

static int
loongarch_insn_is_branch (int insn)
{
  if (loongarch_insn_is_branch_and_must_branch (insn)
	  || loongarch_insn_is_branch_and_cond_branch(insn))
    return 1;
  return 0;
}

static unsigned long long 
loongarch_next_pc_if_branch (int insn)
{
  unsigned long long next_pc;
  unsigned long long cur_pc = cpuinfotab[whatcpu]->csrepc;

  if ((insn & 0xfc000000) == 0x40000000		/* beqz r5:5,sb0:5|10:16<<2 */
      || (insn & 0xfc000000) == 0x44000000	/* bnez r5:5,sb0:5|10:16<<2 */
      || (insn & 0xfc000300) == 0x48000000	/* bceqz c5:3,sb0:5|10:16<<2 */
      || (insn & 0xfc000300) == 0x48000100)	/* bcnez c5:3,sb0:5|10:16<<2 */
    next_pc = cur_pc + loongarch_decode_imm ("0:5|10:16<<2", insn, 1);
  else if ((insn & 0xfc000000) == 0x4c000000)	/* jirl r0:5,r5:5,s10:16<<2 */
    next_pc = REGREG[loongarch_decode_imm ("5:5", insn, 0)]
	    + loongarch_decode_imm ("10:16<<2", insn, 1);
  else if ((insn & 0xfc000000) == 0x50000000	/* b sb0:10|10:16<<2 */
	   || (insn & 0xfc000000) == 0x54000000)/* bl sb0:10|10:16<<2 */
    next_pc = cur_pc + loongarch_decode_imm ("0:10|10:16<<2", insn, 1);
  else if ((insn & 0xfc000000) == 0x58000000	/* beq r5:5,r0:5,sb10:16<<2 */
	   || (insn & 0xfc000000) == 0x5c000000	/* bne r5:5,r0:5,sb10:16<<2 */
	   || (insn & 0xfc000000) == 0x60000000	/* blt r5:5,r0:5,sb10:16<<2 */
	   || (insn & 0xfc000000) == 0x64000000	/* bge r5:5,r0:5,sb10:16<<2 */
	   || (insn & 0xfc000000) == 0x68000000	/* bltu r5:5,r0:5,sb10:16<<2 */
	   || (insn & 0xfc000000) == 0x6c000000)/* bgeu r5:5,r0:5,sb10:16<<2 */
    next_pc = cur_pc + loongarch_decode_imm ("10:16<<2", insn, 1);
  else
    return -1ULL;

  return next_pc;
}


int
md_is_branch(adr)
	void *adr;
{
	u_int32_t inst;
	inst  = *(int *)adr;
	return loongarch_insn_is_branch(inst);
}

int
md_is_cond_branch(adr)
	void *adr;
{
	u_int32_t inst;
	inst  = *(int *)adr;
	return loongarch_insn_is_branch_and_cond_branch(inst);
}

int
md_is_jr (adr)
	void *adr;
{
	u_int32_t inst;
	inst  = *(int *)adr;
	if ((inst & 0xfc000000) == 0x4c000000)
		return 1;
	else
		return 0;
}

void *
md_branch_target(adr)
	void *adr;
{
	u_int32_t inst;
	inst  = *(int *)adr;
	return loongarch_next_pc_if_branch(inst);
}

int
md_is_call (adr)
	void *adr;
{
	u_int32_t inst;
	inst  = *(int *)adr;

	if ((inst & 0xfc000000) == 0x54000000)
		return 1;

	if ((inst & 0xfc000000) == 0x4c000000 && (inst & 0x3e0))
		return 1;
	return (0);
}

int
md_is_writeable (addr)
	 void *addr;
{
	u_int32_t x;

	x = *(int *)addr;
	*(int *)addr = ~x;

	if (*(int *)addr != ~x)
		return (0);
	*(int *)addr = x;
	return (1);
}

/*************************************************************
 *  dispchist(args,siz)
 *	  display the pc history (trace buffer)
 */
int
dispchist(args, siz)
	int		 args, siz;
{
	int  i, l;
	void *adr;

	l = siz;
	for (i = 0;; i++) {
		adr = (void *)getpchist(i);
		if (adr == 0)
			break;
		md_disasm (prnbuf, adr);
		if (more (prnbuf, &l, (args > 1) ? 0 : siz))
			break;
	}
	return(0);
}

/*************************************************************
 * dis(ac,av)
 * the 'l' (disassemble) command
 */
int rflag;			/* Wanting effective addresses for load/store instructions */
int rsvalue;			/* Computed by rs() macro for displaying load/store effective addrs */
int rtvalue;
int do_rt;
int do_rs;

int
md_disassemble(ac, av)
	int 	ac;
	char 	*av[];
{
	void *adr;
	int siz, l;
	int bflag, cflag, i, j, n, tflag;
	static void *last_adr;	/* For repeat of l command */
	void *prev_adr;		/* For figuring print of a0-a3 after jal type inst. */

	rflag = bflag = cflag = n = tflag = 0;

	siz = moresz;
	adr = md_get_excpc;

	for (i = 1; i < ac; i++) {
		if (av[i][0] == '-') {
			for (j = 1; av[i][j] != 0; j++) {
				switch (av[i][j]) {
				case 'b':
					bflag = 1;
					break;
				case 'c':
					cflag = 1;
					break;
				case 't':
					tflag = 1;
					n++;
					break;
				case 'r':
					rflag = 1;
					break;
				default:
					printf("%c: unknown option\n", av[i][j]);
					return (-1);
				}
			}
		} else {
			switch (n) {
			case 0:
				if (!get_rsa_reg (&adr, av[i]))
					return (-1);
				break;
			case 1:
				if (!get_rsa (&siz, av[i]))
					return (-1);
				break;
			default:
				printf("%s: unknown option\n", av[i]);
				return (-1);
			}
			n++;
		}
	}

	if (repeating_cmd)
		adr = last_adr - 4;

	ioctl (STDIN, CBREAK, NULL);

	if (tflag) {
		dispchist(n, siz);
		rflag = 0;
		return (0);
	}

	l = siz;

	if (cflag || bflag)
		printf("%s", searching);
	while (1) {
	/* Enable this if you want a 'label' at the start of each
	 * procedure.
	 * if (adr2sym(prnbuf,adr)) {
	 * strcat(prnbuf,":");
	 * if (more(prnbuf,&l,(n>1)?0:siz)) break;
	 * }
	 */
		if (cflag || bflag) {
			int match;
			char *p;
			if (cflag)
				match = md_is_call (adr);
			else
				match = md_is_branch (adr);
		if (!match) {
			dotik (128, 0);
			adr += 4L;
			continue;
		}
		p = searching;
		while (*p++)
			printf("\b \b");
	}
	prev_adr = adr;
	adr = md_disasm (prnbuf, adr);
	last_adr = adr;
	if (more (prnbuf, &l, (n > 1) ? 0 : siz))
		break;
	if (rflag && (md_is_call(prev_adr))) {
		/*
		 * We're showing registers and we just displayed
		 * a JAL type instruction.  Show a0-a3
		 */

		printf("\t\t\t# a0=0x%x a1=0x%x a2=0x%x a3=0x%x\n",
		cpuinfotab[whatcpu]->a0, cpuinfotab[whatcpu]->a1,
		cpuinfotab[whatcpu]->a2, cpuinfotab[whatcpu]->a3);
	}

	if (cflag || bflag)
		printf("%s", searching);
	}
	rflag = 0;
	return (0);
}

static int cmd_syms(int argc, char **argv)
{
	int i;
	if (argc == 1) {
		for (i = 0; symtab && symtab[i].st_name; i++) {
			printf("%-30s 0x%lx\n", symtab[i].st_name, symtab[i].st_value);
		}
	} else if (argc > 1 && argv[1][0] >= '0' && argv[1][0] <= '9') {
		unsigned long address = strtoul(argv[1], 0, 0);
		printf("%s\n", sprintf_ip_sym(address));
		return 0;
	} else {
		int l = strlen(argv[1]);
		for (i = 0; symtab && symtab[i].st_name; i++) {
			if (!strncmp(argv[1], symtab[i].st_name, l)) {
			printf("%-30s 0x%lx\n", symtab[i].st_name, symtab[i].st_value);
			}
		}
	}
	return 0;
}
