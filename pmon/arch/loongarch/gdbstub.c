/*
 * Kernel Debug Core
 *
 * Copyright (C) 2000-2001 VERITAS Software Corporation.
 * Copyright (C) 2002-2004 Timesys Corporation
 * Copyright (C) 2003-2004 Amit <PERSON> <<EMAIL>>
 * Copyright (C) 2004 <PERSON> <<EMAIL>>
 * Copyright (C) 2004-2006 <PERSON> <<EMAIL>>
 * Copyright (C) 2004-2006 LinSysSoft Technologies Pvt. Ltd.
 * Copyright (C) 2005-2009 Wind River Systems, Inc.
 * Copyright (C) 2007 MontaVista Software, Inc.
 * Copyright (C) 2008 Red Hat, Inc., Ingo Molnar <<EMAIL>>
 * Copyright (C) 2025 Loongson Technology Corporation Limited <<EMAIL>>
 *
 * Contributors at various stages not listed above:
 *  <PERSON> ( <EMAIL> )
 *  <PERSON> <geor<PERSON>@mvista.com>
 *  Anurekh Saxena (<EMAIL>)
 *  Lake Stevens Instrument Division (Glenn Engel)
 *  <PERSON>, Cygnus Support.
 *
 * Original KGDB stub: <PERSON> <<EMAIL>>,
 * T<PERSON>ran <PERSON> <<EMAIL>>
 *
 * This file is licensed under the terms of the GNU General Public License
 * version 2. This program is licensed "as is" without any warranty of any
 * kind, whether express or implied.
 */
#include <cpu.h>
#include "loongarchregs.h"
#include "errno.h"
#include <linux/list.h>
#include <ctype.h>
#include <sys/signal.h>
#include <unaligned.h>
#include <pmon.h>
#include <sys/types.h>
#include <linux/types.h>
#include <file.h>
#include <cmd_hist.h>
#define __weak __attribute__((weak))
#define noinline __attribute__((noinline))

#define LOONGARCH_CPUCFG0		0x0
#define EXCCODE_ALE		9	/* Unalign Access */
#define EXCCODE_TLBL		1	/* TLB miss on a load */
#define LOONGARCH_CSR_ESTAT		0x5	/* Exception status */
#define  CSR_ESTAT_ESUBCODE_SHIFT	22
#define  CSR_ESTAT_ESUBCODE_WIDTH	9
#define  CSR_ESTAT_ESUBCODE		(_ULCAST_(0x1ff) << CSR_ESTAT_ESUBCODE_SHIFT)
#define  CSR_ESTAT_EXC_SHIFT		16
#define  CSR_ESTAT_EXC_WIDTH		6
#define  CSR_ESTAT_EXC			(_ULCAST_(0x3f) << CSR_ESTAT_EXC_SHIFT)
#define  CSR_ESTAT_IS_SHIFT		0
#define  CSR_ESTAT_IS_WIDTH		15
#define  CSR_ESTAT_IS			(_ULCAST_(0x7fff) << CSR_ESTAT_IS_SHIFT)
#define LOONGARCH_CSR_ECFG		0x4	/* Exception config */
#define LOONGARCH_CSR_CPUNUM		0x20	/* CPU core number */
#define  CSR_PRMD_PWE_SHIFT		3
#define  CSR_PRMD_PWE			(_ULCAST_(0x1) << CSR_PRMD_PWE_SHIFT)
#define LOONGARCH_CSR_CRMD		0x0	/* Current mode info */
#define  CSR_CRMD_WE_SHIFT		9
#define  CSR_CRMD_WE			(_ULCAST_(0x1) << CSR_CRMD_WE_SHIFT)
#define LOONGARCH_CSR_TLBREPC		0x8a	/* TLB refill EPC */
/* Debug registers */
#define LOONGARCH_CSR_MWPC		0x300	/* data breakpoint config */
#define LOONGARCH_CSR_MWPS		0x301	/* data breakpoint status */

#define LOONGARCH_CSR_DB0ADDR		0x310	/* data breakpoint 0 address */
#define LOONGARCH_CSR_DB0MASK		0x311	/* data breakpoint 0 mask */
#define LOONGARCH_CSR_DB0CTL		0x312	/* data breakpoint 0 control */
#define LOONGARCH_CSR_DB0ASID		0x313	/* data breakpoint 0 asid */

#define LOONGARCH_CSR_DB1ADDR		0x318	/* data breakpoint 1 address */
#define LOONGARCH_CSR_DB1MASK		0x319	/* data breakpoint 1 mask */
#define LOONGARCH_CSR_DB1CTL		0x31a	/* data breakpoint 1 control */
#define LOONGARCH_CSR_DB1ASID		0x31b	/* data breakpoint 1 asid */

#define LOONGARCH_CSR_DB2ADDR		0x320	/* data breakpoint 2 address */
#define LOONGARCH_CSR_DB2MASK		0x321	/* data breakpoint 2 mask */
#define LOONGARCH_CSR_DB2CTL		0x322	/* data breakpoint 2 control */
#define LOONGARCH_CSR_DB2ASID		0x323	/* data breakpoint 2 asid */

#define LOONGARCH_CSR_DB3ADDR		0x328	/* data breakpoint 3 address */
#define LOONGARCH_CSR_DB3MASK		0x329	/* data breakpoint 3 mask */
#define LOONGARCH_CSR_DB3CTL		0x32a	/* data breakpoint 3 control */
#define LOONGARCH_CSR_DB3ASID		0x32b	/* data breakpoint 3 asid */

#define LOONGARCH_CSR_DB4ADDR		0x330	/* data breakpoint 4 address */
#define LOONGARCH_CSR_DB4MASK		0x331	/* data breakpoint 4 maks */
#define LOONGARCH_CSR_DB4CTL		0x332	/* data breakpoint 4 control */
#define LOONGARCH_CSR_DB4ASID		0x333	/* data breakpoint 4 asid */

#define LOONGARCH_CSR_DB5ADDR		0x338	/* data breakpoint 5 address */
#define LOONGARCH_CSR_DB5MASK		0x339	/* data breakpoint 5 mask */
#define LOONGARCH_CSR_DB5CTL		0x33a	/* data breakpoint 5 control */
#define LOONGARCH_CSR_DB5ASID		0x33b	/* data breakpoint 5 asid */

#define LOONGARCH_CSR_DB6ADDR		0x340	/* data breakpoint 6 address */
#define LOONGARCH_CSR_DB6MASK		0x341	/* data breakpoint 6 mask */
#define LOONGARCH_CSR_DB6CTL		0x342	/* data breakpoint 6 control */
#define LOONGARCH_CSR_DB6ASID		0x343	/* data breakpoint 6 asid */

#define LOONGARCH_CSR_DB7ADDR		0x348	/* data breakpoint 7 address */
#define LOONGARCH_CSR_DB7MASK		0x349	/* data breakpoint 7 mask */
#define LOONGARCH_CSR_DB7CTL		0x34a	/* data breakpoint 7 control */
#define LOONGARCH_CSR_DB7ASID		0x34b	/* data breakpoint 7 asid */

#define LOONGARCH_CSR_FWPC		0x380	/* instruction breakpoint config */
#define LOONGARCH_CSR_FWPS		0x381	/* instruction breakpoint status */

#define LOONGARCH_CSR_IB0ADDR		0x390	/* inst breakpoint 0 address */
#define LOONGARCH_CSR_IB0MASK		0x391	/* inst breakpoint 0 mask */
#define LOONGARCH_CSR_IB0CTL		0x392	/* inst breakpoint 0 control */
#define LOONGARCH_CSR_IB0ASID		0x393	/* inst breakpoint 0 asid */

#define LOONGARCH_CSR_IB1ADDR		0x398	/* inst breakpoint 1 address */
#define LOONGARCH_CSR_IB1MASK		0x399	/* inst breakpoint 1 mask */
#define LOONGARCH_CSR_IB1CTL		0x39a	/* inst breakpoint 1 control */
#define LOONGARCH_CSR_IB1ASID		0x39b	/* inst breakpoint 1 asid */

#define LOONGARCH_CSR_IB2ADDR		0x3a0	/* inst breakpoint 2 address */
#define LOONGARCH_CSR_IB2MASK		0x3a1	/* inst breakpoint 2 mask */
#define LOONGARCH_CSR_IB2CTL		0x3a2	/* inst breakpoint 2 control */
#define LOONGARCH_CSR_IB2ASID		0x3a3	/* inst breakpoint 2 asid */

#define LOONGARCH_CSR_IB3ADDR		0x3a8	/* inst breakpoint 3 address */
#define LOONGARCH_CSR_IB3MASK		0x3a9	/* breakpoint 3 mask */
#define LOONGARCH_CSR_IB3CTL		0x3aa	/* inst breakpoint 3 control */
#define LOONGARCH_CSR_IB3ASID		0x3ab	/* inst breakpoint 3 asid */

#define LOONGARCH_CSR_IB4ADDR		0x3b0	/* inst breakpoint 4 address */
#define LOONGARCH_CSR_IB4MASK		0x3b1	/* inst breakpoint 4 mask */
#define LOONGARCH_CSR_IB4CTL		0x3b2	/* inst breakpoint 4 control */
#define LOONGARCH_CSR_IB4ASID		0x3b3	/* inst breakpoint 4 asid */

#define LOONGARCH_CSR_IB5ADDR		0x3b8	/* inst breakpoint 5 address */
#define LOONGARCH_CSR_IB5MASK		0x3b9	/* inst breakpoint 5 mask */
#define LOONGARCH_CSR_IB5CTL		0x3ba	/* inst breakpoint 5 control */
#define LOONGARCH_CSR_IB5ASID		0x3bb	/* inst breakpoint 5 asid */

#define LOONGARCH_CSR_IB6ADDR		0x3c0	/* inst breakpoint 6 address */
#define LOONGARCH_CSR_IB6MASK		0x3c1	/* inst breakpoint 6 mask */
#define LOONGARCH_CSR_IB6CTL		0x3c2	/* inst breakpoint 6 control */
#define LOONGARCH_CSR_IB6ASID		0x3c3	/* inst breakpoint 6 asid */

#define LOONGARCH_CSR_IB7ADDR		0x3c8	/* inst breakpoint 7 address */
#define LOONGARCH_CSR_IB7MASK		0x3c9	/* inst breakpoint 7 mask */
#define LOONGARCH_CSR_IB7CTL		0x3ca	/* inst breakpoint 7 control */
#define LOONGARCH_CSR_IB7ASID		0x3cb	/* inst breakpoint 7 asid */

#define LOONGARCH_CSR_DEBUG		0x500	/* debug config */
#define LOONGARCH_CSR_DEPC		0x501	/* debug epc */
#define LOONGARCH_CSR_DESAVE		0x502	/* debug save */

struct pt_regs {
	/* Saved main processor registers. */
	unsigned long regs[32];

	/* Saved special registers. */
	unsigned long csr_crmd;
	unsigned long csr_prmd;
	unsigned long csr_euen;
	unsigned long csr_misc;
	unsigned long csr_ecfg;
	unsigned long csr_estat;
	unsigned long csr_epc;
	unsigned long csr_badvaddr;
	unsigned long csr_badi;
#ifdef HAVE_FLOAT
	struct loongarch_fpu fpu;
#endif
};

/*
 * These are the private implementation headers between the kernel
 * debugger core and the debugger front end code.
 */

/* kernel debug core data structures */
struct kgdb_state {
	int			ex_vector;
	int			signo;
	int			err_code;
	int			cpu;
	int			pass_exception;
	unsigned long		thr_query;
	unsigned long		threadid;
	long			kgdb_usethreadid;
	struct pt_regs		*linux_regs;
	int		*send_ready;
};

/* Exception state values */
#define DCPU_WANT_MASTER 0x1 /* Waiting to become a master kgdb cpu */
#define DCPU_NEXT_MASTER 0x2 /* Transition from one master cpu to another */
#define DCPU_IS_SLAVE    0x4 /* Slave cpu enter exception */
#define DCPU_SSTEP       0x8 /* CPU is single stepping */

static int cmdloop(struct pt_regs *regs);
static int gdb_write(int fd, char *buf, int count);
static int swicth_gdb_console(void);
/* kernel debug core break point routines */
extern int dbg_remove_all_break(void);
extern int dbg_set_sw_break(unsigned long addr);
extern int dbg_remove_sw_break(unsigned long addr);
extern int dbg_activate_sw_breakpoints(void);
extern int dbg_deactivate_sw_breakpoints(void);

/* stub return value for switching between the gdbstub and kdb */
#define DBG_PASS_EVENT -12345

/* gdbstub interface functions */
static int gdb_serial_stub(struct kgdb_state *ks);
static void gdbstub_msg_write(const char *s, int len);

#ifndef DBG_MAX_REG_NUM
#define DBG_MAX_REG_NUM 0
#else
extern struct dbg_reg_def_t dbg_reg_def[];
extern char *dbg_get_reg(int regno, void *mem, struct pt_regs *regs);
extern int dbg_set_reg(int regno, void *mem, struct pt_regs *regs);
#endif
#ifndef KGDB_MAX_BREAKPOINTS
# define KGDB_MAX_BREAKPOINTS	1000
#endif

#define KGDB_HW_BREAKPOINT	1

/*
 * Functions each KGDB-supporting architecture must provide:
 */

static int
kgdb_arch_handle_exception(int vector, int signo, int err_code,
			   char *remcom_in_buffer,
			   char *remcom_out_buffer,
			   struct pt_regs *regs);


/* Optional functions. */
extern int kgdb_hex2long(char **ptr, unsigned long *long_val);
extern char *kgdb_mem2hex(char *mem, char *buf, int count);
extern int kgdb_hex2mem(char *buf, char *mem, int count);

extern int
kgdb_handle_exception(int ex_vector, int signo, int err_code,
		      struct pt_regs *regs);
#define GDB_SIZEOF_REG	     __SIZEOF_LONG__

#define BUFMAX			2048
#define DBG_MAX_REG_NUM		34
#define DBG_FP_FCC0_REGNO	(DBG_MAX_REG_NUM + 32)
#define DBG_FP_FSR_REGNO	(DBG_FP_FCC0_REGNO + 8)
#define DBG_ALL_REG_NUM		(DBG_FP_FSR_REGNO + 1)
#define NUMREGBYTES		(DBG_MAX_REG_NUM * __SIZEOF_LONG__)
#define BREAK_INSTR_SIZE	4
#define CACHE_FLUSH_IS_SAFE	0

enum kgdb_bptype {
	BP_BREAKPOINT = 0,
	BP_HARDWARE_BREAKPOINT,
	BP_WRITE_WATCHPOINT,
	BP_READ_WATCHPOINT,
	BP_ACCESS_WATCHPOINT,
	BP_POKE_BREAKPOINT,
};

enum kgdb_bpstate {
	BP_UNDEFINED = 0,
	BP_REMOVED,
	BP_SET,
	BP_ACTIVE
};

struct kgdb_bkpt {
	unsigned long		bpt_addr;
	unsigned char		saved_instr[BREAK_INSTR_SIZE];
	enum kgdb_bptype	type;
	enum kgdb_bpstate	state;
};

struct dbg_reg_def_t {
	char *name;
	int size;
	int offset;
};

extern void arch_kgdb_breakpoint(void);
extern void breakinst(void);
static inline u32 read_cpucfg(u32 reg)
{
	u64 val;
	__asm__ __volatile__ (
		"cpucfg %[val], %[reg] \n\t"
		: [val] "=r" (val)
		: [reg] "r" (reg)
		: "memory");
	return val;
}
#define csr_readq csr_read64
static inline u64 csr_read64(u32 reg)
{
	u64 val;
	__asm__ __volatile__ (
		"csrrd %[val], %[reg] \n\t"
		: [val] "=r" (val)
		: [reg] "i" (reg)
		: "memory");
	return val;
}
#define csr_writeq csr_write64
static inline void csr_write64(u64 val, u32 reg)
{
	__asm__ __volatile__ (
		"csrwr %[val], %[reg] \n\t"
		: [val] "+r" (val)
		: [reg] "i" (reg)
		: "memory");
}
#define csr_xchgl csr_xchg32
static inline u32 csr_xchg32(u32 val, u32 mask, u32 reg)
{
	__asm__ __volatile__ (
		"csrxchg %[val], %[mask], %[reg] \n\t"
		: [val] "+r" (val)
		: [mask] "r" (mask), [reg] "i" (reg)
		: "memory");
	return val;
}
int arch_has_hw_breakpoint();
int arch_set_hw_breakpoint(unsigned long address,int length, enum kgdb_bptype type, unsigned long addrmask);
int arch_remove_hw_breakpoint(unsigned long address,int length, enum kgdb_bptype type);
int arch_breakpoint_inst();
static char gdb_rcmd_buf[512];
static int gdb_cmd_len;
static const char hex_asc[] = "0123456789abcdef";
#define hex_asc_lo(x)	hex_asc[((x) & 0x0f)]
#define hex_asc_hi(x)	hex_asc[((x) & 0xf0) >> 4]
#define tolower(c) (((c)>='A' && (c)<='Z')?((c)-'A'+'a'):(c))
int hex_to_bin(char ch)
{
	if ((ch >= '0') && (ch <= '9'))
		return ch - '0';
	ch = tolower(ch);
	if ((ch >= 'a') && (ch <= 'f'))
		return ch - 'a' + 10;
	return -1;
}

static inline char *hex_byte_pack(char *buf, unsigned char byte)
{
	*buf++ = hex_asc_hi(byte);
	*buf++ = hex_asc_lo(byte);
	return buf;
}

struct dbg_reg_def_t dbg_reg_def[DBG_ALL_REG_NUM] =
{
	{ "r0", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[0]) },
	{ "r1", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[1]) },
	{ "r2", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[2]) },
	{ "r3", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[3]) },
	{ "r4", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[4]) },
	{ "r5", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[5]) },
	{ "r6", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[6]) },
	{ "r7", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[7]) },
	{ "r8", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[8]) },
	{ "r9", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[9]) },
	{ "r10", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[10]) },
	{ "r11", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[11]) },
	{ "r12", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[12]) },
	{ "r13", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[13]) },
	{ "r14", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[14]) },
	{ "r15", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[15]) },
	{ "r16", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[16]) },
	{ "r17", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[17]) },
	{ "r18", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[18]) },
	{ "r19", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[19]) },
	{ "r20", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[20]) },
	{ "r21", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[21]) },
	{ "r22", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[22]) },
	{ "r23", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[23]) },
	{ "r24", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[24]) },
	{ "r25", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[25]) },
	{ "r26", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[26]) },
	{ "r27", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[27]) },
	{ "r28", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[28]) },
	{ "r29", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[29]) },
	{ "r30", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[30]) },
	{ "r31", GDB_SIZEOF_REG, offsetof(struct pt_regs, regs[31]) },
	{ "pc", GDB_SIZEOF_REG, offsetof(struct pt_regs, csr_epc) },
	{ "badv", GDB_SIZEOF_REG, offsetof(struct pt_regs, csr_badvaddr) },
	{ "f0", GDB_SIZEOF_REG, 0 },
	{ "f1", GDB_SIZEOF_REG, 1 },
	{ "f2", GDB_SIZEOF_REG, 2 },
	{ "f3", GDB_SIZEOF_REG, 3 },
	{ "f4", GDB_SIZEOF_REG, 4 },
	{ "f5", GDB_SIZEOF_REG, 5 },
	{ "f6", GDB_SIZEOF_REG, 6 },
	{ "f7", GDB_SIZEOF_REG, 7 },
	{ "f8", GDB_SIZEOF_REG, 8 },
	{ "f9", GDB_SIZEOF_REG, 9 },
	{ "f10", GDB_SIZEOF_REG, 10 },
	{ "f11", GDB_SIZEOF_REG, 11 },
	{ "f12", GDB_SIZEOF_REG, 12 },
	{ "f13", GDB_SIZEOF_REG, 13 },
	{ "f14", GDB_SIZEOF_REG, 14 },
	{ "f15", GDB_SIZEOF_REG, 15 },
	{ "f16", GDB_SIZEOF_REG, 16 },
	{ "f17", GDB_SIZEOF_REG, 17 },
	{ "f18", GDB_SIZEOF_REG, 18 },
	{ "f19", GDB_SIZEOF_REG, 19 },
	{ "f20", GDB_SIZEOF_REG, 20 },
	{ "f21", GDB_SIZEOF_REG, 21 },
	{ "f22", GDB_SIZEOF_REG, 22 },
	{ "f23", GDB_SIZEOF_REG, 23 },
	{ "f24", GDB_SIZEOF_REG, 24 },
	{ "f25", GDB_SIZEOF_REG, 25 },
	{ "f26", GDB_SIZEOF_REG, 26 },
	{ "f27", GDB_SIZEOF_REG, 27 },
	{ "f28", GDB_SIZEOF_REG, 28 },
	{ "f29", GDB_SIZEOF_REG, 29 },
	{ "f30", GDB_SIZEOF_REG, 30 },
	{ "f31", GDB_SIZEOF_REG, 31 },
	{ "fcc0", 1, 0 },
	{ "fcc1", 1, 1 },
	{ "fcc2", 1, 2 },
	{ "fcc3", 1, 3 },
	{ "fcc4", 1, 4 },
	{ "fcc5", 1, 5 },
	{ "fcc6", 1, 6 },
	{ "fcc7", 1, 7 },
	{ "fcsr", GDB_SIZEOF_REG, 0 },
};

int dbg_set_reg(int regno, void *mem, struct pt_regs *regs)
{
	int fp_reg;

	if (regno < 0 || regno >= DBG_ALL_REG_NUM)
		return -EINVAL;

	if (dbg_reg_def[regno].offset != -1 && regno < DBG_MAX_REG_NUM) {
		memcpy((void *)regs + dbg_reg_def[regno].offset, mem,
		       dbg_reg_def[regno].size);
	} 
#ifdef HAVE_FLOAT
	else if (dbg_reg_def[regno].offset != -1 && regno < DBG_ALL_REG_NUM) {
		/* FP registers 32 -> 77 */
		if (!(regs->csr_euen & CSR_EUEN_FPEN))
			return 0;
		if (regno == DBG_FP_FSR_REGNO) {
			/* Process the fcsr/fsr (register 74) */
			memcpy((void *)&regs->fpu.fcsr, mem,
			       dbg_reg_def[regno].size);
		} else if (regno >= DBG_FP_FCC0_REGNO && regno < DBG_FP_FSR_REGNO) {
			/* Process the fcc */
			fp_reg = dbg_reg_def[regno].offset;
			memcpy((char *)&regs->fpu.fcc + fp_reg, mem,
			       dbg_reg_def[regno].size);
		} else {
		fp_reg = dbg_reg_def[regno].offset;
		memcpy((void *)&regs->fpu.fpr[fp_reg], mem,
		       dbg_reg_def[regno].size);
		}
	}
#endif

	return 0;
}

char *dbg_get_reg(int regno, void *mem, struct pt_regs *regs)
{
	int fp_reg;

	if (regno >= DBG_ALL_REG_NUM || regno < 0)
		return NULL;

	if (dbg_reg_def[regno].offset != -1 && regno < DBG_MAX_REG_NUM) {
		/* First 32 registers, epc, badv */
		memcpy(mem, (void *)regs + dbg_reg_def[regno].offset,
		       dbg_reg_def[regno].size);
	}
#ifdef HAVE_FLOAT
	 else if (dbg_reg_def[regno].offset != -1 && regno < DBG_ALL_REG_NUM) {
		/* FP registers 32 -> 77 */
		if (!(regs->csr_euen & CSR_EUEN_FPEN))
			goto out;
		if (regno == DBG_FP_FSR_REGNO) {
			/* Process the fcr31/fsr (register 74) */
			memcpy(mem, (void *)&regs->fpu.fcsr,
			       dbg_reg_def[regno].size);
		} else if (regno >= DBG_FP_FCC0_REGNO && regno < DBG_FP_FSR_REGNO) {
			/* Process the fcc */
			fp_reg = dbg_reg_def[regno].offset;
			memcpy(mem, (char *)&regs->fpu.fcc + fp_reg,
			       dbg_reg_def[regno].size);
		} else {
		fp_reg = dbg_reg_def[regno].offset;
		memcpy(mem, (void *)&regs->fpu.fpr[fp_reg],
		       dbg_reg_def[regno].size);
		}
	}
#endif

out:
	return dbg_reg_def[regno].name;

}

static int maped_address(unsigned long long ui_address)
{
#if defined(LOADADDR) && defined(MEMSIZE)
	if (ui_address >= LOADADDR && ui_address < LOADADDR + MEMSIZE)
		return 0;
#endif
	if (sizeof(long) == 4)
		return 0;
	if (sizeof(long) == 8 && (ui_address < UNCACHED_MEMORY_ADDR || ui_address >= CACHED_MEMORY_ADDR + (1ULL<<60)))
		return 1;
	return 0;
}

long probe_kernel_read(void *dst, const void *src, size_t size)
{
	if (maped_address((unsigned long long)src))
		return -EFAULT;
	memcpy(dst, src, size);

	return 0;
}

long probe_kernel_write(void *dst, const void *src, size_t size)
{
	if (maped_address((unsigned long long)dst))
		return -EFAULT;
	memcpy(dst, src, size);

	return 0;
}

#define KGDB_MAX_THREAD_QUERY 17
int kgdb_connected;

/* Our I/O buffers. */
static char			remcom_in_buffer[BUFMAX];
static char			remcom_out_buffer[BUFMAX];
static int			gdbstub_use_prev_in_buf;
static int			gdbstub_prev_in_buf_pos;

/* Storage for the registers, in GDB format. */
static unsigned long		gdb_regs[(NUMREGBYTES +
					sizeof(unsigned long) - 1) /
					sizeof(unsigned long)];

#define NO_POLL_CHAR		0x00ff0000
static int serial8250_get_poll_char()
{
	if (!tgt_testchar())
		return NO_POLL_CHAR;

	return tgt_getchar();
}

/*
 * GDB remote protocol parser:
 */

static int gdbstub_read_wait(void)
{
	int ret = serial8250_get_poll_char();
	while (ret == NO_POLL_CHAR)
		ret = serial8250_get_poll_char();
	return ret;
}

/* scan for the sequence $<data>#<checksum> */
static void get_packet(char *buffer)
{
	unsigned char checksum;
	unsigned char xmitcsum;
	int count;
	char ch;

	do {
		/*
		 * Spin and wait around for the start character, ignore all
		 * other characters:
		 */
		while ((ch = (gdbstub_read_wait())) != '$')
			/* nothing */;

		checksum = 0;
		xmitcsum = -1;

		count = 0;

		/*
		 * now, read until a # or end of buffer is found:
		 */
		while (count < (BUFMAX - 1)) {
			ch = gdbstub_read_wait();
			if (ch == '#')
				break;
			checksum = checksum + ch;
			buffer[count] = ch;
			count = count + 1;
		}

		if (ch == '#') {
			xmitcsum = hex_to_bin(gdbstub_read_wait()) << 4;
			xmitcsum += hex_to_bin(gdbstub_read_wait());

			if (checksum != xmitcsum)
				/* failed checksum */
				tgt_putchar('-');
			else
				/* successful transfer */
				tgt_putchar('+');
		}
		buffer[count] = 0;
	} while (checksum != xmitcsum);
}

static int run_gdb_cmd(char *remcom_out_buffer)
{
	swicth_gdb_console();
	do_cmd(remcom_out_buffer);
	swicth_gdb_console();
	return 0;
}

/*
 * Send the packet in buffer.
 * Check for gdb connection if asked for.
 */
static void put_packet(char *buffer)
{
	unsigned char checksum;
	int count;
	char ch;

	/*
	 * $<packet info>#<checksum>.
	 */
	while (1) {
		tgt_putchar('$');
		checksum = 0;
		count = 0;

		while ((ch = buffer[count])) {
			tgt_putchar(ch);
			checksum += ch;
			count++;
		}

		tgt_putchar('#');
		tgt_putchar(hex_asc_hi(checksum));
		tgt_putchar(hex_asc_lo(checksum));

		/* Now see what we get in reply. */
		ch = gdbstub_read_wait();

		if (ch == 3)
			ch = gdbstub_read_wait();

		/* If we get an ACK, we are done. */
		if (ch == '+')
			return;

		/*
		 * If we get the start of another packet, this means
		 * that GDB is attempting to reconnect.  We will NAK
		 * the packet being sent, and stop trying to send this
		 * packet.
		 */
		if (ch == '$') {
			tgt_putchar('-');
			return;
		}
	}
}

static char gdbmsgbuf[BUFMAX + 1];

static void gdbstub_msg_write(const char *s, int len)
{
	char *bufptr;
	int wcount;
	int i;

	if (len == 0)
		len = strlen(s);

	/* 'O'utput */
	gdbmsgbuf[0] = 'O';

	/* Fill and send buffers... */
	while (len > 0) {
		bufptr = gdbmsgbuf + 1;

		/* Calculate how many this time */
		if ((len << 1) > (BUFMAX - 2))
			wcount = (BUFMAX - 2) >> 1;
		else
			wcount = len;

		/* Pack in hex chars */
		for (i = 0; i < wcount; i++)
			bufptr = hex_byte_pack(bufptr, s[i]);
		*bufptr = '\0';

		/* Move up */
		s += wcount;
		len -= wcount;

		/* Write packet */
		put_packet(gdbmsgbuf);
	}
}

/*
 * Convert the memory pointed to by mem into hex, placing result in
 * buf.  Return a pointer to the last char put in buf (null). May
 * return an error.
 */
char *kgdb_mem2hex(char *mem, char *buf, int count)
{
	char *tmp;
	int err;

	/*
	 * We use the upper half of buf as an intermediate buffer for the
	 * raw memory copy.  Hex conversion will work against this one.
	 */
	tmp = buf + count;

	err = probe_kernel_read(tmp, mem, count);
	if (err)
		return NULL;
	while (count > 0) {
		buf = hex_byte_pack(buf, *tmp);
		tmp++;
		count--;
	}
	*buf = 0;

	return buf;
}

/*
 * Convert the hex array pointed to by buf into binary to be placed in
 * mem.  Return a pointer to the character AFTER the last byte
 * written.  May return an error.
 */
int kgdb_hex2mem(char *buf, char *mem, int count)
{
	char *tmp_raw;
	char *tmp_hex;

	/*
	 * We use the upper half of buf as an intermediate buffer for the
	 * raw memory that is converted from hex.
	 */
	tmp_raw = buf + count * 2;

	tmp_hex = tmp_raw - 1;
	while (tmp_hex >= buf) {
		tmp_raw--;
		*tmp_raw = hex_to_bin(*tmp_hex--);
		*tmp_raw |= hex_to_bin(*tmp_hex--) << 4;
	}

	return probe_kernel_write(mem, tmp_raw, count);
}

/*
 * While we find nice hex chars, build a long_val.
 * Return number of chars processed.
 */
int kgdb_hex2long(char **ptr, unsigned long *long_val)
{
	int hex_val;
	int num = 0;
	int negate = 0;

	*long_val = 0;

	if (**ptr == '-') {
		negate = 1;
		(*ptr)++;
	}
	while (**ptr) {
		hex_val = hex_to_bin(**ptr);
		if (hex_val < 0)
			break;

		*long_val = (*long_val << 4) | hex_val;
		num++;
		(*ptr)++;
	}

	if (negate)
		*long_val = -*long_val;

	return num;
}

int kgdb_hex2longaddr(char **ptr, unsigned long *long_val)
{
	char *ptr0 = *ptr;
	int ret;
	ret = kgdb_hex2long(ptr, long_val);
	if (*ptr - ptr0 < 16 && (*long_val & (1<<31)))
		*long_val = (int)*long_val;
	return ret;
}

/*
 * Copy the binary array pointed to by buf into mem.  Fix $, #, and
 * 0x7d escaped with 0x7d. Return -EFAULT on failure or 0 on success.
 * The input buf is overwitten with the result to write to mem.
 */
static int kgdb_ebin2mem(char *buf, char *mem, int count)
{
	int size = 0;
	char *c = buf;

	while (count-- > 0) {
		c[size] = *buf++;
		if (c[size] == 0x7d)
			c[size] = *buf++ ^ 0x20;
		size++;
	}

	return probe_kernel_write(mem, c, size);
}

#if DBG_MAX_REG_NUM > 0
void pt_regs_to_gdb_regs(unsigned long *gdb_regs, struct pt_regs *regs)
{
	int i;
	int idx = 0;
	char *ptr = (char *)gdb_regs;

	for (i = 0; i < DBG_MAX_REG_NUM; i++) {
		dbg_get_reg(i, ptr + idx, regs);
		idx += dbg_reg_def[i].size;
	}
}

void gdb_regs_to_pt_regs(unsigned long *gdb_regs, struct pt_regs *regs)
{
	int i;
	int idx = 0;
	char *ptr = (char *)gdb_regs;

	for (i = 0; i < DBG_MAX_REG_NUM; i++) {
		dbg_set_reg(i, ptr + idx, regs);
		idx += dbg_reg_def[i].size;
	}
}
#endif /* DBG_MAX_REG_NUM > 0 */

/* Write memory due to an 'M' or 'X' packet. */
static int write_mem_msg(int binary)
{
	char *ptr = &remcom_in_buffer[1];
	unsigned long addr;
	unsigned long length;
	int err;

	if (kgdb_hex2longaddr(&ptr, &addr) > 0 && *(ptr++) == ',' &&
	    kgdb_hex2long(&ptr, &length) > 0 && *(ptr++) == ':') {
		if (binary)
			err = kgdb_ebin2mem(ptr, (char *)addr, length);
		else
			err = kgdb_hex2mem(ptr, (char *)addr, length);
		if (err)
			return err;
		//if (CACHE_FLUSH_IS_SAFE)
		//	flush_icache_range(addr, addr + length);
		return 0;
	}

	return -EINVAL;
}

static void error_packet(char *pkt, int error)
{
	error = -error;
	pkt[0] = 'E';
	pkt[1] = hex_asc[(error / 10)];
	pkt[2] = hex_asc[(error % 10)];
	pkt[3] = '\0';
}

/*
 * Thread ID accessors. We represent a flat TID space to GDB, where
 * the per CPU idle threads (which under Linux all have PID 0) are
 * remapped to negative TIDs.
 */

#define BUF_THREAD_ID_SIZE	8

static char *pack_threadid(char *pkt, unsigned char *id)
{
	unsigned char *limit;
	int lzero = 1;

	limit = id + (BUF_THREAD_ID_SIZE / 2);
	while (id < limit) {
		if (!lzero || *id != 0) {
			pkt = hex_byte_pack(pkt, *id);
			lzero = 0;
		}
		id++;
	}

	if (lzero)
		pkt = hex_byte_pack(pkt, 0);

	return pkt;
}

static void int_to_threadref(unsigned char *id, int value)
{
	put_unaligned_be32(value, id);
}

static void *getthread(struct pt_regs *regs, int tid)
{
	return tid > -2 ? 0 : (void *)tid;
}


/*
 * Remap normal tasks to their real PID,
 * CPU shadow threads are mapped to -CPU - 2
 */
static inline int shadow_pid(int realpid)
{
	long cpunum;
	if (realpid)
		return realpid;
	cpunum = csr_readq(LOONGARCH_CSR_CPUNUM);

	return  -cpunum - 2;
}

/*
 * All the functions that start with gdb_cmd are the various
 * operations to implement the handlers for the gdbserial protocol
 * where KGDB is communicating with an external debugger
 */

/* Handle the '?' status packets */
static void gdb_cmd_status(struct kgdb_state *ks)
{
	/*
	 * We know that this packet is only sent
	 * during initial connect.  So to be safe,
	 * we clear out our breakpoints now in case
	 * GDB is reconnecting.
	 */
	dbg_remove_all_break();

	remcom_out_buffer[0] = 'S';
	hex_byte_pack(&remcom_out_buffer[1], ks->signo);
}

static void gdb_get_regs_helper(struct kgdb_state *ks)
{
	pt_regs_to_gdb_regs(gdb_regs, ks->linux_regs);
}

/* Handle the 'g' get registers request */
static void gdb_cmd_getregs(struct kgdb_state *ks)
{
	gdb_get_regs_helper(ks);
	kgdb_mem2hex((char *)gdb_regs, remcom_out_buffer, NUMREGBYTES);
}

/* Handle the 'G' set registers request */
static void gdb_cmd_setregs(struct kgdb_state *ks)
{
	kgdb_hex2mem(&remcom_in_buffer[1], (char *)gdb_regs, NUMREGBYTES);

		gdb_regs_to_pt_regs(gdb_regs, ks->linux_regs);
		strcpy(remcom_out_buffer, "OK");
}

/* Handle the 'm' memory read bytes */
static void gdb_cmd_memread(struct kgdb_state *ks)
{
	char *ptr = &remcom_in_buffer[1];
	unsigned long length;
	unsigned long addr;
	char *err;

	if (kgdb_hex2longaddr(&ptr, &addr) > 0 && *(ptr++) == ',' &&
					kgdb_hex2long(&ptr, &length) > 0) {
		err = kgdb_mem2hex((char *)addr, remcom_out_buffer, length);
		if (!err)
			error_packet(remcom_out_buffer, -EINVAL);
	} else {
		error_packet(remcom_out_buffer, -EINVAL);
	}
}

/* Handle the 'M' memory write bytes */
static void gdb_cmd_memwrite(struct kgdb_state *ks)
{
	int err = write_mem_msg(0);

	if (err)
		error_packet(remcom_out_buffer, err);
	else
		strcpy(remcom_out_buffer, "OK");
}

#if DBG_MAX_REG_NUM > 0
static char *gdb_hex_reg_helper(int regnum, char *out)
{
	int i;
	int offset = 0;

	for (i = 0; i < regnum; i++)
		offset += dbg_reg_def[i].size;
	return kgdb_mem2hex((char *)gdb_regs + offset, out,
			    dbg_reg_def[i].size);
}

/* Handle the 'p' individual regster get */
static void gdb_cmd_reg_get(struct kgdb_state *ks)
{
	unsigned long regnum;
	char *ptr = &remcom_in_buffer[1];

	kgdb_hex2long(&ptr, &regnum);
	if (regnum >= DBG_MAX_REG_NUM) {
		error_packet(remcom_out_buffer, -EINVAL);
		return;
	}
	gdb_get_regs_helper(ks);
	gdb_hex_reg_helper(regnum, remcom_out_buffer);
}

/* Handle the 'P' individual regster set */
static void gdb_cmd_reg_set(struct kgdb_state *ks)
{
	unsigned long regnum;
	char *ptr = &remcom_in_buffer[1];
	int i = 0;

	kgdb_hex2long(&ptr, &regnum);
	if (*ptr++ != '=' ||
	    !dbg_get_reg(regnum, gdb_regs, ks->linux_regs)) {
		error_packet(remcom_out_buffer, -EINVAL);
		return;
	}
	memset(gdb_regs, 0, sizeof(gdb_regs));
	while (i < sizeof(gdb_regs) * 2)
		if (hex_to_bin(ptr[i]) >= 0)
			i++;
		else
			break;
	i = i / 2;
	kgdb_hex2mem(ptr, (char *)gdb_regs, i);
	dbg_set_reg(regnum, gdb_regs, ks->linux_regs);
	strcpy(remcom_out_buffer, "OK");
}
#endif /* DBG_MAX_REG_NUM > 0 */

/* Handle the 'X' memory binary write bytes */
static void gdb_cmd_binwrite(struct kgdb_state *ks)
{
	int err = write_mem_msg(1);

	if (err)
		error_packet(remcom_out_buffer, err);
	else
		strcpy(remcom_out_buffer, "OK");
}

/* Handle the 'D' or 'k', detach or kill packets */
static void gdb_cmd_detachkill(struct kgdb_state *ks)
{
	int error;

	/* The detach case */
	if (remcom_in_buffer[0] == 'D') {
		error = dbg_remove_all_break();
		if (error < 0) {
			error_packet(remcom_out_buffer, error);
		} else {
			strcpy(remcom_out_buffer, "OK");
		}
		put_packet(remcom_out_buffer);
	} else {
		/*
		 * Assume the kill case, with no exit code checking,
		 * trying to force detach the debugger:
		 */
		dbg_remove_all_break();
	}
}

/* Handle the 'R' reboot packets */
static int gdb_cmd_reboot(struct kgdb_state *ks)
{
	/* For now, only honor R0 */
	if (strcmp(remcom_in_buffer, "R0") == 0) {
		printf("Executing emergency reboot\n");
		strcpy(remcom_out_buffer, "OK");
		put_packet(remcom_out_buffer);

		/*
		 * Execution should not return from
		 * machine_emergency_restart()
		 */
		do_cmd("reboot");

		return 1;
	}
	return 0;
}

/* Handle the 'q' query packets */
static void gdb_cmd_query(struct kgdb_state *ks)
{
	unsigned char thref[BUF_THREAD_ID_SIZE];
	char *ptr;
	int i;
	int cpu;
	int finished = 0;

	switch (remcom_in_buffer[1]) {
	case 's':
	case 'f':
		if (memcmp(remcom_in_buffer + 2, "ThreadInfo", 10))
			break;

		i = 0;
		remcom_out_buffer[0] = 'm';
		ptr = remcom_out_buffer + 1;
		if (remcom_in_buffer[1] == 'f') {
			/* Each cpu is a shadow thread */
				ks->thr_query = 0;
				int_to_threadref(thref, shadow_pid(0));
				ptr = pack_threadid(ptr, thref);
				*(ptr++) = ',';
				i++;
		}

		*(--ptr) = '\0';
		break;

	case 'C':
		/* Current thread id */
		strcpy(remcom_out_buffer, "QC");
		ks->threadid = shadow_pid(0);
		int_to_threadref(thref, ks->threadid);
		pack_threadid(remcom_out_buffer + 2, thref);
		break;
	case 'T':
		if (memcmp(remcom_in_buffer + 1, "ThreadExtraInfo,", 16))
			break;

		ks->threadid = 0;
		ptr = remcom_in_buffer + 17;
		kgdb_hex2long(&ptr, &ks->threadid);
		if (!getthread(ks->linux_regs, ks->threadid)) {
			error_packet(remcom_out_buffer, -EINVAL);
			break;
		}
		{
			static char tmpstr[23 + BUF_THREAD_ID_SIZE];

			sprintf(tmpstr, "shadowCPU%d",
					(int)(-ks->threadid - 2));
			kgdb_mem2hex(tmpstr, remcom_out_buffer, strlen(tmpstr));
		}
		break;
	case 'R':
		if (strncmp(remcom_in_buffer, "qRcmd,", 6) == 0) {
			int len = strlen(remcom_in_buffer + 6);

			if ((len % 2) != 0) {
				strcpy(remcom_out_buffer, "E01");
				break;
			}
			kgdb_hex2mem(remcom_in_buffer + 6,
				     remcom_out_buffer, len);
			len = len / 2;
			remcom_out_buffer[len++] = 0;
			gdb_cmd_len = 0;
			run_gdb_cmd(remcom_out_buffer);
			if (gdb_cmd_len)
				kgdb_mem2hex(gdb_rcmd_buf, remcom_out_buffer, gdb_cmd_len);
			else
				strcpy(remcom_out_buffer, "OK");
		}
		break;
	}
}

/* Handle the 'H' task query packets */
static void gdb_cmd_task(struct kgdb_state *ks)
{
	void *thread;
	char *ptr;

	switch (remcom_in_buffer[1]) {
	case 'g':
		ptr = &remcom_in_buffer[2];
		kgdb_hex2long(&ptr, &ks->threadid);
		thread = getthread(ks->linux_regs, ks->threadid);
		ks->kgdb_usethreadid = ks->threadid;
		strcpy(remcom_out_buffer, "OK");
		break;
	case 'c':
		ptr = &remcom_in_buffer[2];
		kgdb_hex2long(&ptr, &ks->threadid);
		strcpy(remcom_out_buffer, "OK");
		break;
	}
}

/* Handle the 'T' thread query packets */
static void gdb_cmd_thread(struct kgdb_state *ks)
{
	char *ptr = &remcom_in_buffer[1];
	void *thread;

	kgdb_hex2long(&ptr, &ks->threadid);
	thread = getthread(ks->linux_regs, ks->threadid);
	if (thread)
		strcpy(remcom_out_buffer, "OK");
	else
		error_packet(remcom_out_buffer, -EINVAL);
}

int kgdb_arch_set_breakpoint(struct kgdb_bkpt *bpt)
{
	int err;
	unsigned inst = arch_breakpoint_inst();

	memcpy(bpt->saved_instr, (char *)bpt->bpt_addr,
				BREAK_INSTR_SIZE);
	memcpy((char *)bpt->bpt_addr,
				 &inst, BREAK_INSTR_SIZE);
	return 0;
}

int __weak kgdb_arch_remove_breakpoint(struct kgdb_bkpt *bpt)
{
	memcpy((char *)bpt->bpt_addr, (char *)bpt->saved_instr, BREAK_INSTR_SIZE);
	return 0;
}

int __weak kgdb_validate_break_address(unsigned long addr)
{
	struct kgdb_bkpt tmp;
	int err;
	/* Validate setting the breakpoint and then removing it.  If the
	 * remove fails, the kernel needs to emit a bad message because we
	 * are deep trouble not being able to put things back the way we
	 * found them.
	 */
	tmp.bpt_addr = addr;
	err = kgdb_arch_set_breakpoint(&tmp);
	if (err)
		return err;
	err = kgdb_arch_remove_breakpoint(&tmp);
	if (err)
		printf("KGDB: Critical breakpoint error, kernel "
		   "memory destroyed at: %lx", addr);
	return err;
}

/*
 * Holds information about breakpoints in a kernel. These breakpoints are
 * added and removed by gdb.
 */
static struct kgdb_bkpt		kgdb_break[KGDB_MAX_BREAKPOINTS] = {
	[0 ... KGDB_MAX_BREAKPOINTS-1] = { .state = BP_UNDEFINED }
};

__weak void kgdb_flush_swbreak_addr(unsigned long addr)
{
}
/*
 * SW breakpoint management:
 */
int dbg_activate_sw_breakpoints(void)
{
	int error;
	int ret = 0;
	int i;

	for (i = 0; i < KGDB_MAX_BREAKPOINTS; i++) {
		if (kgdb_break[i].state != BP_SET)
			continue;

		error = kgdb_arch_set_breakpoint(&kgdb_break[i]);
		if (error) {
			ret = error;
			printf("KGDB: BP install failed: %lx",
			       kgdb_break[i].bpt_addr);
			continue;
		}

		kgdb_flush_swbreak_addr(kgdb_break[i].bpt_addr);
		kgdb_break[i].state = BP_ACTIVE;
	}
	return ret;
}

int dbg_deactivate_sw_breakpoints(void)
{
	int error;
	int ret = 0;
	int i;

	for (i = 0; i < KGDB_MAX_BREAKPOINTS; i++) {
		if (kgdb_break[i].state != BP_ACTIVE)
			continue;
		error = kgdb_arch_remove_breakpoint(&kgdb_break[i]);
		if (error) {
			printf("KGDB: BP remove failed: %lx\n",
			       kgdb_break[i].bpt_addr);
			ret = error;
		}

		kgdb_flush_swbreak_addr(kgdb_break[i].bpt_addr);
		kgdb_break[i].state = BP_SET;
	}
	return ret;
}

int dbg_remove_sw_break(unsigned long addr)
{
	int i;

	for (i = 0; i < KGDB_MAX_BREAKPOINTS; i++) {
		if ((kgdb_break[i].state == BP_SET) &&
				(kgdb_break[i].bpt_addr == addr)) {
			kgdb_break[i].state = BP_REMOVED;
			return 0;
		}
	}
	return -ENOENT;
}

int dbg_remove_all_break(void)
{
	int error;
	int i;

	/* Clear memory breakpoints. */
	for (i = 0; i < KGDB_MAX_BREAKPOINTS; i++) {
		if (kgdb_break[i].state != BP_ACTIVE)
			goto setundefined;
		error = kgdb_arch_remove_breakpoint(&kgdb_break[i]);
		if (error)
			printf("KGDB: breakpoint remove failed: %lx\n",
			       kgdb_break[i].bpt_addr);
setundefined:
		kgdb_break[i].state = BP_UNDEFINED;
	}

	return 0;
}

int dbg_set_sw_break(unsigned long addr)
{
	int err = kgdb_validate_break_address(addr);
	int breakno = -1;
	int i;

	if (err)
		return err;

	for (i = 0; i < KGDB_MAX_BREAKPOINTS; i++) {
		if ((kgdb_break[i].state == BP_SET) &&
					(kgdb_break[i].bpt_addr == addr))
			return -EEXIST;
	}
	for (i = 0; i < KGDB_MAX_BREAKPOINTS; i++) {
		if (kgdb_break[i].state == BP_REMOVED &&
					kgdb_break[i].bpt_addr == addr) {
			breakno = i;
			break;
		}
	}

	if (breakno == -1) {
		for (i = 0; i < KGDB_MAX_BREAKPOINTS; i++) {
			if (kgdb_break[i].state == BP_UNDEFINED) {
				breakno = i;
				break;
			}
		}
	}

	if (breakno == -1)
		return -E2BIG;

	kgdb_break[breakno].state = BP_SET;
	kgdb_break[breakno].type = BP_BREAKPOINT;
	kgdb_break[breakno].bpt_addr = addr;

	return 0;
}

/* Handle the 'z' or 'Z' breakpoint remove or set packets */
static noinline void gdb_cmd_break(struct kgdb_state *ks)
{
	/*
	 * Since GDB-5.3, it's been drafted that '0' is a software
	 * breakpoint, '1' is a hardware breakpoint, so let's do that.
	 */
	char *bpt_type = &remcom_in_buffer[1];
	char *ptr = &remcom_in_buffer[2];
	unsigned long addr;
	unsigned long length;
	int error = 0;

	if (arch_has_hw_breakpoint() && *bpt_type >= '1') {
		/* Unsupported */
		if (*bpt_type > '4')
			return;
	} else {
		if (*bpt_type != '0' && *bpt_type != '1')
			/* Unsupported. */
			return;
	}

	/*
	 * Test if this is a hardware breakpoint, and
	 * if we support it:
	 */
	if (*bpt_type == '1' && !arch_has_hw_breakpoint())
		/* Unsupported. */
		return;

	if (*(ptr++) != ',') {
		error_packet(remcom_out_buffer, -EINVAL);
		return;
	}
	if (!kgdb_hex2longaddr(&ptr, &addr)) {
		error_packet(remcom_out_buffer, -EINVAL);
		return;
	}
	if (*(ptr++) != ',' ||
		!kgdb_hex2long(&ptr, &length)) {
		error_packet(remcom_out_buffer, -EINVAL);
		return;
	}

	if (remcom_in_buffer[0] == 'Z' && *bpt_type == '0')
		error = dbg_set_sw_break(addr);
	else if (remcom_in_buffer[0] == 'z' && *bpt_type == '0')
		error = dbg_remove_sw_break(addr);
	else if (remcom_in_buffer[0] == 'Z')
		error = arch_set_hw_breakpoint(addr,
					(int)length, *bpt_type - '0', 0);
	else if (remcom_in_buffer[0] == 'z')
		error = arch_remove_hw_breakpoint(addr,
			(int) length, *bpt_type - '0');

	if (error == 0)
		strcpy(remcom_out_buffer, "OK");
	else
		error_packet(remcom_out_buffer, error);
}

/* Handle the 'C' signal / exception passing packets */
static int gdb_cmd_exception_pass(struct kgdb_state *ks)
{
	/* C09 == pass exception
	 * C15 == detach kgdb, pass exception
	 */
	if (remcom_in_buffer[1] == '0' && remcom_in_buffer[2] == '9') {

		ks->pass_exception = 1;
		remcom_in_buffer[0] = 'c';

	} else if (remcom_in_buffer[1] == '1' && remcom_in_buffer[2] == '5') {

		ks->pass_exception = 1;
		remcom_in_buffer[0] = 'D';
		dbg_remove_all_break();
		return 1;

	} else {
		gdbstub_msg_write("KGDB only knows signal 9 (pass)"
			" and 15 (pass and disconnect)\n"
			"Executing a continue without signal passing\n", 0);
		remcom_in_buffer[0] = 'c';
	}

	/* Indicate fall through */
	return -1;
}

static void __show_regs(const struct pt_regs *regs)
{
	const int field = 2 * sizeof(unsigned long);
	unsigned int excsubcode;
	unsigned int exccode;
	int i;

	printf("\n");

	/*
	 * Saved main processor registers
	 */
	for (i = 0; i < 32; ) {
		if ((i % 4) == 0)
			printf("$%2d   :", i);
		printf(" %0*lx", field, regs->regs[i]);

		i++;
		if ((i % 4) == 0)
			printf("\n");
	}

	/*
	 * Saved csr registers
	 */
	printf("epc   : %0*lx %pS\n", field, regs->csr_epc,
	       (void *) regs->csr_epc);
	printf("ra    : %0*lx %pS\n", field, regs->regs[1],
	       (void *) regs->regs[1]);

	printf("CSR crmd: %08lx	", regs->csr_crmd);
	printf("CSR prmd: %08lx	", regs->csr_prmd);
	printf("CSR ecfg: %08llx	", csr_readq(LOONGARCH_CSR_ECFG));
	printf("CSR estat: %08lx	", regs->csr_estat);
	printf("CSR euen: %08lx	", regs->csr_euen);

	printf("\n");

	exccode = ((regs->csr_estat) & CSR_ESTAT_EXC) >> CSR_ESTAT_EXC_SHIFT;
	excsubcode = ((regs->csr_estat) & CSR_ESTAT_ESUBCODE) >> CSR_ESTAT_ESUBCODE_SHIFT;
	printf("ExcCode : %x (SubCode %x)\n", exccode, excsubcode);

	if (exccode >= EXCCODE_TLBL && exccode <= EXCCODE_ALE)
		printf("BadVA : %0*lx\n", field, regs->csr_badvaddr);

	printf("PrId  : %08x\n", read_cpucfg(LOONGARCH_CPUCFG0));
}

/*
 * This function performs all gdbserial command procesing
 */
static int gdb_serial_stub(struct kgdb_state *ks)
{
	int error = 0;
	int tmp;

	/* Initialize comm buffer and globals. */
	memset(remcom_out_buffer, 0, sizeof(remcom_out_buffer));
	ks->kgdb_usethreadid = shadow_pid(0);
	ks->pass_exception = 0;

	if (kgdb_connected) {
		unsigned char thref[BUF_THREAD_ID_SIZE];
		char *ptr;

		/* Reply to host that an exception has occurred */
		ptr = remcom_out_buffer;
		*ptr++ = 'T';
		ptr = hex_byte_pack(ptr, ks->signo);
		ptr += strlen(strcpy(ptr, "thread:"));
		int_to_threadref(thref, shadow_pid(0));
		ptr = pack_threadid(ptr, thref);
		*ptr++ = ';';
		put_packet(remcom_out_buffer);
	}


	while (1) {
		error = 0;

		/* Clear the out buffer. */
		memset(remcom_out_buffer, 0, sizeof(remcom_out_buffer));

		get_packet(remcom_in_buffer);

		switch (remcom_in_buffer[0]) {
		case '?': /* gdbserial status */
			gdb_cmd_status(ks);
			break;
		case 'g': /* return the value of the CPU registers */
			gdb_cmd_getregs(ks);
			break;
		case 'G': /* set the value of the CPU registers - return OK */
			gdb_cmd_setregs(ks);
			break;
		case 'm': /* mAA..AA,LLLL  Read LLLL bytes at address AA..AA */
			gdb_cmd_memread(ks);
			break;
		case 'M': /* MAA..AA,LLLL: Write LLLL bytes at address AA..AA */
			gdb_cmd_memwrite(ks);
			break;
#if DBG_MAX_REG_NUM > 0
		case 'p': /* pXX Return gdb register XX (in hex) */
			gdb_cmd_reg_get(ks);
			break;
		case 'P': /* PXX=aaaa Set gdb register XX to aaaa (in hex) */
			gdb_cmd_reg_set(ks);
			break;
#endif /* DBG_MAX_REG_NUM > 0 */
		case 'X': /* XAA..AA,LLLL: Write LLLL bytes at address AA..AA */
			gdb_cmd_binwrite(ks);
			break;
			/* kill or detach. KGDB should treat this like a
			 * continue.
			 */
		case 'D': /* Debugger detach */
		case 'k': /* Debugger detach via kill */
			gdb_cmd_detachkill(ks);
			goto default_handle;
		case 'R': /* Reboot */
			if (gdb_cmd_reboot(ks))
				goto default_handle;
			break;
		case 'q': /* query command */
			gdb_cmd_query(ks);
			break;
		case 'H': /* task related */
			gdb_cmd_task(ks);
			break;
		case 'T': /* Query thread status */
			gdb_cmd_thread(ks);
			break;
		case 'z': /* Break point remove */
		case 'Z': /* Break point set */
			gdb_cmd_break(ks);
			break;
		case '3': /* Escape into back into kdb */
			if (remcom_in_buffer[1] == '\0') {
				gdb_cmd_detachkill(ks);
				return 0;
			}
			else if (remcom_in_buffer[1] == '0' && remcom_in_buffer[2] == '\0') {
				__show_regs(ks->linux_regs);
				printf("\nexit n to exit cmdline, n < 0 pass exception instruction\n");
				printf("bt to show backtrace\n");
				if (cmdloop(ks->linux_regs) < 0) {
					ks->linux_regs->csr_epc +=  4;
				}
				return 0;
			} else if (remcom_in_buffer[1] == '1' && remcom_in_buffer[2] == '\0') {
				gdb_cmd_detachkill(ks);
				return DBG_PASS_EVENT;
			}
		case 'C': /* Exception passing */
			tmp = gdb_cmd_exception_pass(ks);
			if (tmp > 0)
				goto default_handle;
			if (tmp == 0)
				break;
			/* Fall through on tmp < 0 */
		case 'c': /* Continue packet */
		case 's': /* Single step packet */
			dbg_activate_sw_breakpoints();
			/* Fall through to default processing */
		default:
default_handle:
			error = kgdb_arch_handle_exception(ks->ex_vector,
						ks->signo,
						ks->err_code,
						remcom_in_buffer,
						remcom_out_buffer,
						ks->linux_regs);
			/*
			 * Leave cmd processing on error, detach,
			 * kill, continue, or single step.
			 */
			if (error >= 0 || remcom_in_buffer[0] == 'D' ||
			    remcom_in_buffer[0] == 'k') {
				error = 0;
				goto kgdb_exit;
			}

		}

		/* reply to the request */
		put_packet(remcom_out_buffer);
	}

kgdb_exit:
	if (ks->pass_exception)
		error = 1;
	return error;
}

/*
 * Handle the 'c' command
 */
static int kgdb_arch_handle_exception(int vector, int signo, int err_code,
			       char *remcom_in_buffer, char *remcom_out_buffer,
			       struct pt_regs *regs)
{
	char *ptr;
	unsigned long address;

	switch (remcom_in_buffer[0]) {
	case 'c':
		/* handle the optional parameter */
		ptr = &remcom_in_buffer[1];
		if (kgdb_hex2long(&ptr, &address))
			regs->csr_epc = address;

		return 0;
	}

	return -1;
}

void noinline loongarch_kgdb_breakpoint(void)
{
	__asm__ __volatile__(
		".globl breakinst\n\t"
		"nop\n"
		"breakinst:\tbreak 0\n\t"
		"nop\n\t");
}

static void kgdb_disable_hw_debug(struct pt_regs *regs);
static void kgdb_enable_hw_debug(struct pt_regs *regs);
/*
+$30#63 to enter cmdline.
 */
int handle_bp(struct pt_regs *regs)
{
	struct kgdb_state kgdb_var;
	struct kgdb_state *ks = &kgdb_var;
	u64 tlbepc = csr_readq(LOONGARCH_CSR_TLBREPC);
	u64 epc = regs->csr_epc;
	static int warnonce;;
	int ret;

	if (tlbepc & 1) {
		csr_writeq(0xb0, LOONGARCH_CSR_CRMD);   /*reset addr access from DA mode to PG mode*/
		regs->csr_epc = tlbepc & ~3ULL;
	}
	if (!warnonce) {
		printf("\n+$30#63 to enter cmdline\n"
			"+$31#64 to pass exception\n"
			"+$3#33 to exit exception\n"
			"gdb target remote /dev/ttyuSB0 connect this gdbserver\n");
		warnonce = 1;
	}
	memset(ks, 0, sizeof(struct kgdb_state));
	ks->cpu			= 0;
	ks->ex_vector		= 0;
	ks->signo		= SIGTRAP;
	ks->err_code		= 0;

	ks->linux_regs = regs;
	dbg_deactivate_sw_breakpoints();
	kgdb_disable_hw_debug(regs);
	kgdb_connected = 1;
	ret = gdb_serial_stub(ks);
	kgdb_enable_hw_debug(regs);
	kgdb_connected = 0;
	if (ret == DBG_PASS_EVENT)
		return ret;
	if (regs->csr_epc == (unsigned long)&breakinst)
		regs->csr_epc += 4;
	if (tlbepc & 1) {
		csr_writeq(regs->csr_epc, LOONGARCH_CSR_TLBREPC);
		regs->csr_epc = epc;
	}
	return 0;
}

static int swicth_gdb_console(void)
{
	static int (*old_write) (int fd, const void *buf, size_t nchar);
	if (_file[1].fs->write != gdb_write) {
		old_write =_file[1].fs->write;
		_file[1].fs->write = gdb_write;
	} else  {
		_file[1].fs->write = old_write;
	}
}

static int gdb_write(int fd, char *buf, int count)
{
	gdbstub_msg_write(buf, count);
	return count;
}

static int cmd_kgdb(int argc, char **argv)
{
	arch_kgdb_breakpoint();
 return 0;
}

static int cmd_b(int argc, char **argv)
{

	unsigned long addr;
	if (argc > 1) {
		addr = strtoul(argv[1], 0, 0);
		dbg_set_sw_break(addr);
	}
	dbg_activate_sw_breakpoints();
	return 0;
}

static int cmd_unb(int argc, char **argv)
{
	unsigned long addr;
	if (argc > 1) {
		addr = strtoul(argv[1], 0, 0);
		dbg_remove_sw_break(addr);
	} else
		dbg_remove_all_break();
	dbg_activate_sw_breakpoints();
	return 0;
}

static int cmd_disable(int argc, char **argv)
{
	if (argv[0][0] == 'e')
		dbg_activate_sw_breakpoints();
	else
		dbg_deactivate_sw_breakpoints();
	return 0;
}


unsigned long watch_csrrd(unsigned int reg);
void watch_csrwr(unsigned long val, unsigned int reg);
static unsigned int dbcn = -1, ibcn;
int arch_has_hw_breakpoint()
{
	if (dbcn == -1) {
		ibcn = watch_csrrd(LOONGARCH_CSR_FWPC) & 0x3f;
		dbcn = watch_csrrd(LOONGARCH_CSR_MWPC) & 0x3f;
	}
	return ibcn || dbcn;
}

int arch_breakpoint_inst()
{
	return 0x002a0000;
}

int arch_set_hw_breakpoint(unsigned long  address,int length, enum kgdb_bptype type, unsigned long addrmask)
{
	unsigned int i, dbc, total;
	unsigned long taddr, t;

	ibcn = watch_csrrd(LOONGARCH_CSR_FWPC) & 0x3f;
	dbcn = watch_csrrd(LOONGARCH_CSR_MWPC) & 0x3f;

	if (type == BP_HARDWARE_BREAKPOINT) {
		for (i = 0; i < ibcn; i++) {
			taddr = watch_csrrd(LOONGARCH_CSR_IB0ADDR + 8*i);
			t = watch_csrrd(LOONGARCH_CSR_IB0CTL + 8*i) & 0x1f;
			if (taddr != address && t)
				continue;
			watch_csrwr(address, LOONGARCH_CSR_IB0ADDR + 8*i);
			watch_csrwr(addrmask, LOONGARCH_CSR_IB0MASK + 8*i);
			watch_csrwr(0, LOONGARCH_CSR_IB0ASID + 8*i);
			watch_csrwr(0x1e, LOONGARCH_CSR_IB0CTL + 8*i);
			watch_csrwr(0x10000, LOONGARCH_CSR_FWPS);
			break;
		}
	} else  {
		for (i = 0; i < dbcn; i++) {

			taddr = watch_csrrd(LOONGARCH_CSR_DB0ADDR + 8*i);
			t =  watch_csrrd(LOONGARCH_CSR_DB0CTL + 8*i) & 0x1f;

			if (taddr != address && t)
				continue;

			dbc = 0x1e;
			switch (length) {
			case 8:
				break;
			case 4:
				dbc |= (1<<10);
				break;
			case 2:
				dbc |= (2<<10);
				break;
			case 1:
				dbc |= (3<<10);
				break;
			default:
				break;
			}

			if (type == BP_WRITE_WATCHPOINT) {
				dbc |= 1<<9;
			} else if (BP_READ_WATCHPOINT) {
				dbc |= 1<<8;
			} else {
				dbc |= 3<<8;
			}

			watch_csrwr(address, LOONGARCH_CSR_DB0ADDR + 8*i);
			watch_csrwr(addrmask, LOONGARCH_CSR_DB0MASK + 8*i);
			watch_csrwr(0, LOONGARCH_CSR_DB0ASID + 8*i);
			watch_csrwr(dbc, LOONGARCH_CSR_DB0CTL + 8*i);
			break;
		}
	}
	if (!kgdb_connected)
		csr_xchg32(CSR_CRMD_WE, CSR_CRMD_WE, LOONGARCH_CSR_CRMD);
	return 0;
}
int arch_remove_hw_breakpoint(unsigned long  address,int length, enum kgdb_bptype type)
{
	unsigned int i, dbc, total;
	unsigned long taddr;

	ibcn = watch_csrrd(LOONGARCH_CSR_FWPC) & 0x3f;
	dbcn = watch_csrrd(LOONGARCH_CSR_MWPC) & 0x3f;
	if (type == BP_HARDWARE_BREAKPOINT) {
		for (i = 0; i < ibcn; i++) {
			taddr = watch_csrrd(LOONGARCH_CSR_IB0ADDR + 8*i);
			if (taddr != address)
				continue;
			watch_csrwr(0, LOONGARCH_CSR_IB0ADDR + 8 * i);
			watch_csrwr(0, LOONGARCH_CSR_IB0MASK + 8 * i);
			watch_csrwr(0, LOONGARCH_CSR_IB0ASID + 8 * i);
			watch_csrwr(0, LOONGARCH_CSR_IB0CTL + 8 * i);
			watch_csrwr(1 << i, LOONGARCH_CSR_FWPS);
			break;
		}
	} else {
		for (i = 0; i < dbcn; i++) {
			taddr = watch_csrrd(LOONGARCH_CSR_DB0ADDR + 8 * i);
			if (taddr != address)
				continue;
			watch_csrwr(0, LOONGARCH_CSR_DB0ADDR + 8 * i);
			watch_csrwr(0, LOONGARCH_CSR_DB0MASK + 8 * i);
			watch_csrwr(0, LOONGARCH_CSR_DB0ASID + 8 * i);
			watch_csrwr(0, LOONGARCH_CSR_DB0CTL + 8 * i);
			watch_csrwr(1 << i, LOONGARCH_CSR_MWPS);
			break;
		}
	}
	if (!kgdb_connected)
		csr_xchg32(CSR_CRMD_WE, CSR_CRMD_WE, LOONGARCH_CSR_CRMD);
	return 0;
}

static void kgdb_disable_hw_debug(struct pt_regs *regs)
{
	csr_xchg32(0, CSR_CRMD_WE, LOONGARCH_CSR_CRMD);
}

static void kgdb_enable_hw_debug(struct pt_regs *regs)
{
	regs->csr_prmd |= CSR_PRMD_PWE;
}

void  arch_kgdb_breakpoint(void)
{
	loongarch_kgdb_breakpoint();
}

static int cmd_hb(int argc, char **argv)
{
	unsigned long addr;
	unsigned long addrmask = 0;
	unsigned int length = 4;
	enum kgdb_bptype type;

	if (argc > 1)
		addr = strtoul(argv[1], 0, 0);
	if (argc > 2)
		addrmask = strtoul(argv[2], 0, 0);
	if (argc > 3)
		length = strtoul(argv[3], 0, 0);
	switch (argv[0][4]) {
	case 'h':
		type = BP_HARDWARE_BREAKPOINT;
		break;
	case 'w':
		type = BP_WRITE_WATCHPOINT;
		break;
	case 'r':
		type = BP_WRITE_WATCHPOINT;
		BP_ACCESS_WATCHPOINT;
		break;
	case 'a':
		type == BP_POKE_BREAKPOINT;
		break;
	default:
		type = BP_HARDWARE_BREAKPOINT;
		break;
	}

	if (argc > 1)
		arch_set_hw_breakpoint(addr, length, type, addrmask);
	return 0;
}

static int cmd_unhb(int argc, char **argv)
{
	unsigned long addr;
	enum kgdb_bptype type;

	if (argc > 1)
		addr = strtoul(argv[1], 0, 0);
	switch (argv[0][6]) {
	case 'h':
		type = BP_HARDWARE_BREAKPOINT;
		break;
	case 'w':
		type = BP_WRITE_WATCHPOINT;
		break;
	case 'r':
		type = BP_WRITE_WATCHPOINT;
		BP_ACCESS_WATCHPOINT;
		break;
	case 'a':
		type == BP_POKE_BREAKPOINT;
		break;
	default:
		type = BP_HARDWARE_BREAKPOINT;
		break;
	}

	if (argc > 1)
		arch_remove_hw_breakpoint(addr, 4, type);
	return 0;
}

unsigned long watch_csrrd(unsigned int reg)
{
	switch (reg) {
	case LOONGARCH_CSR_IB0ADDR:
		return csr_read64(LOONGARCH_CSR_IB0ADDR);
	case LOONGARCH_CSR_IB1ADDR:
		return csr_read64(LOONGARCH_CSR_IB1ADDR);
	case LOONGARCH_CSR_IB2ADDR:
		return csr_read64(LOONGARCH_CSR_IB2ADDR);
	case LOONGARCH_CSR_IB3ADDR:
		return csr_read64(LOONGARCH_CSR_IB3ADDR);
	case LOONGARCH_CSR_IB4ADDR:
		return csr_read64(LOONGARCH_CSR_IB4ADDR);
	case LOONGARCH_CSR_IB5ADDR:
		return csr_read64(LOONGARCH_CSR_IB5ADDR);
	case LOONGARCH_CSR_IB6ADDR:
		return csr_read64(LOONGARCH_CSR_IB6ADDR);
	case LOONGARCH_CSR_IB7ADDR:
		return csr_read64(LOONGARCH_CSR_IB7ADDR);

	case LOONGARCH_CSR_IB0MASK:
		return csr_read64(LOONGARCH_CSR_IB0MASK);
	case LOONGARCH_CSR_IB1MASK:
		return csr_read64(LOONGARCH_CSR_IB1MASK);
	case LOONGARCH_CSR_IB2MASK:
		return csr_read64(LOONGARCH_CSR_IB2MASK);
	case LOONGARCH_CSR_IB3MASK:
		return csr_read64(LOONGARCH_CSR_IB3MASK);
	case LOONGARCH_CSR_IB4MASK:
		return csr_read64(LOONGARCH_CSR_IB4MASK);
	case LOONGARCH_CSR_IB5MASK:
		return csr_read64(LOONGARCH_CSR_IB5MASK);
	case LOONGARCH_CSR_IB6MASK:
		return csr_read64(LOONGARCH_CSR_IB6MASK);
	case LOONGARCH_CSR_IB7MASK:
		return csr_read64(LOONGARCH_CSR_IB7MASK);

	case LOONGARCH_CSR_IB0ASID:
		return csr_read64(LOONGARCH_CSR_IB0ASID);
	case LOONGARCH_CSR_IB1ASID:
		return csr_read64(LOONGARCH_CSR_IB1ASID);
	case LOONGARCH_CSR_IB2ASID:
		return csr_read64(LOONGARCH_CSR_IB2ASID);
	case LOONGARCH_CSR_IB3ASID:
		return csr_read64(LOONGARCH_CSR_IB3ASID);
	case LOONGARCH_CSR_IB4ASID:
		return csr_read64(LOONGARCH_CSR_IB4ASID);
	case LOONGARCH_CSR_IB5ASID:
		return csr_read64(LOONGARCH_CSR_IB5ASID);
	case LOONGARCH_CSR_IB6ASID:
		return csr_read64(LOONGARCH_CSR_IB6ASID);
	case LOONGARCH_CSR_IB7ASID:
		return csr_read64(LOONGARCH_CSR_IB7ASID);

	case LOONGARCH_CSR_IB0CTL:
		return csr_read64(LOONGARCH_CSR_IB0CTL);
	case LOONGARCH_CSR_IB1CTL:
		return csr_read64(LOONGARCH_CSR_IB1CTL);
	case LOONGARCH_CSR_IB2CTL:
		return csr_read64(LOONGARCH_CSR_IB2CTL);
	case LOONGARCH_CSR_IB3CTL:
		return csr_read64(LOONGARCH_CSR_IB3CTL);
	case LOONGARCH_CSR_IB4CTL:
		return csr_read64(LOONGARCH_CSR_IB4CTL);
	case LOONGARCH_CSR_IB5CTL:
		return csr_read64(LOONGARCH_CSR_IB5CTL);
	case LOONGARCH_CSR_IB6CTL:
		return csr_read64(LOONGARCH_CSR_IB6CTL);
	case LOONGARCH_CSR_IB7CTL:
		return csr_read64(LOONGARCH_CSR_IB7CTL);

	case LOONGARCH_CSR_DB0ADDR:
		return csr_read64(LOONGARCH_CSR_DB0ADDR);
	case LOONGARCH_CSR_DB1ADDR:
		return csr_read64(LOONGARCH_CSR_DB1ADDR);
	case LOONGARCH_CSR_DB2ADDR:
		return csr_read64(LOONGARCH_CSR_DB2ADDR);
	case LOONGARCH_CSR_DB3ADDR:
		return csr_read64(LOONGARCH_CSR_DB3ADDR);
	case LOONGARCH_CSR_DB4ADDR:
		return csr_read64(LOONGARCH_CSR_DB4ADDR);
	case LOONGARCH_CSR_DB5ADDR:
		return csr_read64(LOONGARCH_CSR_DB5ADDR);
	case LOONGARCH_CSR_DB6ADDR:
		return csr_read64(LOONGARCH_CSR_DB6ADDR);
	case LOONGARCH_CSR_DB7ADDR:
		return csr_read64(LOONGARCH_CSR_DB7ADDR);

	case LOONGARCH_CSR_DB0MASK:
		return csr_read64(LOONGARCH_CSR_DB0MASK);
	case LOONGARCH_CSR_DB1MASK:
		return csr_read64(LOONGARCH_CSR_DB1MASK);
	case LOONGARCH_CSR_DB2MASK:
		return csr_read64(LOONGARCH_CSR_DB2MASK);
	case LOONGARCH_CSR_DB3MASK:
		return csr_read64(LOONGARCH_CSR_DB3MASK);
	case LOONGARCH_CSR_DB4MASK:
		return csr_read64(LOONGARCH_CSR_DB4MASK);
	case LOONGARCH_CSR_DB5MASK:
		return csr_read64(LOONGARCH_CSR_DB5MASK);
	case LOONGARCH_CSR_DB6MASK:
		return csr_read64(LOONGARCH_CSR_DB6MASK);
	case LOONGARCH_CSR_DB7MASK:
		return csr_read64(LOONGARCH_CSR_DB7MASK);

	case LOONGARCH_CSR_DB0ASID:
		return csr_read64(LOONGARCH_CSR_DB0ASID);
	case LOONGARCH_CSR_DB1ASID:
		return csr_read64(LOONGARCH_CSR_DB1ASID);
	case LOONGARCH_CSR_DB2ASID:
		return csr_read64(LOONGARCH_CSR_DB2ASID);
	case LOONGARCH_CSR_DB3ASID:
		return csr_read64(LOONGARCH_CSR_DB3ASID);
	case LOONGARCH_CSR_DB4ASID:
		return csr_read64(LOONGARCH_CSR_DB4ASID);
	case LOONGARCH_CSR_DB5ASID:
		return csr_read64(LOONGARCH_CSR_DB5ASID);
	case LOONGARCH_CSR_DB6ASID:
		return csr_read64(LOONGARCH_CSR_DB6ASID);
	case LOONGARCH_CSR_DB7ASID:
		return csr_read64(LOONGARCH_CSR_DB7ASID);

	case LOONGARCH_CSR_DB0CTL:
		return csr_read64(LOONGARCH_CSR_DB0CTL);
	case LOONGARCH_CSR_DB1CTL:
		return csr_read64(LOONGARCH_CSR_DB1CTL);
	case LOONGARCH_CSR_DB2CTL:
		return csr_read64(LOONGARCH_CSR_DB2CTL);
	case LOONGARCH_CSR_DB3CTL:
		return csr_read64(LOONGARCH_CSR_DB3CTL);
	case LOONGARCH_CSR_DB4CTL:
		return csr_read64(LOONGARCH_CSR_DB4CTL);
	case LOONGARCH_CSR_DB5CTL:
		return csr_read64(LOONGARCH_CSR_DB5CTL);
	case LOONGARCH_CSR_DB6CTL:
		return csr_read64(LOONGARCH_CSR_DB6CTL);
	case LOONGARCH_CSR_DB7CTL:
		return csr_read64(LOONGARCH_CSR_DB7CTL);

	case LOONGARCH_CSR_FWPS:
		return csr_read64(LOONGARCH_CSR_FWPS);
	case LOONGARCH_CSR_MWPS:
		return csr_read64(LOONGARCH_CSR_MWPS);
	case LOONGARCH_CSR_FWPC:
		return csr_read64(LOONGARCH_CSR_FWPC);
	case LOONGARCH_CSR_MWPC:
		return csr_read64(LOONGARCH_CSR_MWPC);
	default:
		printf("read watch register number error %d\n", reg);
	}
	return 0;
}

void watch_csrwr(unsigned long val, unsigned int reg)
{
	switch (reg) {
	case LOONGARCH_CSR_IB0ADDR:
		csr_write64(val, LOONGARCH_CSR_IB0ADDR);
		break;
	case LOONGARCH_CSR_IB1ADDR:
		csr_write64(val, LOONGARCH_CSR_IB1ADDR);
		break;
	case LOONGARCH_CSR_IB2ADDR:
		csr_write64(val, LOONGARCH_CSR_IB2ADDR);
		break;
	case LOONGARCH_CSR_IB3ADDR:
		csr_write64(val, LOONGARCH_CSR_IB3ADDR);
		break;
	case LOONGARCH_CSR_IB4ADDR:
		csr_write64(val, LOONGARCH_CSR_IB4ADDR);
		break;
	case LOONGARCH_CSR_IB5ADDR:
		csr_write64(val, LOONGARCH_CSR_IB5ADDR);
		break;
	case LOONGARCH_CSR_IB6ADDR:
		csr_write64(val, LOONGARCH_CSR_IB6ADDR);
		break;
	case LOONGARCH_CSR_IB7ADDR:
		csr_write64(val, LOONGARCH_CSR_IB7ADDR);
		break;

	case LOONGARCH_CSR_IB0MASK:
		csr_write64(val, LOONGARCH_CSR_IB0MASK);
		break;
	case LOONGARCH_CSR_IB1MASK:
		csr_write64(val, LOONGARCH_CSR_IB1MASK);
		break;
	case LOONGARCH_CSR_IB2MASK:
		csr_write64(val, LOONGARCH_CSR_IB2MASK);
		break;
	case LOONGARCH_CSR_IB3MASK:
		csr_write64(val, LOONGARCH_CSR_IB3MASK);
		break;
	case LOONGARCH_CSR_IB4MASK:
		csr_write64(val, LOONGARCH_CSR_IB4MASK);
		break;
	case LOONGARCH_CSR_IB5MASK:
		csr_write64(val, LOONGARCH_CSR_IB5MASK);
		break;
	case LOONGARCH_CSR_IB6MASK:
		csr_write64(val, LOONGARCH_CSR_IB6MASK);
		break;
	case LOONGARCH_CSR_IB7MASK:
		csr_write64(val, LOONGARCH_CSR_IB7MASK);
		break;

	case LOONGARCH_CSR_IB0ASID:
		csr_write64(val, LOONGARCH_CSR_IB0ASID);
		break;
	case LOONGARCH_CSR_IB1ASID:
		csr_write64(val, LOONGARCH_CSR_IB1ASID);
		break;
	case LOONGARCH_CSR_IB2ASID:
		csr_write64(val, LOONGARCH_CSR_IB2ASID);
		break;
	case LOONGARCH_CSR_IB3ASID:
		csr_write64(val, LOONGARCH_CSR_IB3ASID);
		break;
	case LOONGARCH_CSR_IB4ASID:
		csr_write64(val, LOONGARCH_CSR_IB4ASID);
		break;
	case LOONGARCH_CSR_IB5ASID:
		csr_write64(val, LOONGARCH_CSR_IB5ASID);
		break;
	case LOONGARCH_CSR_IB6ASID:
		csr_write64(val, LOONGARCH_CSR_IB6ASID);
		break;
	case LOONGARCH_CSR_IB7ASID:
		csr_write64(val, LOONGARCH_CSR_IB7ASID);
		break;

	case LOONGARCH_CSR_IB0CTL:
		csr_write64(val, LOONGARCH_CSR_IB0CTL);
		break;
	case LOONGARCH_CSR_IB1CTL:
		csr_write64(val, LOONGARCH_CSR_IB1CTL);
		break;
	case LOONGARCH_CSR_IB2CTL:
		csr_write64(val, LOONGARCH_CSR_IB2CTL);
		break;
	case LOONGARCH_CSR_IB3CTL:
		csr_write64(val, LOONGARCH_CSR_IB3CTL);
		break;
	case LOONGARCH_CSR_IB4CTL:
		csr_write64(val, LOONGARCH_CSR_IB4CTL);
		break;
	case LOONGARCH_CSR_IB5CTL:
		csr_write64(val, LOONGARCH_CSR_IB5CTL);
		break;
	case LOONGARCH_CSR_IB6CTL:
		csr_write64(val, LOONGARCH_CSR_IB6CTL);
		break;
	case LOONGARCH_CSR_IB7CTL:
		csr_write64(val, LOONGARCH_CSR_IB7CTL);
		break;

	case LOONGARCH_CSR_DB0ADDR:
		csr_write64(val, LOONGARCH_CSR_DB0ADDR);
		break;
	case LOONGARCH_CSR_DB1ADDR:
		csr_write64(val, LOONGARCH_CSR_DB1ADDR);
		break;
	case LOONGARCH_CSR_DB2ADDR:
		csr_write64(val, LOONGARCH_CSR_DB2ADDR);
		break;
	case LOONGARCH_CSR_DB3ADDR:
		csr_write64(val, LOONGARCH_CSR_DB3ADDR);
		break;
	case LOONGARCH_CSR_DB4ADDR:
		csr_write64(val, LOONGARCH_CSR_DB4ADDR);
		break;
	case LOONGARCH_CSR_DB5ADDR:
		csr_write64(val, LOONGARCH_CSR_DB5ADDR);
		break;
	case LOONGARCH_CSR_DB6ADDR:
		csr_write64(val, LOONGARCH_CSR_DB6ADDR);
		break;
	case LOONGARCH_CSR_DB7ADDR:
		csr_write64(val, LOONGARCH_CSR_DB7ADDR);
		break;

	case LOONGARCH_CSR_DB0MASK:
		csr_write64(val, LOONGARCH_CSR_DB0MASK);
		break;
	case LOONGARCH_CSR_DB1MASK:
		csr_write64(val, LOONGARCH_CSR_DB1MASK);
		break;
	case LOONGARCH_CSR_DB2MASK:
		csr_write64(val, LOONGARCH_CSR_DB2MASK);
		break;
	case LOONGARCH_CSR_DB3MASK:
		csr_write64(val, LOONGARCH_CSR_DB3MASK);
		break;
	case LOONGARCH_CSR_DB4MASK:
		csr_write64(val, LOONGARCH_CSR_DB4MASK);
		break;
	case LOONGARCH_CSR_DB5MASK:
		csr_write64(val, LOONGARCH_CSR_DB5MASK);
		break;
	case LOONGARCH_CSR_DB6MASK:
		csr_write64(val, LOONGARCH_CSR_DB6MASK);
		break;
	case LOONGARCH_CSR_DB7MASK:
		csr_write64(val, LOONGARCH_CSR_DB7MASK);
		break;

	case LOONGARCH_CSR_DB0ASID:
		csr_write64(val, LOONGARCH_CSR_DB0ASID);
		break;
	case LOONGARCH_CSR_DB1ASID:
		csr_write64(val, LOONGARCH_CSR_DB1ASID);
		break;
	case LOONGARCH_CSR_DB2ASID:
		csr_write64(val, LOONGARCH_CSR_DB2ASID);
		break;
	case LOONGARCH_CSR_DB3ASID:
		csr_write64(val, LOONGARCH_CSR_DB3ASID);
		break;
	case LOONGARCH_CSR_DB4ASID:
		csr_write64(val, LOONGARCH_CSR_DB4ASID);
		break;
	case LOONGARCH_CSR_DB5ASID:
		csr_write64(val, LOONGARCH_CSR_DB5ASID);
		break;
	case LOONGARCH_CSR_DB6ASID:
		csr_write64(val, LOONGARCH_CSR_DB6ASID);
		break;
	case LOONGARCH_CSR_DB7ASID:
		csr_write64(val, LOONGARCH_CSR_DB7ASID);
		break;

	case LOONGARCH_CSR_DB0CTL:
		csr_write64(val, LOONGARCH_CSR_DB0CTL);
		break;
	case LOONGARCH_CSR_DB1CTL:
		csr_write64(val, LOONGARCH_CSR_DB1CTL);
		break;
	case LOONGARCH_CSR_DB2CTL:
		csr_write64(val, LOONGARCH_CSR_DB2CTL);
		break;
	case LOONGARCH_CSR_DB3CTL:
		csr_write64(val, LOONGARCH_CSR_DB3CTL);
		break;
	case LOONGARCH_CSR_DB4CTL:
		csr_write64(val, LOONGARCH_CSR_DB4CTL);
		break;
	case LOONGARCH_CSR_DB5CTL:
		csr_write64(val, LOONGARCH_CSR_DB5CTL);
		break;
	case LOONGARCH_CSR_DB6CTL:
		csr_write64(val, LOONGARCH_CSR_DB6CTL);
		break;
	case LOONGARCH_CSR_DB7CTL:
		csr_write64(val, LOONGARCH_CSR_DB7CTL);
		break;

	case LOONGARCH_CSR_FWPS:
		csr_write64(val, LOONGARCH_CSR_FWPS);
		break;
	case LOONGARCH_CSR_MWPS:
		csr_write64(val, LOONGARCH_CSR_MWPS);
		break;
	default:
		printf("write watch register number error %d\n", reg);
	}
}

static int cmdloop(struct pt_regs *regs)
{
	char line[LINESZ + 8];
	while (1) {
		printf("GDB>");
#if NCMD_HIST > 0
		get_cmd(line);
#else
		get_line(line, 0);
#endif
		if (!strcmp("bt", line))
			bt(regs->csr_epc, regs->regs[3], regs->regs[1]);
		else if (!strncmp(line, "exit ", 5)) {
			return strtoul(line + 4, 0, 0);
		} else
			do_cmd(line);
	}
	return 0;
}

static const Cmd Cmds[] =
{
	{"MyCmds"},
        {"gdb.b", "[addr]", 0, "set soft breakpoint", cmd_b, 0, 9, CMD_REPEAT},
	{"gdb.unb", "[addr]", 0,  "del soft breakpoint", cmd_unb, 0, 9, CMD_REPEAT},
	{"gdb.hb", "[addr] [addrmask]", 0,  "add hb breakpoint", cmd_hb, 0, 9, CMD_REPEAT},
	{"gdb.watch", "[addr] [addrmask]", 0,  "add watch breakpoint", cmd_hb, 0, 9, CMD_REPEAT},
	{"gdb.rwatch", "[addr] [addrmask]", 0,  "add read watch breakpoint", cmd_hb, 0, 9, CMD_REPEAT},
	{"gdb.awatch", "[addr] [addrmask]", 0,  "add all watch breakpoint", cmd_hb, 0, 9, CMD_REPEAT},
	{"gdb.unhb", "[addr]", 0,  "del hb breakpoint", cmd_unhb, 0, 9, CMD_REPEAT},
	{"gdb.unwatch", "[addr]", 0,  "del watch breakpoint", cmd_unhb, 0, 9, CMD_REPEAT},
	{"gdb.unrwatch", "[addr]", 0,  "del read watch breakpoint", cmd_unhb, 0, 9, CMD_REPEAT},
	{"gdb.unawatch", "[addr]", 0,  "del all watch breakpoint", cmd_unhb, 0, 9, CMD_REPEAT},
	{"gdb.disable", "", 0, "disable all soft breakpoints",cmd_disable, 0, 9, CMD_REPEAT},
        {"gdb.enable", "", 0, "enable all soft breakpoints",cmd_disable, 0, 9, CMD_REPEAT},
	{"gdb.break", "", 0, "enter kgdb,goback use +$D#44+ or +$3#33",cmd_kgdb, 0, 9, CMD_REPEAT},
	{0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

static void init_cmd()
{
	cmdlist_expand(Cmds, 1);
}
