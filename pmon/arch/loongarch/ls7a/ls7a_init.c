#include "target/ls7a_config.h"
#include "target/ls7a.h"

#include "ls3a7a_setup_ht_link.c"
#include "ls7a.c"
#include "ls3a7a_win.c"

//#define GEN4_SUPPORT

void ls7a_init(void)
{
#if (CHIPSET_MODE != 2)
	int status;
	//Initialize LS7A here
#ifdef DEBUG_ENABLE_LOONGSON
	pr_info("------------------Test Ls7a access-----------------\n");
	status = ls7a_dbg();
	if (status) {
		pr_info("Test Ls7a access fail!\n");
		while(1);
	}
#endif
	loongson_ht_trans_init();
#endif
	ls7a_resource_cfg(&ls7a_cfg_t);

	if (ls7a_version()) {
		/*Debug New 7A Func,Drop Rtc voltage*/
		do {
			readl(LS7A_ACPI_PMCON_RTC_REG) |= (0x3 << 9);
		} while (((readl(LS7A_ACPI_PMCON_RTC_REG) >> 9) & 0x3) != 0x3);
	}
	ls7a_clear_pcie_portirq();
#ifdef LS7A2000
	pr_info("Ls7A2000 Chipset initialize done.\n");
#else
	pr_info("Ls7A1000 Chipset initialize done.\n");
#endif
}
