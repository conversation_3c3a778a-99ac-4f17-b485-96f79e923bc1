#include <cpu.h>
#include <machine/bus.h>
#include "target/ls7a_config.h"
#include "ht.h"
#include "gmemparam.h"

void ls7a2000_hw_init(uint64_t node_id);
void ls7a2000_7a1_hw_init(uint64_t node_id);
extern void prg_init(uint32_t prg_vreg_regulator);
extern void pcie_init(void);
extern void sata_phy_init(void);
extern void usb2_init(void);
extern void usb3_init(void);
extern void rio_init(uint32_t, uint8_t, uint32_t);

extern void mm_feature_init_7a(void);
extern int ddr4_init (uint64_t node_num, ddr_ctrl *mm_ctrl_p);

extern uint64_t gl_node_id;

extern void core_exec();
extern void slave_main_entry(void);

#define PRG_VREG 0x8000

pcie_desc ls7a_pcie_ctrl[] = {
#if (TOT_NODE_NUM > 1) || defined(LOONGSON_3A6000) || defined(LOONGSON_3C6000) || (defined(LOONGSON_3B6000)) || (defined(QR_3A5000_GYKFB))
	{"F0", 0, LS7A_PCIE_F0_DISABLE, 8, LS7A_PCIE_GEN2_MODE, LS7A_PCIE_X1_MODE, LS7A_PCIE_EQ_MODE0},
#else
	{"F0", 0, LS7A_PCIE_F0_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X4_MODE, LS7A_PCIE_EQ_MODE0},
#endif
	{"F1", 0, LS7A_PCIE_F1_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X4_MODE, LS7A_PCIE_EQ_MODE0},
#if (defined(QR_3A5000_GYKFB)) || (defined(QR_3C5000_SINGL))
	{"H ", 0, LS7A_PCIE_H_DISABLE , 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X4_MODE, LS7A_PCIE_EQ_MODE0},
#else
	{"H ", 0, LS7A_PCIE_H_DISABLE , 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X8_MODE, LS7A_PCIE_EQ_MODE0},
#endif
	{"G0", 0, LS7A_PCIE_G0_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X8_MODE, LS7A_PCIE_EQ_MODE0},
#if (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1)
	{"F0", 0, LS7A_ANOTHER_PCIE_F0_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X4_MODE, LS7A_PCIE_EQ_MODE0},
	{"F1", 0, LS7A_ANOTHER_PCIE_F1_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X4_MODE, LS7A_PCIE_EQ_MODE0},
	{"H ", 0, LS7A_ANOTHER_PCIE_H_DISABLE,  8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X8_MODE, LS7A_PCIE_EQ_MODE0},
	{"G0", 0, LS7A_ANOTHER_PCIE_G0_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X8_MODE, LS7A_PCIE_EQ_MODE0},
#endif
};

sata_desc ls7a_sata_ctrl[] = {
	{LS7A_SATA_DISABLE, {{LS7A_SATA_PORT0_DISABLE, LS7A_SATA_PORT1_DISABLE, LS7A_SATA_PORT2_DISABLE, LS7A_SATA_PORT3_DISABLE}}, 0x53, 0x00, 0x72, 0x20, 0xc0, 0x20},
#if (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1)
	{LS7A_SATA1_DISABLE, {{LS7A_SATA_PORT0_DISABLE, LS7A_SATA_PORT1_DISABLE, LS7A_SATA_PORT2_DISABLE, LS7A_SATA_PORT3_DISABLE}}, 0x53, 0x00, 0x72, 0x20, 0xc0, 0x20},
#endif
};

usb_desc ls7a_usb_ctrl[] = { /* Use this configuration when UsbCfg->FixDefaultFlag is false */
#ifdef LOONGSON_3A6000
	{LS7A_USB0_DISABLE, {{0x3f1c0c8000ULL}, {0x301c0c8000ULL}, {0x3f1c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x3f1c0c8000ULL}}},
	{LS7A_USB1_DISABLE, {{0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x3f1c0c8000ULL}, {0x301c0c8000ULL}, {0x3f1c0c8000ULL}}},
#else
	{LS7A_USB0_DISABLE, {{0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}}},
	{LS7A_USB1_DISABLE, {{0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}}},
#endif
	/* XHCI ONLY support first param */
	{LS7A_XHCI_DISABLE, {{0}}},
#if (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1)
	/* another only has XHCI */
	{LS7A_XHCI1_DISABLE, {{0}}},
#endif
};

gmac_desc ls7a_gmac_ctrl[] = {
	{LS7A_GMAC0_DISABLE},
#if (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1)
    {LS7A_GMAC1_DISABLE},
#endif
};

iommu_desc ls7a_iommu_ctrl[] = {
    {LS7A_IOMMU_DISABLE},
#if (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1)
    {LS7A_IOMMU1_DISABLE},
#endif
};

void ls7a_pll_adapt(ls7a_pll_table* pll_ctrl)
{
	/*rapidio, gmac, sata3/usb3*/
	pll_ctrl[LS7A_PLL0].pll_val = LS7A_PLL_VALUE(170, 17, 34, 28); //250 125 151
	pll_ctrl[LS7A_PLL0].div = 4;
	/*gpu, gmem, dc*/
	//pll_ctrl[LS7A_PLL1].pll_val = LS7A_PLL_VALUE(48, 12, 4, 12); //400 2400 400
	pll_ctrl[LS7A_PLL1].pll_val = LS7A_PLL_VALUE(48, 10, 4, 12); //480 2400 400
	//pll_ctrl[LS7A_PLL1].pll_val = LS7A_PLL_VALUE(50, 10, 5, 12); //500 2000 400
	pll_ctrl[LS7A_PLL1].div = (0 << LS7A_GMEM_DIV_RESETn_OFFSET) | (1 << LS7A_GMEM_RESETn_OFFSET) | (LS7A_GMEM_DIV_MODE << LS7A_GMEM_DIV_MODE_OFFSET) | 1;
	/*flex, node, hda bitclk*/
	pll_ctrl[LS7A_PLL2].pll_val = LS7A_PLL_VALUE(30, 30, 4, 125); //node 750
	pll_ctrl[LS7A_PLL2].div = 1;
	/*PIX0, default 38.2MHz for x800x600*/
	pll_ctrl[LS7A_PLL3].pll_val = LS7A_PLL_VALUE(104, 68, 68, 68);
	pll_ctrl[LS7A_PLL3].div = 4;
	/*PIX1, default 38.2MHz for x800x600*/
	pll_ctrl[LS7A_PLL4].pll_val = LS7A_PLL_VALUE(104, 68, 68, 68);
	pll_ctrl[LS7A_PLL4].div = 4;
#if (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1)
	//7a 2w
	/*rapidio, gmac, sata3/usb3*/
	pll_ctrl[LS7A1_PLL0].pll_val = LS7A_PLL_VALUE(170, 17, 34, 28); //250 125 151
	pll_ctrl[LS7A1_PLL0].div = 4;
	/*flex, node, hda bitclk*/
	pll_ctrl[LS7A1_PLL2].pll_val = LS7A_PLL_VALUE(24, 24, 3, 100);
	pll_ctrl[LS7A1_PLL2].div = 1;
#endif
}

void ls7a_feature_init(void)
{
	ls7a_cfg_t.multiuse.hda_i2s_sel = LS7A_HDA_I2S_SEL;

	/* pll */
	ls7a_pll_adapt(ls7a_cfg_t.pll);

	ls7a_cfg_t.pcie.reboot_to_link = REBOOT2LINK;
	/* pcie */
	ls7a_cfg_t.pcie.reset_delay = LS7A_PCIE_RESET_DELAY;
	ls7a_cfg_t.pcie.controller = ls7a_pcie_ctrl;
	/* sata */
	ls7a_cfg_t.sata.controller = ls7a_sata_ctrl;
	/* usb */
	ls7a_cfg_t.usb.controller = ls7a_usb_ctrl;
	/* gmac */
	ls7a_cfg_t.gmac.controller = ls7a_gmac_ctrl;
	/* iommu */
	ls7a_cfg_t.iommu.controller = ls7a_iommu_ctrl;

	/* display */
	ls7a_cfg_t.dc.graphics_disable = LS7A_GRAPHICS_DISABLE;
	ls7a_cfg_t.dc.gmem_disable = LS7A_GMEM_DISABLE;
	ls7a_cfg_t.dc.gpu_disable = LS7A_GPU_DISABLE;
	ls7a_cfg_t.dc.vpu_enable = LS7A_VPU_ENABLE && ls7a_perf_enh();

	/* rio */
	ls7a_cfg_t.rio_enable = LS7A_RIO_ENABLE && ls7a_perf_enh();

	/* misc devices */
	ls7a_cfg_t.misc.lpc_disable = LS7A_LPC_DISABLE;
	ls7a_cfg_t.misc.fan.min_rpm = 5000;
	ls7a_cfg_t.misc.fan.min_rpm = 10000;
}

void config_one_pll(uint64_t conf_base, int pll_num)
{
	uint32_t i, val32;
	uint64_t pll_base = conf_base + CONF_PLL0_OFFSET + (pll_num % 5) * 0x10;
	/*switch to backup clk*/
	readl(pll_base + 0x4) &= ~(0x7 << LS7A_PLL_SEL0_OFFSET);
	/*power down pll*/
	readl(pll_base + 0x4) |= (1 << LS7A_PLL_PD_OFFSET);
	/*disable pll configure*/
	readl(pll_base + 0x4) &= ~(1 << LS7A_PLL_SET_OFFSET);

	/*configure pll parameters*/
	readl(pll_base) = ls7a_cfg_t.pll[pll_num].pll_val;
	val32 = readl(pll_base + 0x4);
	if (pll_num == 1)
		readl(pll_base + 0x4) = (val32 & ~(0x3f << LS7A_PLL_DIV_REFC_OFFSET) & ~(0xf << LS7A_GMEM_DIV_RESETn_OFFSET)) | ls7a_cfg_t.pll[pll_num].div;
	else
		readl(pll_base + 0x4) = (val32 & ~(0x3f << LS7A_PLL_DIV_REFC_OFFSET)) | ls7a_cfg_t.pll[pll_num].div;

	/*enable pll configure*/
	readl(pll_base + 0x4) |= (1 << LS7A_PLL_SET_OFFSET);
	/*not bypass pll*/
	readl(pll_base + 0x4) &= ~(0x1 << LS7A_PLL_BYPASS_OFFSET);
	/*power up pll*/
	readl(pll_base + 0x4) &= ~(0x1 << LS7A_PLL_PD_OFFSET);

	/*poll lock signal*/
	i = 0x1000;
	do {
		val32 = readl(pll_base + 0x4) & (0x1 << LS7A_PLL_LOCK_OFFSET);
		i--;
	} while ((!val32) && i);

	if (i > 0) {
		/* select pll out */
		readl(pll_base + 0x4) |= (0x7 << LS7A_PLL_SEL0_OFFSET);
		return;
	}
	pr_info("!!!LS7A PLL%d soft configure fail.\r\n", pll_num % 5);
	while(1);
}

int device_cfg (void)
{
	uint64_t gmem_size;
	uint64_t i = 0;

	usb2_init();

	if (ls7a_cfg_t.usb.controller[2].disable)
		readl(LS7A_CONFBUS_BASE_ADDR + CONF_SB_OFFSET) &= ~(0x1 << 19);
	else
		usb3_init();

	prg_init(PRG_VREG);
	pcie_init();
	if (ls7a_cfg_t.rio_enable) {
		rio_init(LSRIO_SPEED_5, LSRIO_PORT_F1, 0);
	}
	if (ls7a_cfg_t.sata.controller[0].disable) {
		readl(LS7A_CONFBUS_BASE_ADDR + CONF_SB_OFFSET + 0x4) &= ~(0x1 << 11);
	} else {
		sata_phy_init();
		for(i = 0; i < 4; i++) {
			if (ls7a_cfg_t.sata.controller[0].disable_port & (1 << i)) {
				readl(LS7A_CONFBUS_BASE_ADDR + 0x740) |=  (0x1 << (12 + i));
				readl(LS7A_CONFBUS_BASE_ADDR + 0x740) &= ~(0x1 << (16 + i));
			}
		}
	}

	if (!ls7a_cfg_t.misc.lpc_disable) {
		readl(LS7A_CONFBUS_BASE_ADDR + CONF_SB_OFFSET + 0x4) |= (0x1 << 0);
		pr_info("LPC enabled\r\n");
	}

	/*GNET*/
	if (ls7a_cfg_t.gmac.controller[0].disable) {
		readl(LS7A_CONFBUS_BASE_ADDR + CONF_SB_OFFSET) &= ~(0x3 << 4);
		readl(LS7A_CONFBUS_BASE_ADDR + 0x770) &= ~(1 << 24);
		pr_info("GNET disabled\r\n");
	}

	/*DC*/
	if (ls7a_cfg_t.dc.graphics_disable) {
		unsigned long dc_base_addr;
		dc_base_addr = PHYS_TO_UNCACHED(0xe0060000000);
		readl(PHYS_TO_UNCACHED(0xefe00000000 | 0x10 | (6 << 11) | (1 << 8))) = 0x60000000;
		readl(PHYS_TO_UNCACHED(0xefe00000000 | 0x04 | (6 << 11) | (1 << 8))) |= 0x147;
		//disable dc dma out put
		readl(dc_base_addr + 0x1240 + 0x0) &= ~(1 << 8);
		readl(dc_base_addr + 0x1250 + 0x0) &= ~(1 << 8);
		//disable dc clk
		readl(LS7A_CONFBUS_BASE_ADDR + 0x420) &= ~(1 << 7);
		//disable ClockEn
		readl(dc_base_addr + 0x1240 + 0x180) &= ~(1 << 8);
		readl(dc_base_addr + 0x1250 + 0x180) &= ~(1 << 8);
		//pix0 pd
		readl(LS7A_CONFBUS_BASE_ADDR + 0x4b4) |= (1 << 23);
		//pix1 pd
		readl(LS7A_CONFBUS_BASE_ADDR + 0x4c4) |= (1 << 23);
		//PhyResetn and PhyEn
		readl(dc_base_addr + 0x1240 + 0x5c0) &= ~(0x3 << 1);
		readl(dc_base_addr + 0x1250 + 0x5c0) &= ~(0x3 << 1);
		//Cfg_vdac_pd[4] VGA_off_det_en[1] VGA_on_det_en[0]
		readl(dc_base_addr + 0x1bb0) |= ((0x1 << 4) | (0x3 << 0));
		/* release pcie mem */
		readl(PHYS_TO_UNCACHED(0xefe00000000 | 0x10 | (6 << 11) | (1 << 8))) = 0x0;
	} else {
		readl(LS7A_CONFBUS_BASE_ADDR + 0x420) |= (1 << 7);
		pr_info("Graphics clk enabled\r\n");
	}

	/*GPU*/
	if (ls7a_cfg_t.dc.gpu_disable) {
		readl(LS7A_CONFBUS_BASE_ADDR + 0x420) &= ~(1 << 6);
		pr_info("GPU clk disabled\r\n");
	} else {
		readl(LS7A_CONFBUS_BASE_ADDR + 0x420) |= (1 << 6);
		pr_info("GPU clk enabled\r\n");
	}

	/*put PCIE device detect later, else you need to add more delay*/
	/*delay at least 200ms*/
	mdelay(200);

	if (ls7a_cfg_t.dc.gmem_disable) {
		//disable gmem_lpconf_en[29] gmem_lpmc_en[28]
		readl(LS7A_CONFBUS_BASE_ADDR + 0x424) |= (0x3 << 28);
		//disable gmem clk
		readl(LS7A_CONFBUS_BASE_ADDR + 0x420) &= ~(1 << 5);
		pr_info("Gmem clk disabled\r\n");
	} else {
		readl(LS7A_CONFBUS_BASE_ADDR + 0x420) |= (1 << 5);
		pr_info("Gmem clk enabled\r\n");
#if DEBUG_GMEM
		pr_info("\r\nInitial GMEM?(0xf: skip): ");
		if((inputaddress() & 0xf) <= 1)
#endif
		{
			pr_info("Gmem config begin\r\n");
			/*set gmem bar for init gmem use*/
			readq(GPU_HEADER_ADDR + 0x18) = TEMP_GMEM_ADDR;
			readl(GPU_HEADER_ADDR + 0x1c) = 0x0;
			/*mem space en*/
			readl(GPU_HEADER_ADDR + 0x4) = 0x2;

			//uint32_t s1_gmem;
			//s1_gmem = 0xc3a10404; /*memsize: unit 32MB*/
			//ls7a_gmem_init(s1_gmem);
			/***********
			config gmem window: //TODO
			1. conf_nb[0] = 1, 0xe0040000000 gmem
			2. conf_nb[0] = 0, gpu base and mask
			3. conf_nb[0] = 0, gpu base0/mask0 and base1/mask1
			***********/
			//readl(LS7A_CONFBUS_BASE_ADDR + CONF_NB_OFFSET) |= 0x1;
			readl(LS7A_CONFBUS_BASE_ADDR + CONF_GMEM_BAR_MASK_OFFSET) = 0xfffffff;
			readl(LS7A_CONFBUS_BASE_ADDR + CONF_GMEM_BAR_MASK_OFFSET + 0x4) = 0x0;
			mm_feature_init_7a();
			ddr4_init(0, &mm_ctrl_info);

			/*set gmem space bar mask*/
			gmem_size = mm_ctrl_info.paster.mc0_memsize;	//memsize: unit 1GB
			pr_info("Gmem size 0x%x\r\n", gmem_size);

			//test_gmem();
			/*recover gpu bar*/
			readl(GPU_HEADER_ADDR + 0x4 ) = 0;
			readl(GPU_HEADER_ADDR + 0x18) = 0;
			readl(GPU_HEADER_ADDR + 0x1c) = 0;
		}
	}

#ifdef LS7A_UC_ACC
	//readl(LS7A_CONFBUS_BASE_ADDR + CONF_NB_OFFSET + 4) |= (0x3f << 0);
	readl(LS7A_CONFBUS_BASE_ADDR + CONF_SB_OFFSET + 0) |= (0xef << 24);
	pr_info("LS7A uncache accellerator enabled\r\n");
#endif

	return 0;
}

void ls7a0_resource_cfg(void)
{
	uint64_t i;

	gl_node_id = 0;
	uint64_t base = (gl_node_id << NODE_OFFSET) | LS7A_CONFBUS_BASE_ADDR;

	ls7a_dis_ht_clk(base);

	ls7a_dma_cfg(base);

	/*configure 7A pll*/
	for (i = 0; i < 5; i++) {
		config_one_pll(base, i);
	}
	readl(base + CONF_PLL0_OFFSET + 0x10 + 0x4) |= (0x1 << LS7A_GMEM_DIV_RESETn_OFFSET);
	pr_info("LS7A pll configure done.\r\n");

	/* enable HDA0 */
	readl(base + CONF_SB_OFFSET) |= (1 << 20);
	readl(base + CONF_SB_OFFSET + 0x10) &= ~(3 << 11);
	readl(base + CONF_SB_OFFSET + 0x10) |= (1 << 11);
	/* enable HDA1 */
	readl(base + CONF_SB_OFFSET) |= (1 << 21);
	readl(base + CONF_SB_OFFSET + 4) &= ~(1 << 31);

	ls7a2000_hw_init(0);
	/*configure to obey strict HT order*/
	readl(base + 0x414) |= (0x7ffff << 0);

	/* gnet phy configure */
	readl(base + 0x770) &= ~0xf00;
	readl(base + 0x770) |= 0x4e00;

	/*change INT and HPET fix address*/
	readl(base + 0x460) = (INT_BASE_ADDR | 0x4);
	readl(base + 0x464) = (HPET_BASE_ADDR | 0x4);
	pr_info("LS7A hardware init done.\r\n");
	/*3. device configure*/
	device_cfg();
#ifdef LS7A_CAN
	readl(base + 0x444) = 0xf;	//CAN 模式
#endif
	pr_info("\r\nLS7A init done.\r\n");
}

void ls7a1_resource_cfg(void)
{
	uint64_t base;
	uint64_t i;

	/* now use the ls7a_link_id_buf[1] get another 7A init node ID */
	gl_node_id = ls7a_link_id_buf[1];
	base = (gl_node_id << NODE_OFFSET) | LS7A_CONFBUS_BASE_ADDR;

	/* disable some useless device */
	readl(base + 0x420) &= (~(0x7 << 5));
	readl(base + 0x430) &= (~((1 << 5) | (1<< 10) | (1 << 14) | (1 << 21)));
	readl(base + 0x434) &= ~(1 << 0);

	ls7a_dma_cfg(base);

	/* configure 7A pll */
	config_one_pll(base, LS7A1_PLL0);
	config_one_pll(base, LS7A1_PLL2);
	pr_info("LS7A pll configure done.\r\n");

	/* configure to obey strict HT order */
	readl(base + 0x414) |= (0x7ffff << 0);

	/* PCIE bridge linked by F0 */
	if (ls7a_cfg_t.config.ls7a_con_type == 1)
		readl(LS7A_CONFBUS_BASE_ADDR + 0x638) = 1;

	/* init 7a hardware */
	ls7a2000_7a1_hw_init(gl_node_id);

	/* change INT and HPET fix baseess */
	readl(base + 0x460) = (INT_BASE_ADDR | 0x4);
	readl(base + 0x464) = (HPET_BASE_ADDR | 0x4);
	pr_info("LS7A hardware init done.\r\n");
	if (ls7a_cfg_t.usb.controller[3].disable) {
		readl(base + CONF_SB_OFFSET) &= ~(0x1 << 19);
	} else {
		usb3_init();
	}


	/* another 7A linked by HT */
	if (!ls7a_cfg_t.config.ls7a_con_type)
		prg_init(PRG_VREG);
	pcie_init();
	pr_info("\r\nLS7A pcie init done.\r\n");

	/* put PCIE device detect later, else you need to add more delay */
	/* delay at least 200ms */
	mdelay(200);

	if (ls7a_cfg_t.sata.controller[1].disable) {
		readl(base + CONF_SB_OFFSET + 0x4) &= ~(0x1 << 11);
	} else {
		sata_phy_init();
		for(i = 0; i < 4; i++) {
			if (ls7a_cfg_t.sata.controller[1].disable_port & (1 << i)) {
				readl(base + 0x740) |=  (0x1 << (12 + i));
				readl(base + 0x740) &= ~(0x1 << (16 + i));
			}
		}
	}

	/*GNET*/
	if (ls7a_cfg_t.gmac.controller[1].disable) {
		readl(base + CONF_SB_OFFSET) &= ~(3 << 4);
		readl(base + 0x770) & ~(1 << 24);
		pr_info("GNET disabled\r\n");
	}
}

typedef struct {
  int node;
  int lcl_num;
  int phy_num;
} lcl_param;

void fix_ls3c6000_gpio_to_reset(void)
{
#if defined(LOONGSON_3D6000) && !defined(LOONGSON_3E6000) && (TOT_NODE_NUM == 4)
	ls3c6000_phy_param[2].reset_used_gpio = GPIO3;
#elif (TOT_NODE_NUM == 1)
#ifdef ATKWA_3C7A_BOARD
    ls3c6000_phy_param[0].reset_used_gpio = GPIO2;
    ls3c6000_phy_param[1].reset_used_gpio = GPIO3;
#else
	ls3c6000_phy_param[0].reset_used_gpio = GPIO0;
	ls3c6000_phy_param[1].reset_used_gpio = GPIO1;
#endif
#endif

}

void slave_core_disable_double (uint64_t ap_index, lcl_param *context)
{
	void (*call_back)(void) = slave_main_entry;
	uint64_t node = context->node;
	uint64_t phy_num = context->phy_num;
	uint64_t lcl_num = context->lcl_num;

#if defined(LOONGSON_3D6000) && (TOT_NODE_NUM >= 4)
#if (TOT_NODE_NUM == 8)
	if (lcl_num == 3) { // node 0/4 disable all lcl3
#ifdef LOONGSON_3E6000
		disable_lcl_double(node, lcl_num, lcl_num, 0);
#else
		disable_lcl_double(node, lcl_num, phy_num, 0);
#endif
		disable_lcl_double(node + 1, lcl_num, lcl_num, 0);
		disable_lcl_double(node + 2, lcl_num, lcl_num, 0);
		disable_lcl_double(node + 3, lcl_num, lcl_num, 0);
	} else
#endif
	{
		disable_lcl_double(node, lcl_num, phy_num, 0);
		if (lcl_num == 2) {
			disable_lcl_double(node + 2, lcl_num, lcl_num, 0);
		} else if (lcl_num == 0) {
			disable_lcl_double(node + 1, lcl_num, lcl_num, 0);
		} else if (lcl_num == 1) {
			disable_lcl_double(node + 3, lcl_num, lcl_num, 0);
		}
	}
#else
	disable_lcl_double(node, lcl_num, phy_num, 0);
#endif

	readq(0x800000001fe01020) = 0xaaaa;
	call_back = ((uint64_t)call_back & (~(0xff000000))) | 0x900000001c000000ULL;
	(*call_back)();
}

void lcl_port_set(uint64_t target_speed)
{
	uint32_t                  tgt_spd;

	if (ls7a_cfg_t.config.ls3a_chip_type == LS3E6000_CHIP_TYPE) {
		tgt_spd = 2;
		if (target_speed) {
			tgt_spd = target_speed;
		}
		if (ls7a_cfg_t.config.ls3a_node_num >= 4) {
			/*l_cL0 inside the chip*/
			lcl_port_init(0, 0, 0, tgt_spd);
			lcl_port_init(1, 0, 0, tgt_spd);

			lcl_port_init(2, 0, 0, tgt_spd);
			lcl_port_init(3, 0, 0, tgt_spd);

			/*l_cL1*/
			lcl_port_init(0, 1, 1, tgt_spd);
			lcl_port_init(3, 1, 1, tgt_spd);

			lcl_port_init(1, 1, 1, tgt_spd);
			lcl_port_init(2, 1, 1, tgt_spd);

			/*l_cL2*/
			lcl_port_init(0, 2, 2, tgt_spd);
			lcl_port_init(2, 2, 2, tgt_spd);

			lcl_port_init(1, 2, 2, tgt_spd);
			lcl_port_init(3, 2, 2, tgt_spd);
		}
		if (ls7a_cfg_t.config.ls3a_node_num == 8) {
			/*LCL0 inside the chip*/
			lcl_port_init(4, 0, 0, tgt_spd);
			lcl_port_init(5, 0, 0, tgt_spd);
			lcl_port_init(6, 0, 0, tgt_spd);
			lcl_port_init(7, 0, 0, tgt_spd);
			/*LCL1*/
			lcl_port_init(4, 1, 1, tgt_spd);
			lcl_port_init(7, 1, 1, tgt_spd);
			lcl_port_init(5, 1, 1, tgt_spd);
			lcl_port_init(6, 1, 1, tgt_spd);
			/*LCL2*/
			lcl_port_init(4, 2, 2, tgt_spd);
			lcl_port_init(6, 2, 2, tgt_spd);
			lcl_port_init(5, 2, 2, tgt_spd);
			lcl_port_init(7, 2, 2, tgt_spd);
			/*LCL3*/
			lcl_port_init(0, 3, 3, tgt_spd);
			lcl_port_init(4, 3, 3, tgt_spd);
			lcl_port_init(1, 3, 3, tgt_spd);
			lcl_port_init(5, 3, 3, tgt_spd);
			lcl_port_init(2, 3, 3, tgt_spd);
			lcl_port_init(6, 3, 3, tgt_spd);
			lcl_port_init(3, 3, 3, tgt_spd);
			lcl_port_init(7, 3, 3, tgt_spd);
		}
	} else if (ls7a_cfg_t.config.ls3a_chip_type == LS3D6000_CHIP_TYPE) {
		if (ls7a_cfg_t.config.ls3a_node_num == 2) {
			tgt_spd = 4;
			if (target_speed) {
				tgt_spd = target_speed;
			}
			if (tgt_spd != 0x1) {
				lcl_port_init(0, 0, 0, tgt_spd);
				lcl_port_init(1, 0, 0, tgt_spd);
			}
		} else if (ls7a_cfg_t.config.ls3a_node_num == 4) {
			tgt_spd = 2;
			if (target_speed) {
				tgt_spd = target_speed;
			}
			/*l_cL0 inside the chip*/
			lcl_port_init(0, 0, 0, tgt_spd);
			lcl_port_init(1, 0, 0, tgt_spd);

			lcl_port_init(2, 0, 0, tgt_spd);
			lcl_port_init(3, 0, 0, tgt_spd);

			/*l_cL1*/
			lcl_port_init(0, 1, 1, tgt_spd);
			lcl_port_init(3, 1, 1, tgt_spd);

			lcl_port_init(1, 1, 1, tgt_spd);
			lcl_port_init(2, 1, 1, tgt_spd);

			/*l_cL2*/
			lcl_port_init(0, 2, 3, tgt_spd);
			lcl_port_init(2, 2, 2, tgt_spd);

			lcl_port_init(1, 2, 2, tgt_spd);
			lcl_port_init(3, 2, 2, tgt_spd);
		} else if (ls7a_cfg_t.config.ls3a_node_num == 8) {
			tgt_spd = 2;
			if (target_speed) {
				tgt_spd = target_speed;
			}
			/*l_cL0 inside the chip*/
			lcl_port_init(0, 0, 0, tgt_spd);
			lcl_port_init(1, 0, 0, tgt_spd);

			lcl_port_init(2, 0, 0, tgt_spd);
			lcl_port_init(3, 0, 0, tgt_spd);

			lcl_port_init(4, 0, 0, tgt_spd);
			lcl_port_init(5, 0, 0, tgt_spd);

			lcl_port_init(6, 0, 0, tgt_spd);
			lcl_port_init(7, 0, 0, tgt_spd);

			/*l_cL1*/
			lcl_port_init(0, 1, 1, tgt_spd);
			lcl_port_init(3, 1, 1, tgt_spd);

			lcl_port_init(1, 1, 3, tgt_spd);
			lcl_port_init(2, 1, 1, tgt_spd);

			lcl_port_init(4, 1, 1, tgt_spd);
			lcl_port_init(7, 1, 1, tgt_spd);

			lcl_port_init(5, 1, 1, tgt_spd);
			lcl_port_init(6, 1, 1, tgt_spd);

			/*l_cL2*/
			lcl_port_init(0, 2, 3, tgt_spd);
			lcl_port_init(2, 2, 2, tgt_spd);

			lcl_port_init(1, 2, 2, tgt_spd);
			lcl_port_init(3, 2, 2, tgt_spd);

			lcl_port_init(4, 2, 3, tgt_spd);
			lcl_port_init(6, 2, 2, tgt_spd);

			lcl_port_init(5, 2, 2, tgt_spd);
			lcl_port_init(7, 2, 2, tgt_spd);

			/*l_cL3*/
			lcl_port_init(0, 3, 2, tgt_spd);
			lcl_port_init(4, 3, 2, tgt_spd);

			lcl_port_init(1, 3, 1, tgt_spd);
			lcl_port_init(5, 3, 3, tgt_spd);

			lcl_port_init(2, 3, 3, tgt_spd);
			lcl_port_init(6, 3, 3, tgt_spd);

			lcl_port_init(3, 3, 3, tgt_spd);
			lcl_port_init(7, 3, 3, tgt_spd);
		}
	} else {
		if (ls7a_cfg_t.config.ls3a_node_num == 4) {
			tgt_spd = 4;
			if (target_speed) {
				tgt_spd = target_speed;
			}
			if (tgt_spd != 0x1) {
				lcl_port_init(0, 2, 2, tgt_spd);
				lcl_port_init(2, 2, 3, tgt_spd);
			}
		}
	}
}

void lcl_pre_init()
{
	lcl_param                 context;

#if defined(LOONGSON_3D6000)
#if (TOT_NODE_NUM == 2)
	disable_lcl_double(0, 0, 0, 1);
	context.node = 1;
	context.lcl_num = 0;
	context.phy_num = 0;
	core_exec (32, (uint64_t)&context, slave_core_disable_double);
#elif (TOT_NODE_NUM >= 4)
	disable_lcl_double_start();
	context.node = 0;
	context.lcl_num = 2;
#ifdef LOONGSON_3E6000
	context.phy_num = 2;
#else
	context.phy_num = 3;
#endif
	core_exec (96, (uint64_t)&context, slave_core_disable_double);
	context.node = 0;
	context.lcl_num = 0;
	context.phy_num = 0;
	core_exec (64, (uint64_t)&context, slave_core_disable_double);
	context.node = 0;
	context.lcl_num = 1;
	context.phy_num = 1;
	core_exec (32, (uint64_t)&context, slave_core_disable_double);
#if (TOT_NODE_NUM == 8)
	context.node = 4;
	context.lcl_num = 2;
#ifdef LOONGSON_3E6000
	context.phy_num = 2;
#else
	context.phy_num = 3;
#endif
	core_exec (224, (uint64_t)&context, slave_core_disable_double);
	context.node = 4;
	context.lcl_num = 0;
	context.phy_num = 0;
	core_exec (192, (uint64_t)&context, slave_core_disable_double);
	context.node = 4;
	context.lcl_num = 1;
	context.phy_num = 1;
	core_exec (160, (uint64_t)&context, slave_core_disable_double);
	context.node = 4;
	context.lcl_num = 3;
#ifdef LOONGSON_3E6000
	context.phy_num = 3;
#else
	context.phy_num = 2;
#endif
	core_exec (128, (uint64_t)&context, slave_core_disable_double);
#ifdef LOONGSON_3E6000
	disable_lcl_double(0, 3, 3, 0);
	disable_lcl_double(1, 3, 3, 0);
#else
	disable_lcl_double(0, 3, 2, 0);
	disable_lcl_double(1, 3, 1, 0);
#endif
	disable_lcl_double(2, 3, 3, 0);
	disable_lcl_double(3, 3, 3, 0);
#endif
#endif
#else /* 3D6000 */
	disable_lcl_double(0, 2, 2, 1);
	context.node = 2;
	context.lcl_num = 2;
	context.phy_num = 3;
	core_exec (64, (uint64_t)&context, slave_core_disable_double);
#endif
}
