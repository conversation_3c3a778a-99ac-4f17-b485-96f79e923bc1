#include <sys/types.h>

uint64_t cpu_getcount()
{
    uint64_t count;
    __asm__ __volatile__(          \
       "csrrd %0, 0x201\n\t"     \
       "srli.d  %0, %0, 1\n\t"     \
       : "=r" (count) 
	   : "0" (count));
    return count;
}

#ifdef CORE_FREQ
#define CLKPERUSEC_BATUM  (CORE_FREQ >> 1)
#else
#define CLKPERUSEC_BATUM  500
#endif

void delay(int microseconds)
{
    uint64_t clk;
	uint64_t total, start;

    if(microseconds) {
        start = cpu_getcount();
	    total = microseconds * CLKPERUSEC_BATUM;
	    while (total > (cpu_getcount() - start));
    }
}

void mdelay(int millisecond)
{
    int count; 
    volatile int i;
    count = millisecond;
    for(i=0; i<count; i++)
        delay(1000);
}

void sdelay(int second)
{
    int count; 
    volatile int i;
    count = second;
    for(i=0; i<count; i++)
        mdelay(1000);
}

