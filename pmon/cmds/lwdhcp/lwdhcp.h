#ifndef			__LWDHCP__
#define			__LWDHCP__

/*
#include <sys/param.h>
#include <sys/file.h>
#include <sys/syslog.h>
#include <sys/endian.h>

#ifdef		KERNEL
#undef		KERNEL
#include <sys/socket.h>
#else
#include <sys/socket.h>
#endif
*/

#include <types.h>
#include <stdio.h>

#define		LWDHCP_DEBUG	1

#ifdef		LWDHCP_DEBUG
	#define		DbgPrint(str, args...)		printf(str, ##args)
	#define		PERROR(str)					perror(str)
#else
	#define		DbgPrint(str, args...)
	#define		PERROR(str)
#endif


#define		LWDHCP_MESSAGE		1

#ifdef		LWDHCP_MESSAGE	
	#define		Message(str, args...)		printf(str, ##args)
#else
	#define		Message(str, args...)
#endif


struct client_config_t
{
#define		INTERFACE_MAXLEN		20
	char		interface[INTERFACE_MAXLEN];
	char		arp[16];							//store the MAC	address

	u_int32_t	addr;
	int			ifindex;
};

extern struct client_config_t 	client_config;

#endif		//	__LWDHCP__
