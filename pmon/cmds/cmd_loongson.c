#include <pmon.h>
#include <stdio.h>

/*
 * Loongson gpio test
 */
int ls_gpio_test(int argc,char **argv)
{
    int gpio, value, type;

    if(argc<4)
        goto err;
    
    gpio = strtoul(argv[2],0,0);
    
    if(!strcmp(argv[1],"cpu")) {
        type = 1;
    } else if (!strcmp(argv[1],"byte")) {
        type = 0;
    } else {
        printf("Type:[byte | cpu]\n");
        goto err;
    }
    
    if(!strcmp(argv[3],"out")) {
        if(argc < 5)
            goto err;
        value=strtoul(argv[4],0,0);
        printf("Gpio%d output value: %d\n", gpio, value);
        if(type) {
            cpu_gpio_out(gpio,value);
        } else {
            byte_gpio_out(gpio,value);
        }
        printf("Setting ok\n");
    } else if(!strcmp(argv[3],"in")) {
        printf("gpio%d input value: ", gpio);
        if(type) {
            value = cpu_gpio_in(gpio);
        } else {
            value = byte_gpio_in(gpio);
        }
        printf("%d\n", value);
    } else{
        printf("Mode:[in | out]\n");
        goto err;
    }
    return 0;
err:
    printf("Cmd format: lsgpio type gpio mode [value]\n");
    printf("Example byte gpio3:\n");
    printf("\tlsgpio byte 3 in\n");
    printf("\tlsgpio byte 3 out 1\n");
    return -1;
}

static const Cmd Cmds[] =
{
    {"loongson"},
    {"lsgpio", "type:[byte | cpu] mode:[in | out]", NULL, "loongson gpio test", ls_gpio_test, 1, 6, 0},
    {0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

void init_cmd()
{
    cmdlist_expand(Cmds, 1);
}
