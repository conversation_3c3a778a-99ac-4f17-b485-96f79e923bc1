/* $Id: ns16550.c,v 1.1.1.1 2006/09/14 01:59:08 root Exp $ */

/*
 * Copyright (c) 2000-2002 Opsycon AB  (www.opsycon.se)
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by
 *	Opsycon Open System Consulting AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */
#include <termio.h>
#include <machine/pio.h>
#include <machine/bus.h>
#include <pmon.h>
#include <pmon/dev/ns16550.h>

extern int getbaudval __P((int));

#if NMOD_USB_KBD != 0
extern unsigned char usb_kbd_code;
#endif
static int
nsinit (volatile ns16550dev *dp)
{
#ifdef USE_NS16550_FIFO
	unsigned int x;
	static int nsfifo;

	/* force access to id reg */
	outb(&dp->cfcr,0);
	outb(&dp->iir, 0);
	if ((inb(&dp->iir) & 0x38) != 0) {
		return 1;
	}

	/* in case it really is a 16550, enable the fifos */
	outb(&dp->fifo, FIFO_ENABLE);
	for(x = 0; x < 1000000; x++);
	outb(&dp->fifo, FIFO_ENABLE | FIFO_RCV_RST | FIFO_XMT_RST | FIFO_TRIGGER_1);
	for(x = 0; x < 1000000; x++);

	if ((inb(&dp->iir) & IIR_FIFO_MASK) == IIR_FIFO_MASK)
		nsfifo = 1;
	else {
		dp->fifo = 0;
	}
#endif /* NS16650_FIFO */
    /* Clear rx fifo, in order to fix occasional stuck pmon startups */
	outb(&dp->iir,2);
	return 0;
}

volatile ns16550dev *dp_cmd;
static int
nsprogram (volatile ns16550dev *dp, unsigned long freq, int baudrate)
{
static int rates[] = {
	50, 75, 110, 134, 150, 200, 300, 600, 1200, 1800, 2400,
	4800, 9600, 19200, 38400, 57600, 115200, 0 };

	unsigned short brtc;
	unsigned short brtc_x;
	int timeout, *p;

	dp_cmd = dp;
	/* wait for Tx fifo to completely drain */
	timeout = 1000000;
	while (!(inb(&dp->lsr) & LSR_TSRE)) {
		if (--timeout == 0)
			break;
	}

	baudrate = getbaudval (baudrate);
	for (p = rates; *p; p++)
		if (*p == baudrate)
			break;
	if (*p == 0)
		return 1;
#ifndef	USE_SM502_UART0
	brtc = (freq*256) / (16*(baudrate));
	brtc_x = brtc%256;
	brtc = brtc/256;
#else
	brtc = (96000000*256)/(16*(19450));
	brtc_x = brtc%256;
	brtc = brtc/256;
#endif
	outb(&dp->cfcr, CFCR_DLAB);
	outb(&dp->data, brtc & 0xff);
	outb(&dp->ier, brtc >> 8);
#ifdef LOONGSON_2K1500 ||LOONGSON_2K2000
	outb(&dp->iir, brtc_x & 0xff);
#endif
	outb(&dp->cfcr, CFCR_8BITS);
	outb(&dp->mcr, MCR_IENABLE/* | MCR_DTR | MCR_RTS*/);
	outb(&dp->ier, 0);
	return 0;
}

int
ns16550 (int op, struct DevEntry *dev, unsigned long param, int data)
{
	volatile ns16550dev *dp;
	unsigned char code;
	dp = (ns16550dev *) dev->sio;
	if(dp==-1)return (op == OP_RXRDY)?0:1;

	switch (op) {
		case OP_INIT:
			return nsinit (dp);

		case OP_XBAUD:
		case OP_BAUD:
			return nsprogram (dp, dev->freq, data);

		case OP_TXRDY:
		#ifdef NOMSG_ON_SERIAL
		return 1;
		#endif
			return (inb(&dp->lsr) & LSR_TXRDY);

		case OP_TX:
		#ifdef NOMSG_ON_SERIAL
		return 0;
		#endif
			outb(&dp->data, data);
			break;

		case OP_RXRDY:
		#ifdef NOMSG_ON_SERIAL
		return 0;
		#endif
#if NMOD_USB_KBD != 0
			if(usb_kbd_available && usb_kbd_code){
#if NMOD_VGACON >0
				return (inb(&dp->lsr) & LSR_RXRDY);
			}
#else
				return 1;
			}
#endif
			else{
				return (inb(&dp->lsr) & LSR_RXRDY);
			}
#endif

			return (inb(&dp->lsr) & LSR_RXRDY);

		case OP_RX:
#if NMOD_USB_KBD != 0
			if(usb_kbd_available && usb_kbd_code){
#if NMOD_VGACON >0
				return inb(&dp->data) & 0xff;
#else
				code = usb_kbd_code;
				usb_kbd_code = 0;
				return code;
#endif
			}else{
				return inb(&dp->data) & 0xff;
			}
#else
			return inb(&dp->data) & 0xff;
#endif

		case OP_RXSTOP:
			if (data)
				outb(&dp->mcr, inb(&dp->mcr) & ~MCR_RTS);
			else
				outb(&dp->mcr, inb(&dp->mcr) | MCR_RTS);
			break;
	}
	return 0;
}

#define nr_strtol strtoul
#define nr_strtoll strtoull

int set_baud(int ac, char **av)
{
	unsigned long freq;
	int baudrate;
	unsigned short brtc;
	unsigned short brtc_x,brtc_l,brtc_h;
	int timeout, *p;
	if(ac <3)
	{
		printf("set_baud freq baud [reg]\n");
		return -1;
	}
	if(ac == 4)
		dp_cmd = (volatile ns16550dev *) PHYS_TO_UNCACHED(nr_strtol(av[3], 0, 0));

	freq = nr_strtol(av[1], 0, 0);
	baudrate = nr_strtol(av[2], 0, 0);
/* wait for Tx fifo to completely drain */


	printf("freq %d\n",freq);
	printf("baudrate %d\n",baudrate);
	printf("dp_reg 0x%lx\n",dp_cmd);

	timeout = 1000000;
	while (!(inb(&dp_cmd->lsr) & LSR_TSRE)) {
		if (--timeout == 0)
			break;
	}


	outb(&dp_cmd->cfcr, CFCR_DLAB);
	brtc_l = inb(&dp_cmd->data);
	brtc_h = inb(&dp_cmd->ier);
	brtc_x = inb(&dp_cmd->iir);
	outb(&dp_cmd->cfcr, CFCR_8BITS);

	printf("old cfg\n");
	printf("brtc_l 0x%x brtc_h 0x%x brtc_x 0x%x\n",brtc_l,brtc_h,brtc_x);
	brtc = (freq*256) / (16*(baudrate));
	brtc_x = brtc%256;
	brtc = brtc/256;

	brtc_l = brtc &0xff;
	brtc_h = (brtc>>8) &0xff;
	printf("while cfg\n");
	printf("brtc_l 0x%x brtc_h 0x%x brtc_x 0x%x\n",brtc_l,brtc_h,brtc_x);
	outb(&dp_cmd->cfcr, CFCR_DLAB);
	outb(&dp_cmd->data, brtc & 0xff);
	outb(&dp_cmd->ier, brtc >> 8);
	outb(&dp_cmd->iir, brtc_x & 0xff);
	outb(&dp_cmd->cfcr, CFCR_8BITS);
	outb(&dp_cmd->mcr, MCR_IENABLE/* | MCR_DTR | MCR_RTS*/);
	outb(&dp_cmd->ier, 0);

	outb(&dp_cmd->cfcr, CFCR_DLAB);
	brtc_l = inb(&dp_cmd->data);
	brtc_h = inb(&dp_cmd->ier);
	brtc_x = inb(&dp_cmd->iir);
	outb(&dp_cmd->cfcr, CFCR_8BITS);

	printf("now cfg\n");
	printf("brtc_l 0x%x brtc_h 0x%x brtc_x 0x%x\n",brtc_l,brtc_h,brtc_x);

	return 0;



}

int add_baud(int ac, char **av)
{
	unsigned short freq,baud;
	unsigned short brtc_x,brtc_l,brtc_h;
	unsigned short add_brtc_x,add_brtc_l,add_brtc_h;
	int timeout, *p;
	if(ac <2)
	{
	  add_brtc_l = 1;
	}else{
	  add_brtc_l = nr_strtol(av[1], 0, 0);
	}

	if(ac < 3)
	{
	  add_brtc_h = 1;
	}else{
	  add_brtc_h = nr_strtol(av[2], 0, 0);
	}

	if(ac < 4)
	{
	  add_brtc_x = 1;
	}else{
	  add_brtc_x = nr_strtol(av[3], 0, 0);
	}

	timeout = 1000000;
	while (!(inb(&dp_cmd->lsr) & LSR_TSRE)) {
		if (--timeout == 0)
			break;
	}


	outb(&dp_cmd->cfcr, CFCR_DLAB);
	brtc_l = inb(&dp_cmd->data);
	brtc_h = inb(&dp_cmd->ier);
	brtc_x = inb(&dp_cmd->iir);
	outb(&dp_cmd->cfcr, CFCR_8BITS);
	outb(&dp_cmd->mcr, MCR_IENABLE/* | MCR_DTR | MCR_RTS*/);
	outb(&dp_cmd->ier, 0);

	printf("old cfg\n");
	printf("brtc_l 0x%x brtc_h 0x%x brtc_x 0x%x\n",brtc_l,brtc_h,brtc_x);

	brtc_l += add_brtc_l;
	brtc_h += add_brtc_h;
	brtc_x += add_brtc_x;
	printf("while cfg\n");
	printf("brtc_l 0x%x brtc_h 0x%x brtc_x 0x%x\n\n",brtc_l,brtc_h,brtc_x);
	outb(&dp_cmd->cfcr, CFCR_DLAB);
	outb(&dp_cmd->data, brtc_l & 0xff);
	outb(&dp_cmd->ier, brtc_h & 0xff);
	outb(&dp_cmd->iir, brtc_x & 0xff);
	outb(&dp_cmd->cfcr, CFCR_8BITS);
	outb(&dp_cmd->mcr, MCR_IENABLE/* | MCR_DTR | MCR_RTS*/);
	outb(&dp_cmd->ier, 0);

	outb(&dp_cmd->cfcr, CFCR_DLAB);
	brtc_l = inb(&dp_cmd->data);
	brtc_h = inb(&dp_cmd->ier);
	brtc_x = inb(&dp_cmd->iir);
	outb(&dp_cmd->cfcr, CFCR_8BITS);
	outb(&dp_cmd->mcr, MCR_IENABLE/* | MCR_DTR | MCR_RTS*/);
	outb(&dp_cmd->ier, 0);

	printf("now cfg\n");
	printf("brtc_l 0x%x brtc_h 0x%x brtc_x 0x%x\n",brtc_l,brtc_h,brtc_x);

	return 0;




}
static const Cmd Cmds[] =
{
	{"MyCmds"},

	{"set_baud", "set baud", 0, "set_baud", set_baud, 0, 99, CMD_REPEAT},
	{"add_baud", "set baud", 0, "set_baud", add_baud, 0, 99, CMD_REPEAT},
	{0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

static void init_cmd()
{
	cmdlist_expand(Cmds, 1);
}
