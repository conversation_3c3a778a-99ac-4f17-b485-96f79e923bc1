#
# LS2K2000
#
machine		ls2k	loongarch	# CPU Architecture, Platform

include "conf/LOONGSON_PUBILC"

## 2K2000 support options ##
option		SMBIOS_SUPPORT
#option		ACPI_SUPPORT
#select		acpi_support
option		LS_STR

option		CORE_FREQ=1400
option		DDR_FREQ=800
option		RESERVED_COREMASK=0xfffc
option      ARCH=la364
option      TUNE=la364
option		TOT_NODE_NUM=1
option		CORES_PER_NODE=2
option		MC_PER_NODE=1
option		TOT_7A_NUM=1
option		LOONGSON_2K2000
option		ls2k2000
option		LOONGSON_LS2K
option		LS2K2000_GMEM_SIZE=256
option		PCIE_CONF_BASE="0xfe00000000"

#
# Platform options
#
option		LS3_HT			        # Enable the IO cache coherent of HT
option		SOFT_CLKSEL
option		HT0_FREQ=1600
option		DDR_FREQ_2SLOT=600   # Frequency of dual slot memory with one mc
option		NODE_OFFSET=44
option		BOOTCORE_ID=0

option		HIGH_MEM_WIN_BASE_ADDR=0x80000000
option		SHUTDOWN_MASK=0x0000
option		BONITO_100M
#option		BONITO_25M
option		LS7A			#for 7A
option		LS7A2000		#for 7A2000
option		CHIPSET_MODE=0

select		ls_rtc
#option		LS_RIO		#RapidIo support
option		LS2K_GPIO_BEEP
option		BEEP_GPIO=43

#option     QUICK_START

select		cmd_dtb
option		DTB				# Only 2K use DTB

#
# VGA option
#
#option		USE_BMC
option		VGA_NO_ROM
option		VGA_BASE=0x1e000000
option		VRAM_SIZE=128
#option		DEBUG_EMU_VGA
#option		X800x600
option		LS_FB_XSIZE=1024
option		LS_FB_YSIZE=768
option		CONFIG_VIDEO_16BPP

#
# GPU driver selection. Selects for video
# Disable all options below to disable gpu driver 
# Enable all options below to enble gpu driver 
#
select		mod_x86emu_int10
select		mod_framebuffer
select		mod_vesa
select		mod_vgacon
#option		VESAFB

select		lt9211
option		drv_lt9211
option		LT9211C_MODE_SEL=TTL_IN_LVDS_OUT
option		LT9211C_LVDS_1024x768
#option		LT9211C_LVDS_DUAL_PORT

option		EXTERNAL_RTC
option		EEPROM_10BIT_ADDR

#
#  Now the Machine specification
#
mainbus0	at root
localbus0	at mainbus0
loopdev0	at mainbus0
#fd0		at mainbus0
pcibr0		at mainbus0
pcibr1		at mainbus0
pci*		at pcibr?
ppb*		at pci? dev ? function ?	# PCI-PCI bridges
pci*		at ppb? bus ?

ahci*		at pci? dev ? function ?
ahci_sd*	at ahci?
ahci_cdrom*	at ahci?

#
# USB
#
#uhci*		at pci? dev ? function ?
ohci*		at pci? dev ? function ?	# OHCI
usb*		at ohci?
pcixhci*    at pci? dev ? function ?
xhci*       at pcixhci?
usb*        at xhci?
#usbnet*    at xhci?
select      mod_usb_xhci
#select		otg-device

#
# Pseudo devices
#
pseudo-device	loop	1	# network loopback

#
# Networking Devices
#
#gt0		at localbus? base 4
#gt1		at localbus? base 5
#gt2		at localbus? base 6
# fxp normally only used for debugging (enable/disable both)
#fxp0		at pci? dev ? function ?	# Intel 82559 Device
#inphy*		at mii? phy ?			    # Intel 82555 PHYs
#brgphy*	at mii? phy ?		        # Broadcom PHYs
#rtl*		at pci? dev ? function ?
#rtk*		at pci? dev ? function ?
igb*		at pci? dev ? function ?	# Intel 82576
select		igb1
bnx*		at pci? dev ? function ?	# BCM5709S
#rte*		at pci? dev ? function ?
#option		RTL8111
pcisyn0		at pci? dev ? function ?
pcisyn1		at pci? dev ? function ?
#pcisyn2		at pci? dev ? function ?
select		gmac
#select		e100
option		USE_GMAC_NUM=2
option		GMAC_USE_FLASH_MAC


#
# IDE controllers
#
option		IDE_DMA
pciide*		at pci ? dev ? function ? flags 0x0000

#
# IDE hard drives
#
wd*		    at pciide? channel ? drive ? flags 0x0000
#wd0		at pciide? channel ? drive ? flags 0x0000
#cmdide*	at pci ? dev ? function ? flags 0x0000
#wd*		at cmdide? channel ? drive ? flags 0x0000

pcinvme*	at pci? dev ? function ?
nvme*		at pcinvme?

#
# LSI MegaRAID SAS RAID controllers
#
mfi*		at pci?
scsibus*	at mfi?
sd*		    at scsibus? target ? lun ?
# SCSI		RAID disk drive support
select		scsi_sd
select		raw_ether
# SCSI support
#siop*		at pci? dev ? function ?	# Symbios/NCR 53c...
#scsibus*	at siop?
#sd*		at scsibus? target ? lun ?
#cd*		at scsibus? target ? lun ?
#option		CONFIG_LSI_9260			    # for LSI_9260-8i(2108) RAID card support

#option MCI_DEBUG
select      mci
option      SDIO0_EN=1
emmc0 at localbus0 flags 0
option      SDIO1_EN=1
tfcard0 at localbus0 flags 0
option      SDIO_PAD_LEVEL=3

ide_cd*		at pciide? channel ? drive ? flags 0x0001

option		USE_ENVMAC
#option		FLOATINGPT
option		WDC_NORESET

#option		CONFIG_DDR_32BIT
#option		MC0_MEMSIZE=1024
#option		DDR_RESET_REVERT
#option		AUTO_MEM_CONFIG
#option     USE_ACPI_AREA_REG

##use for 8G memory
option		MC0_MEMSIZE=8192
option		MC0_ROW_NUM=1
option		MC0_DLL_CK=0x64

#select		nand
#select		m25p80
option		LS2K2000_B2K_OPTION
option		USE_GPIO4_FUNCTION
option		USE_DVO_FUNCTION
option		USE_I2S_FUNCTION
option		USE_I2C0_FUNCTION
option		USE_I2C1_FUNCTION
option		USE_I2C2_FUNCTION
option		USE_I2C3_FUNCTION

option		USE_UART0_FUNCTION
option		USE_UART3_FUNCTION
option		USE_UART4_FUNCTION
option		USE_UART5_FUNCTION

option		USE_PWM1_FUNCTION
option		USE_PWM2_FUNCTION
option		USE_PWM3_FUNCTION
option		USE_PWM4_FUNCTION
option		USE_PWM5_FUNCTION

option		USE_SDIO_FUNCTION
