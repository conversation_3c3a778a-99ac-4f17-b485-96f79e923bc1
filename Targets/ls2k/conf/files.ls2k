# $Id: files.ls7a
#
# ls7a Target specific files
#

file    Targets/ls2k/loongson/cpulib.S
file    Targets/ls2k/loongson/serial.S
file	Targets/ls2k/loongson/tgt_machdep.c
file	Targets/ls2k/loongson/ls2k1500_init.c	ls2k1500
file	Targets/ls2k/loongson/ls2k2000_init.c	ls2k2000
file	Targets/ls2k/loongson/resume.c
file	Targets/ls2k/loongson/tgt_bootparam.c

file	Targets/ls2k/pci/pci_machdep.c
file	Targets/ls2k/pci/ls2k_pci.c
file	Targets/ls2k/pci_dev/ls2k_irq.c

define	localbus { [base = -1 ] }
device	localbus
attach	localbus at mainbus
file	Targets/ls2k/dev/localbus.c		localbus
file	Targets/ls2k/dev/fl_env.c
file	Targets/ls2k/dev/i2c-ls.c
file    Targets/ls2k/dev/gpio_gmac.c		ls2k1500
file	Targets/ls2k/dev/hda_verb.c			ls2k2000
file	Targets/ls2k/dev/dvfs.c
#file	Targets/ls2k/dev/rio.c
file   	Targets/ls2k/dev/gpio.c

file    Targets/ls2k/dev/embed_dev/can_test.c		ls2k1500
file    Targets/ls2k/dev/embed_dev/eeprom.c
file    Targets/ls2k/dev/i2c-gpio.c
file    Targets/ls2k/dev/embed_dev/rtc.c		
file    Targets/ls2k/dev/embed_dev/spi_ad7705.c		ls2k1500
file   	Targets/ls2k/dev/embed_dev/pwm.c		
#dc
file	Targets/ls2k/dev/dc.c				ls2k2000
file	Targets/ls2k/dev/ls2k_spi_pci_w.c
file	Targets/ls2k/dev/load_dtb.c		cmd_dtb
file	Targets/ls2k/dev/signal_test.c
file    Targets/ls2k/dev/gmac_test.c

#dp
file    Targets/ls2k/dev/embed_dev/lt8718/lt8718.c        ls2k2000

file    Targets/ls2k/dev/embed_dev/lt9211c/lt9211_drv.c           lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/OcmI2cMaster.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/OcmDelay.c         lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvCsc.c        lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvLvdsTx.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvMipiRpt.c    lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvTtlTx.c      lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvDcsCmd.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvMipiTx.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvLvdsRx.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvMipiRx.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvSystem.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvMipiLs.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/DrvTtlRx.c      lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModTtlRx.c      lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModLvdsRx.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModSystem.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModMipiTx.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModLvdsTx.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModMipiRpt.c    lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModPattern.c    lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModTtlTx.c      lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModMipiRx.c     lt9211
file    Targets/ls2k/dev/embed_dev/lt9211c/ModMipiLs.c     lt9211

#TCM2 kx
#file    sys/dev/tcm/tcm2_tis_spi.c                         cmd_tcm2
#file    sys/dev/tcm/tcm2_device.c                          cmd_tcm2
#file    sys/dev/tcm/tcm2_sm3.c                             cmd_tcm2
#file    sys/dev/tcm/tcm2_mailbox.c                         cmd_tcm2
#file    sys/dev/tcm/tcm2_command.c                         cmd_tcm2
#file    sys/dev/tcm/tcm2_key.c                             cmd_tcm2
#file    sys/dev/tcm/tcm2_log.c                             cmd_tcm2
#file    sys/dev/tcm/tcm2_sm4.c                             cmd_tcm2
#file    sys/dev/tcm/tcm2_sm2.c                             cmd_tcm2
#file    sys/dev/tcm/tcm2_sigver.c                          cmd_tcm2
#file    sys/dev/tcm/tcm2_test.c                            cmd_tcm2
#file    sys/dev/tcm/tcm2_context.c                         cmd_tcm2
#file    sys/dev/tcm/tcm2_nv.c                              cmd_tcm2
#file    sys/dev/tcm/tcm2_pcr.c                             cmd_tcm2
#file    Targets/ls2k/dev/ls2k_spi_tcm2.c                   cmd_tcm2

# OTG Device
file sys/dev/usb/otg-dev/dwc2_udc.c		otg-device
file sys/dev/usb/otg-dev/dwc2_udc_otg.c		otg-device
file sys/dev/usb/otg-dev/printer.c		otg-device
file sys/dev/usb/otg-dev/gadget_config.c	otg-device
file sys/dev/usb/otg-dev/usbstring.c		otg-device
file sys/dev/usb/otg-dev/epautoconf.c		otg-device
file sys/dev/usb/otg-dev/otg_cmd.c		otg-device

#Gmac
file	sys/dev/gmac/synopGMAC_Dev.c
file	sys/dev/gmac/synopGMAC_network_interface.c
file	sys/dev/gmac/synopGMAC_pci_bus_interface.c
file	sys/dev/gmac/synopGMAC_plat.c

file	pmon/arch/loongarch/ls3a/dma_coherent.c
#nand
file   sys/dev/nand/m25p80.c m25p80 & nand needs-flag
file   sys/dev/nand/ls2k-nand.c nand needs-flag
file   sys/dev/nand/spinand_lld.c spinand_lld & nand needs-flag
#lio
file	sys/dev/nand/chips/gen_probe.c	cfi_flash & nand needs-flag
file	sys/dev/nand/chips/cfi_util.c	cfi_flash & nand needs-flag
file	sys/dev/nand/chips/cfi_probe.c  cfi_flash & nand needs-flag
file	sys/dev/nand/chips/jedec_probe.c jedec_flash & nand needs-flag
file	sys/dev/nand/chips/cfi_cmdset_0001.c	cfi_flash & nand needs-flag
file	sys/dev/nand/chips/cfi_cmdset_0002.c	cfi_flash & nand needs-flag
file	sys/dev/nand/chips/cfi_cmdset_0020.c	cfi_flash & nand needs-flag
#Emmc & TFCARD
device emmc
attach emmc at localbus

device tfcard
attach tfcard at localbus

file sys/dev/mmc/mmc.c       mci
file sys/dev/mmc/sd_card.c   mci
file sys/dev/mmc/core.c      mci needs-flag 
file sys/dev/mmc/ls_mci.c    mci 
file sys/dev/mmc/mci_pmon.c  mci 


device	syn: ether, ifnet
attach	syn at localbus
file	sys/dev/gmac/if_gmac.c gmac

# Ethernet driver for Discovery ethernet
device	gt: ether, ifnet, ifmedia
attach	gt at localbus
file	sys/dev/ic/if_gt.c			gt

device  lotg : usbbus
attach  lotg at lotgbus

#
# Media Indepedent Interface (mii)
#
include "sys/dev/mii/files.mii"

# Various PCI bridges

include "sys/dev/pci/files.pci"

#For 7A pci gmac
device	pcisyn:ether, ifnet
attach	pcisyn at pci


#
# Machine-independent ATAPI drivers
#

include "sys/dev/ata/files.ata"

#
# SCSI framework
#
include "sys/scsi/files.scsi"

#
# Custom application files
#
include "pmon/custom/files.custom"

#
# Multi Processors application files
#
include "pmon/arch/loongarch/acpi/mp/files.mp"

file sys/dev/mmc_pci/mmc.c pciemmc needs-flag
file sys/dev/mmc_pci/mmc-ls2k1500.c pciemmc needs-flag

device pciemmc
attach pciemmc at pci


