#include <sys/types.h>
#include "pmon.h"

#include "../loongson/loongson2_def.h"
#include "target/mem_ctrl.h"
#include "../include/ls2k.h"
#include "../../../pmon/arch/loongarch/common/ddr4/ddr4_param_debug.c"

#include "../../../pmon/arch/loongarch/early_printf.c"
#include "../../../pmon/arch/loongarch/loongarch_delay.c"
#include "../pci_dev/ls2k_init.c"

ddr_ctrl mm_ctrl_info;
i2c_param i2c_node_info;

extern int ddr4_init (uint64_t node_num, ddr_ctrl *mm_ctrl);

void mm_feature_init(void)
{
#ifdef LOONGSON_2K2000
	mm_ctrl_info.mc_type = LS2K2000_MC_TYPE;
#elif LOONGSON_2K1500
	mm_ctrl_info.mc_type = LS2K1500_MC_TYPE;
#endif
	mm_ctrl_info.mc_interleave_offset = 8;
	mm_ctrl_info.mc_regs_base = DDR_CFG_BASE;
	mm_ctrl_info.cache_mem_base = 0xa000000000000000;
	mm_ctrl_info.dimm_info_in_flash_offset = DIMM_INFO_IN_FLASH_OFFS;
	mm_ctrl_info.ddr_freq = DDR_FREQ;
	mm_ctrl_info.ddr_freq_2slot = DDR_FREQ_2SLOT;
	mm_ctrl_info.node_offset = NODE_OFFSET;
	mm_ctrl_info.tot_node_num = TOT_NODE_NUM;
	mm_ctrl_info.node_mc_num = MC_PER_NODE;
#ifdef BONITO_100M
	mm_ctrl_info.ref_clk = 100;
#elif BONITO_25M
	mm_ctrl_info.ref_clk = 25;
#endif
	mm_ctrl_info.spi_base = SPI_BASE;
	mm_ctrl_info.uart_base = UART_BASE;
	mm_ctrl_info.l2xbar_conf_addr = L2XBAR_CONF_ADDR;
#ifdef CONFIG_DDR_32BIT
	mm_ctrl_info.channel_width = 32;
#else
	mm_ctrl_info.channel_width = 64;
#endif
	mm_ctrl_info.dll_bypass = 0;
	//if you want change kernel high start address you should change the macro
	mm_ctrl_info.mem_base = HIGH_MEM_WIN_BASE_ADDR;
	/* mm_ctrl is global variable */
#ifdef QUICK_START
	mm_ctrl_info.table.enable_early_printf      = 0;
#else
	mm_ctrl_info.table.enable_early_printf      = 1;
#endif
	mm_ctrl_info.table.ddr_fast_init            = 0;
	mm_ctrl_info.table.enable_half_freq         = 0;
#ifdef DDR3_MODE
	mm_ctrl_info.table.ddr_param_store          = 0;
	mm_ctrl_info.table.ddr3_dimm                = 1;
	mm_ctrl_info.table.enable_mc_vref_training  = 0;
	mm_ctrl_info.table.enable_ddr_vref_training = 0;
	mm_ctrl_info.table.enable_bit_training      = 0;
	mm_ctrl_info.table.disable_dimm_ecc         = 1;
#else
	mm_ctrl_info.table.ddr_param_store          = 1;
	mm_ctrl_info.table.ddr3_dimm                = 0;
	mm_ctrl_info.table.enable_mc_vref_training  = 1;
	mm_ctrl_info.table.enable_ddr_vref_training = 1;
	mm_ctrl_info.table.enable_bit_training      = 0;
	mm_ctrl_info.table.disable_dimm_ecc         = 1;
#endif
	mm_ctrl_info.table.low_speed                = 0;
#ifdef AUTO_MEM_CONFIG
	mm_ctrl_info.table.auto_ddr_config          = 1;
#else
	mm_ctrl_info.table.auto_ddr_config          = 0;
#endif
	mm_ctrl_info.table.enable_ddr_leveling      = 1;
	mm_ctrl_info.table.print_ddr_leveling       = 0;
	mm_ctrl_info.table.vref_training_debug      = 0;
	mm_ctrl_info.table.bit_training_debug       = 1;
	mm_ctrl_info.table.enable_write_training    = 1;
	mm_ctrl_info.table.debug_write_training     = 0;
	mm_ctrl_info.table.print_dll_sample         = 0;
	mm_ctrl_info.table.disable_dq_odt_training  = 1;
	mm_ctrl_info.table.lvl_debug                = 0;
	mm_ctrl_info.table.disable_dram_crc         = 1;
#ifdef	MEM_TWO_MODE_ENABLE
	mm_ctrl_info.table.two_t_mode_enable        = 1;
#else 
	mm_ctrl_info.table.two_t_mode_enable        = 0;
#endif
	mm_ctrl_info.table.disable_read_dbi         = 1;
	mm_ctrl_info.table.disable_write_dbi        = 1;
	mm_ctrl_info.table.disable_dm               = 0;
	mm_ctrl_info.table.preamble2                = 0;
	mm_ctrl_info.table.set_by_protocol          = 1;
	mm_ctrl_info.table.param_set_from_spd_debug = 0;
	mm_ctrl_info.table.refresh_1x               = 1;
	mm_ctrl_info.table.spd_only                 = 0;
	mm_ctrl_info.table.ddr_debug_param          = 0;
	mm_ctrl_info.table.ddr_soft_clksel          = 1;
#ifdef LS_STR
	mm_ctrl_info.table.str                      = 1;
#else
	mm_ctrl_info.table.str                      = 0;
#endif
#ifdef DDR3_MODE
	mm_ctrl_info.vref.vref_range                = 1;
	mm_ctrl_info.vref.vref_value                = 0x60;
	mm_ctrl_info.table.pda_mode                 = 0;
#else
	mm_ctrl_info.vref.vref_range                = 0;
	mm_ctrl_info.vref.vref_value                = 0x58;
	mm_ctrl_info.table.pda_mode                 = 1;
#endif
	mm_ctrl_info.table.signal_test              = 0;
	mm_ctrl_info.vref.mc_vref_adjust            = 0x0;
	mm_ctrl_info.vref.ddr_vref_adjust           = 0x0;
	mm_ctrl_info.vref.vref_init                 = 0x20;
	mm_ctrl_info.data.rl_manualy                = 0;
#ifdef CONFIG_DDR_32BIT
	mm_ctrl_info.data.bit_width                 = 32;
#else
	mm_ctrl_info.data.bit_width                 = 64;
#endif
	mm_ctrl_info.data.nc16_map                  = 0;
	mm_ctrl_info.data.gate_mode                 = 0;
#ifdef DDR_RESET_REVERT
	mm_ctrl_info.data.pad_reset_po              = 0x2;
#else
	mm_ctrl_info.data.pad_reset_po              = 0;
#endif
	mm_ctrl_info.data.wrlevel_count_low         = 0x0;
	mm_ctrl_info.vref.vref_bits_per             = 0x0;
	mm_ctrl_info.vref.vref_bit                  = 0x0;
	mm_ctrl_info.data.ref_manualy               = 0x0;
#ifdef LOONGSON_2K1500
#if DDR_FREQ >400
	mm_ctrl_info.param.dll_ck_mc0               = 0x48;
	mm_ctrl_info.param.dll_ck_mc1               = 0x48;
#else
	mm_ctrl_info.param.dll_ck_mc0               = 0x80;
	mm_ctrl_info.param.dll_ck_mc1               = 0x80;
#endif
#else
#ifndef MC0_DLL_CK
#define MC0_DLL_CK 0x44
#endif
	mm_ctrl_info.param.dll_ck_mc0               = MC0_DLL_CK;
	mm_ctrl_info.param.dll_ck_mc1               = MC0_DLL_CK;
#endif
	mm_ctrl_info.param.RCD                      = 0;
	mm_ctrl_info.param.RP                       = 0;
	mm_ctrl_info.param.RAS                      = 0;
	mm_ctrl_info.param.REF                      = 0;
	mm_ctrl_info.param.RFC                      = 0;

	mm_ctrl_info.ocd.pad_clk_ocd                = PAD_CLK_OCD;
	mm_ctrl_info.ocd.pad_ctrl_ocd               = PAD_CTRL_OCD;
	mm_ctrl_info.ocd.pad_ds_split               = PAD_DS_SPLIT_ALL;
	mm_ctrl_info.ocd.ddr_out_ocd                = 0;

	mm_ctrl_info.odt.rtt_nom_1r_1slot           = RTT_NOM;
	mm_ctrl_info.odt.rtt_park_1r_1slot          = RTT_PARK;
	mm_ctrl_info.odt.mc_dqs_odt_1cs             = MC_DQS_ODT;
	mm_ctrl_info.odt.mc_dq_odt_1cs              = MC_DQ_ODT;

	mm_ctrl_info.odt.rtt_nom_2r_1slot           = RTT_NOM_2RANK;
	mm_ctrl_info.odt.rtt_park_2r_1slot          = RTT_PARK_2RANK;

	mm_ctrl_info.odt.rtt_nom_1r_2slot_cs0       = RTT_NOM_CS0;
	mm_ctrl_info.odt.rtt_park_1r_2slot_cs0      = RTT_PARK_CS0;
	mm_ctrl_info.odt.rtt_nom_1r_2slot_cs1       = RTT_NOM_CS1;
	mm_ctrl_info.odt.rtt_park_1r_2slot_cs1      = RTT_PARK_CS1;

	mm_ctrl_info.odt.rtt_nom_2r_2slot_cs0       = RTT_NOM_2R_CS0;
	mm_ctrl_info.odt.rtt_park_2r_2slot_cs0      = RTT_PARK_2R_CS0;
	mm_ctrl_info.odt.rtt_nom_2r_2slot_cs2       = RTT_NOM_2R_CS2;
	mm_ctrl_info.odt.rtt_park_2r_2slot_cs2      = RTT_PARK_2R_CS2;

	mm_ctrl_info.odt.mc_dqs_odt_2cs             = MC_DQS_ODT_2CS;
	mm_ctrl_info.odt.mc_dq_odt_2cs              = MC_DQ_ODT_2CS;

	mm_ctrl_info.sameba_adj                     = MC_PHY_REG_DATA_070;
	mm_ctrl_info.samebg_adj                     = MC_PHY_REG_DATA_078;
	mm_ctrl_info.samec_adj                      = MC_PHY_REG_DATA_080;
	mm_ctrl_info.samecs_adj                     = MC_PHY_REG_DATA_090;
	mm_ctrl_info.diffcs_adj                     = MC_PHY_REG_DATA_098;

	mm_ctrl_info.i2c_node                       = &i2c_node_info;

	/* paster parameter */
	mm_ctrl_info.paster.mc0_enable              = MC0_ENABLE;
	mm_ctrl_info.paster.mc1_enable              = MC1_ENABLE;

	mm_ctrl_info.paster.mc0_memsize             = MC0_MEMSIZE;
	mm_ctrl_info.paster.mc0_dram_type           = MC0_DRAM_TYPE;
	mm_ctrl_info.paster.mc0_dimm_type           = MC0_DIMM_TYPE;
	mm_ctrl_info.paster.mc0_module_type         = MC0_MODULE_TYPE;
	mm_ctrl_info.paster.mc0_cid_num             = MC0_CID_NUM;
	mm_ctrl_info.paster.mc0_ba_num              = MC0_BA_NUM;
	mm_ctrl_info.paster.mc0_bg_num              = MC0_BG_NUM;
	mm_ctrl_info.paster.mc0_csmap               = MC0_CSMAP;
	mm_ctrl_info.paster.mc0_dram_width          = MC0_DRAM_WIDTH;
	mm_ctrl_info.paster.mc0_module_width        = MC0_MODULE_WIDTH;
	mm_ctrl_info.paster.mc0_sdram_capacity      = MC0_SDRAM_CAPACITY;
	mm_ctrl_info.paster.mc0_col_num             = MC0_COL_NUM;
	mm_ctrl_info.paster.mc0_row_num             = MC0_ROW_NUM;
	mm_ctrl_info.paster.mc0_addr_mirror         = MC0_ADDR_MIRROR;
	mm_ctrl_info.paster.mc0_bg_mirror           = MC0_BG_MIRROR;

	mm_ctrl_info.paster.mc1_memsize             = MC1_MEMSIZE;
	mm_ctrl_info.paster.mc1_dram_type           = MC1_DRAM_TYPE;
	mm_ctrl_info.paster.mc1_dimm_type           = MC1_DIMM_TYPE;
	mm_ctrl_info.paster.mc1_module_type         = MC1_MODULE_TYPE;
	mm_ctrl_info.paster.mc1_cid_num             = MC1_CID_NUM;
	mm_ctrl_info.paster.mc1_ba_num              = MC1_BA_NUM;
	mm_ctrl_info.paster.mc1_bg_num              = MC1_BG_NUM;
	mm_ctrl_info.paster.mc1_csmap               = MC1_CSMAP;
	mm_ctrl_info.paster.mc1_dram_width          = MC1_DRAM_WIDTH;
	mm_ctrl_info.paster.mc1_module_width        = MC1_MODULE_WIDTH;
	mm_ctrl_info.paster.mc1_sdram_capacity      = MC1_SDRAM_CAPACITY;
	mm_ctrl_info.paster.mc1_col_num             = MC1_COL_NUM;
	mm_ctrl_info.paster.mc1_row_num             = MC1_ROW_NUM;
	mm_ctrl_info.paster.mc1_addr_mirror         = MC1_ADDR_MIRROR;
	mm_ctrl_info.paster.mc1_bg_mirror           = MC1_BG_MIRROR;
	mm_ctrl_info.param_reg_array                = &param_info;
}

void spd_i2c_init(void)
{
	i2c_node_info.i2c_mc[0][0].i2c_base = PHYS_TO_UNCACHED(0x1fe00120);
	i2c_node_info.i2c_mc[0][0].devid.slot0_addr = 0x0;
	i2c_node_info.i2c_mc[0][0].devid.slot1_addr = 0x1;
}

void shutdown_cpu(void)
{
	uint64_t i;
	pr_info("Shut down CPU\n");
	for (i = 1; i < TOT_NODE_NUM; i++) {
		readl(PHYS_TO_UNCACHED(0x1fe001d0) | (i << NODE_OFFSET)) = 0;
	}
}

void startup_cpu(void)
{
	uint64_t i;
	pr_info("start up other CPU\n");
	for (i = 0; i < TOT_NODE_NUM; i++) {
		pr_info("node %d\n", i);
		readl(PHYS_TO_UNCACHED(0x1fe00420) | (i << NODE_OFFSET)) |= (1 << 23);
		readl(PHYS_TO_UNCACHED(0x1fe001d0) | (i << NODE_OFFSET)) = 0xffffffff;
	}

}

#ifdef LS2K2000_PAI_OPTION
 // set GPIO43 val for BEEP
#define  BEEP_GPIO  43
// set GPIO50 val for DP
#define  DP_GPIO  50
void beep_init()
{
	int num = 1000;
	uint64_t base = (gl_node_id << NODE_OFFSET) | LS2K_CONFBUS_BASE_ADDR;

	readl(base + 0x440) &= ~(0x1 << 10);
	readl(base + 0x444) &= ~(0xf << 10);
	readb(LS2K_GPIO_OEN_REG + BEEP_GPIO) = 0x00;
	readb(LS2K_GPIO_OEN_REG + DP_GPIO) = 0x00;
	readb(LS2K_GPIO_O_REG + DP_GPIO) = 0x00;
	while(num--) {
		delay(250);
		readb(LS2K_GPIO_O_REG + BEEP_GPIO) = 0x01;
		delay(250);
		readb(LS2K_GPIO_O_REG + BEEP_GPIO) = 0x00;
	}
	readb(LS2K_GPIO_O_REG + DP_GPIO) = 0x01;
}
#endif

extern char stack;
extern char _gp;
void cache_stage()
{
	pr_info("run in cache!\n");

	ls2k_feature_init();
	ls2k_fix_addr();
	startup_cpu();

#ifdef LS2K2000_PAI_OPTION
	beep_init();
#endif

	mm_feature_init();
	spd_i2c_init();
	ddr4_init(TOT_NODE_NUM, &mm_ctrl_info);

	pr_info("mem init done!\n");
#ifdef QUICK_START
       asm volatile("st.d %1,%0,0x28;"::"r"(0x800000001fe01100),"r"(0x9000000090000000));
       asm volatile("st.d %1,%0,0x30;"::"r"(0x800000001fe01100),"r"(&_gp));
       asm volatile("st.d %1,%0,0x20;"::"r"(0x800000001fe01100),"r"(&ls2k_init));
#else
	ls2k_init();
#endif
	pr_info("ls2k init done!\n");
#ifdef LOONGSON_2K2000
	hdmi_init();
	pr_info("hdmi phy init done!\n");
	if (((readl(LS2K_ACPI_PM1_CNT_REG) >> 10) & 0x7) == SLEEP_TYPE_S3) {
		realinit_loongarch();
	} else
#endif
		init_loongarch();
}
