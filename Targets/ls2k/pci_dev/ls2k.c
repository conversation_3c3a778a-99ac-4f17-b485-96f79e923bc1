#include <cpu.h>
#include "target/ls2k_config.h"
#include "gmemparam.h"
#include "ls2k_dev.c"

#define LS2K_PCIE_CFG_READ(data,reg)                                    \
        readl(0x8000000000000000 + data + reg)

#define LS2K_PCIE_CLEAR_PORT_IRQ(addr)                                  \
        do {                                                            \
                readl(addr) = 0x60000000;                               \
                if (LS2K_PCIE_CFG_READ(0x60000000,0x18)) {              \
                        LS2K_PCIE_CFG_READ(0x60000000, 0x1c) =          \
                                LS2K_PCIE_CFG_READ(0x60000000, 0x18);   \
                        break;                                          \
                }                                                       \
                readl(addr) = 0x0;                                      \
	} while(0)
ls7a_resource_table_t ls7a_cfg_t;

void loop_delay(uint64_t loops)
{
	volatile uint64_t counts = loops;
	if (ls7a_cfg_t.config.speedup) {
		counts /= 5;
	}
    counts = counts / 1000;
    if(counts <= 0)
        counts = 10;
    delay(counts);
}

/* 
 * Clean the Ls7a internal PCIe prot useless IRQ, to workaround the bug
 * that some PCIe device will creat pseudo interrupt in kernel.
 * This code has some risk that if the PCIe PHY connect device time are
 * not enough, this funciton will lose efficacy.
 */
int ls2k_clear_pcie_portirq(void)
{
	int i;

	for (i = 0; i < (ls7a_cfg_t.pcie.num); i++) {
		/* This debug print can not delete,if improve the cpu freq the code*/
		pr_info("clear pcie 0x%lx\n", ls7a_cfg_t.pcie.pcie_cfg_buffer[i]);
		/* Clear the PCIe internal port useless irq. Otherwise it maybe cause*/
		/* the linked device irq error in kernel.*/
		LS2K_PCIE_CLEAR_PORT_IRQ(ls7a_cfg_t.pcie.pcie_cfg_buffer[i]);
	}
	return 1;
}
