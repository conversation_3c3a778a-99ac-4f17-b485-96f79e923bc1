#include <cpu.h>
#include "target/ls2k_config.h"
#include <machine/bus.h>

extern void ls2k2000_hw_init(uint64_t node_id);
extern void ls2k1500_hw_init(uint64_t node_id);
extern void prg_init(uint32_t prg_vreg_regulator);
extern void pcie_init(void);
extern void rio_init(uint32_t, uint8_t, uint32_t);
extern void sata_phy_init(void);
extern void usb2_init(void);
extern void usb3_init(void);

extern uint64_t gl_node_id;

#define PRG_VREG 0x8000

pcie_desc ls2k_pcie_ctrl[] = {
#ifdef LOONGSON_2K2000
	{"F0", 0, LS2K_PCIE_F0_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X4_MODE, LS7A_PCIE_EQ_MODE0},
#else
	{"F0", 0, LS2K_PCIE_F0_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X1_MODE /* LS7A_PCIE_FLIP_EN*/, LS7A_PCIE_EQ_MODE0},
#endif
#ifdef LS2K2000_PAI_OPTION
	{"F1", 0, LS2K_PCIE_F1_DISABLE, 8, LS7A_PCIE_GEN2_MODE, LS7A_PCIE_X1_MODE, LS7A_PCIE_EQ_MODE0},
#else
	{"F1", 0, LS2K_PCIE_F1_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X4_MODE, LS7A_PCIE_EQ_MODE0},
#endif
	{"H ", 0, LS2K_PCIE_H_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X8_MODE, LS7A_PCIE_EQ_MODE0},
#ifdef LOONGSON_2K2000
	{"G0", 0, LS2K_PCIE_G0_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X4_MODE, LS7A_PCIE_EQ_MODE0},
#else
	{"G0", 0, LS2K_PCIE_G0_DISABLE, 8, LS7A_PCIE_GEN3_MODE, LS7A_PCIE_X4_MODE, LS7A_PCIE_EQ_MODE0},
#endif
};

sata_desc ls2k_sata_ctrl[] = {
	{LS2K_SATA_DISABLE, {{LS2K_SATA_PORT0_DISABLE, LS2K_SATA_PORT1_DISABLE, LS2K_SATA_PORT2_DISABLE, LS2K_SATA_PORT3_DISABLE}}, 0x53, 0x00, 0x72, 0x20, 0xc0, 0x20},
};

usb_desc ls2k_usb_ctrl[] = {
	{LS2K_USB0_DISABLE, {{0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}}},
	{LS2K_USB1_DISABLE, {{0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}, {0x301c0c8000ULL}}},
	/* XHCI ONLY support first param */
	{LS2K_XHCI_DISABLE, {{0}}},
};

gmac_desc ls2k_gmac_ctrl[] = {
	{LS2K_GMAC0_DISABLE},
};

iommu_desc ls2k_iommu_ctrl[] = {
    {LS2K_IOMMU_DISABLE},
};

void ls2k_pll_adapt(ls7a_pll_table* pll_ctrl)
{
#ifdef LOONGSON_2K2000
	/*rapidio, gmac, sata3/usb3*/
	//pll_ctrl[LS2K_PLL0].pll_val = LS2K_PLL_VALUE(80, 8, 16, 13); //250 125 153.846
	pll_ctrl[LS2K_PLL0].pll_val = LS2K_PLL_VALUE(70, 14, 28, 23); //250 125 152.17
	pll_ctrl[LS2K_PLL0].div = 2;
	/*PIX0, default 38.2MHz for x800x600*/
	pll_ctrl[LS2K_PLL3].pll_val = LS2K_PLL_VALUE(40, 10, 10, 10);
	pll_ctrl[LS2K_PLL3].div = 1;
	/*PIX1, default 38.2MHz for x800x600*/
	pll_ctrl[LS2K_PLL4].pll_val = LS2K_PLL_VALUE(40, 10, 10, 10);
	pll_ctrl[LS2K_PLL4].div = 1;
#else
	/*gmac, sata3/usb3*/
	pll_ctrl[LS2K_PLL0].pll_val = LS2K_PLL_VALUE(80, 0, 16, 13); //250 125 151
	pll_ctrl[LS2K_PLL0].div = 4;
	// /* EMMC , node, */
	//pll_ctrl[LS2K_PLL2].pll_val = LS2K_PLL_VALUE(80, 80, 2, 0); //25 1G 0
	//pll_ctrl[LS2K_PLL2].div = 4;
#endif

}

void ls2k_feature_init(void)
{
	ls7a_cfg_t.config.ls3a_chip_type = CAT(LS,CAT(CPU_TYPE,_CHIP_TYPE));
	ls7a_cfg_t.config.ls3a_chip_name = STR(CPU_TYPE);
	ls7a_cfg_t.config.ls7a_chip_type = CAT(LS,CAT(BRIDGE_TYPE,_CHIP_TYPE));
	ls7a_cfg_t.config.ls7a_chip_name = STR(BRIDGE_TYPE);
	ls7a_cfg_t.config.pci_conf_base = PCIE_CONF_BASE;
	ls7a_cfg_t.config.speedup= 0;
	/* pll */
	ls2k_pll_adapt(ls7a_cfg_t.pll);
	/* pcie */
	ls7a_cfg_t.pcie.reset_delay = LS7A_PCIE_RESET_DELAY;
	ls7a_cfg_t.pcie.controller = ls2k_pcie_ctrl;
	/* sata */
	ls7a_cfg_t.sata.controller = ls2k_sata_ctrl;
	/* usb */
	ls7a_cfg_t.usb.controller = ls2k_usb_ctrl;
	/* gmac */
	ls7a_cfg_t.gmac.controller = ls2k_gmac_ctrl;
	/* iommu */
	ls7a_cfg_t.iommu.controller = ls2k_iommu_ctrl;

	/* display */
	ls7a_cfg_t.dc.graphics_disable = LS2K_GRAPHICS_DISABLE;
	ls7a_cfg_t.dc.gmem_disable = LS2K_GMEM_DISABLE;
	ls7a_cfg_t.dc.gpu_disable = LS2K_GPU_DISABLE;

	/* misc devices */
	ls7a_cfg_t.misc.lpc_disable = LS2K_LPC_DISABLE;
	ls7a_cfg_t.misc.fan.min_rpm = 5000;
	ls7a_cfg_t.misc.fan.min_rpm = 10000;
}

void config_one_pll(uint64_t conf_base, int pll_num)
{
	uint32_t i, val32;
	uint64_t pll_base = conf_base + CONF_PLL0_OFFSET + (pll_num % 5) * 0x10;
	/*switch to backup clk*/
	readl(pll_base + 0x4) &= ~(0x7 << LS2K_PLL_SEL0_OFFSET);
	/*power down pll*/
	readl(pll_base + 0x4) |= (1 << LS2K_PLL_PD_OFFSET);
	/*disable pll configure*/
	readl(pll_base + 0x4) &= ~(1 << LS2K_PLL_SET_OFFSET);

	/*configure pll parameters*/
	readl(pll_base) = ls7a_cfg_t.pll[pll_num].pll_val;
	val32 = readl(pll_base + 0x4);
	if (pll_num == 1)
		readl(pll_base + 0x4) = (val32 & ~(0x3f << LS2K_PLL_DIV_REFC_OFFSET) & ~(0xf << LS2K_GMEM_DIV_RESETn_OFFSET)) | ls7a_cfg_t.pll[pll_num].div;
	else
		readl(pll_base + 0x4) = (val32 & ~(0x3f << LS2K_PLL_DIV_REFC_OFFSET)) | ls7a_cfg_t.pll[pll_num].div;

	/*enable pll configure*/
	readl(pll_base + 0x4) |= (1 << LS2K_PLL_SET_OFFSET);
	/*not bypass pll*/
	readl(pll_base + 0x4) &= ~(0x1 << LS2K_PLL_BYPASS_OFFSET);
	/*power up pll*/
	readl(pll_base + 0x4) &= ~(0x1 << LS2K_PLL_PD_OFFSET);

	/*poll lock signal*/
	i = 0x1000;
	do {
		val32 = readl(pll_base + 0x4) & (0x1 << LS2K_PLL_LOCK_OFFSET);
		i--;
	} while ((!val32) && i);

	if (i > 0) {
		/* select pll out */
		readl(pll_base + 0x4) |= (0x7 << LS2K_PLL_SEL0_OFFSET);
		return;
	}
	pr_info("!!!LS2K PLL%d soft configure fail.\r\n", pll_num % 5);
	while(1);
}

int device_cfg (void)
{
	uint64_t i = 0;

	usb2_init();

#ifdef LOONGSON_2K2000
	prg_init(PRG_VREG);
#endif
	pcie_init();
	if (ls7a_cfg_t.sata.controller[0].disable) {
		readl(LS2K_CONFBUS_BASE_ADDR + CONF_SB_OFFSET + 0x4) &= ~(0x1 << 11);
	} else {
		sata_phy_init();
		for(i = 0; i < 4; i++) {
			if (ls7a_cfg_t.sata.controller[0].disable_port & (1 << i)) {
				readl(LS2K_CONFBUS_BASE_ADDR + 0x740) |=  (0x1 << (12 + i));
				readl(LS2K_CONFBUS_BASE_ADDR + 0x740) &= ~(0x1 << (16 + i));
			}
		}
	}

	/*for ls2k1500, this bit decide whether SPI1 enable*/
	if (!ls7a_cfg_t.misc.lpc_disable) {
		readl(LS2K_CONFBUS_BASE_ADDR + CONF_SB_OFFSET + 0x4) |= (0x1 << 0);
	}

#ifdef LOONGSON_2K2000
	if (ls7a_cfg_t.usb.controller[2].disable)
		readl(LS2K_CONFBUS_BASE_ADDR + CONF_SB_OFFSET) &= ~(0x1 << 19);
	else
		usb3_init();
#ifdef LS_RIO
	/* FLAGS LS_RIO_OUTSIDE use outside clock source, 0 use chip inside clock source */
	rio_init(LSRIO_SPEED_2_5, LSRIO_PORT_G0, 0);
#endif

	/*GNET*/
	if (ls7a_cfg_t.gmac.controller[0].disable) {
		readl(LS2K_CONFBUS_BASE_ADDR + CONF_SB_OFFSET) &= ~(0x3 << 4);
		readl(LS2K_CONFBUS_BASE_ADDR + 0x770) &= ~(1 << 24);
		pr_info("GNET disabled\r\n");
	}


	/*config sdio pad to support high freq*/
	readl(LS2K_CONFBUS_BASE_ADDR + 0x6e0) |= 0x3;
	/*fix emmc and sdio addr*/
	writel(0x79990000, PHYS_TO_UNCACHED(0xfe00000000 | 0x10 | (28 << 11) | 0 << 8));
	writel(0x79991000, PHYS_TO_UNCACHED(0xfe00000000 | 0x10 | (28 << 11) | 1 << 8));

	/*DC*/
	if (ls7a_cfg_t.dc.graphics_disable) {
		unsigned long dc_base_addr;
		dc_base_addr = PHYS_TO_UNCACHED(0xe0060000000);
		readl(PHYS_TO_UNCACHED(PCIE_CONF_BASE | 0x10 | (6 << 11) | (1 << 8))) = 0x60000000;
		readl(PHYS_TO_UNCACHED(PCIE_CONF_BASE | 0x04 | (6 << 11) | (1 << 8))) |= 0x147;
		//disable dc dma out put
		readl(dc_base_addr + 0x1240 + 0x0) &= ~(1 << 8);
		readl(dc_base_addr + 0x1250 + 0x0) &= ~(1 << 8);
		//disable dc clk
		readl(LS2K_CONFBUS_BASE_ADDR + 0x420) &= ~(1 << 7);
		//disable ClockEn
		readl(dc_base_addr + 0x1240 + 0x180) &= ~(1 << 8);
		readl(dc_base_addr + 0x1250 + 0x180) &= ~(1 << 8);
		//pix0 pd
		readl(LS2K_CONFBUS_BASE_ADDR + 0x4b4) |= (1 << 23);
		//pix1 pd
		readl(LS2K_CONFBUS_BASE_ADDR + 0x4c4) |= (1 << 23);
		//PhyResetn and PhyEn
		readl(dc_base_addr + 0x1240 + 0x5c0) &= ~(0x3 << 1);
		readl(dc_base_addr + 0x1250 + 0x5c0) &= ~(0x3 << 1);
		//Cfg_vdac_pd[4] VGA_off_det_en[1] VGA_on_det_en[0]
		readl(dc_base_addr + 0x1bb0) |= ((0x1 << 4) | (0x3 << 0));
		/* release pcie mem */
		readl(PHYS_TO_UNCACHED(PCIE_CONF_BASE | 0x10 | (6 << 11) | (1 << 8))) = 0x0;
	} else {
		readl(LS2K_CONFBUS_BASE_ADDR + 0x420) |= (1 << 7);
		readl(LS2K_CONFBUS_BASE_ADDR + 0x444) &= ~(0x3 << 14);
		readl(LS2K_CONFBUS_BASE_ADDR + 0x444) |= (0x2 << 14);
		/* config dvo pad to support 1080p  */
		readl(LS2K_CONFBUS_BASE_ADDR + 0x6e0) |= (0x3 << 16);
		pr_info("Graphics clk enabled\r\n");
	}

	/*GPU*/
	if (ls7a_cfg_t.dc.gpu_disable) {
		readl(LS2K_CONFBUS_BASE_ADDR + 0x420) &= ~(1 << 6);
		pr_info("GPU clk disabled\r\n");
	} else {
		readl(LS2K_CONFBUS_BASE_ADDR + 0x420) |= (1 << 6);
		pr_info("GPU clk enabled\r\n");
	}

	/*put PCIE device detect later, else you need to add more delay*/
	/*delay at least 200ms*/
	mdelay(200);

	writel(readl(LS2K_CONFBUS_BASE_ADDR + 0x420) | (1 << 4), LS2K_CONFBUS_BASE_ADDR + 0x420);
	/* delay at least 200ms */
	mdelay(200);
	writel(readl(LS2K_CONFBUS_BASE_ADDR + 0x420) & ~(1 << 4), LS2K_CONFBUS_BASE_ADDR + 0x420);
	pr_info("GPU soft reset \r\n");

#endif
	return 0;
}

void ls2k_fix_addr(void)
{
	unsigned long long ht_conf_base = PHYS_TO_UNCACHED(PCIE_CONF_BASE);
	/*config fix address bar for Misc devices block*/
	unsigned long long addr = (ht_conf_base | (2 << 11) | (0 << 8));
	readl(addr + 0x10) = MISC_BASE_ADDR;
	readl(addr + 0x4) |= 0x2;
}

void ls2k_resource_cfg(void)
{
	uint64_t i;

	gl_node_id = 0;
	uint64_t base = (gl_node_id << NODE_OFFSET) | LS2K_CONFBUS_BASE_ADDR;

	config_one_pll(base, 0);
	pr_info("LS2K2000 pll configure done.\r\n");

#ifdef LOONGSON_2K2000
	/* enable HDA0 */
	readl(base + CONF_SB_OFFSET) |= (1 << 20);
	/* enable HDA1 */
	readl(base + CONF_SB_OFFSET) |= (1 << 21);
	readl(base + CONF_SB_OFFSET + 4) &= ~(1 << 31);
	ls2k2000_hw_init(0);
#ifdef LS2K2000_PAI_OPTION
	/* usb oc config */
	readl(base + 0x448) = 0x3e55555;
#endif
	ls2k2000_gpu_mmap_cfg(LS2K2000_GMEM_SIZE << 20);
	/* enable SE int*/
	readl(base + CONF_SB_OFFSET + 4) |= (1 << 2);
	/*disable pcie_f0/f1/h/g0_uca_en and graphic_uca_en*/
	readl(base + CONF_NB_OFFSET + 4) &= ~(0x1f << 0);
	/* acpi wakeup status clear when shutdown abnormally*/
	writel(readl(LS2K_ACPI_PM1_STS_REG) | (1 << 11), LS2K_ACPI_PM1_STS_REG);
#elif LOONGSON_2K1500
	/* otg oc config */
	readl(base + 0x448) = 0x3e50000;
	ls2k1500_hw_init(0);
#endif
	/*change INT and HPET fix address*/
	readl(base + 0x460) = (INT_BASE_ADDR | 0x4);
	readl(base + 0x464) = (HPET_BASE_ADDR | 0x4);
	pr_info("LS2K hardware init done.\r\n");
	/*3. device configure*/
	device_cfg();
	pr_info("\r\nLS2K init done.\r\n");
}

