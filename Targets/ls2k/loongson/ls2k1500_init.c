#include <include/stdarg.h>
#include <include/stdio.h>
#include <include/file.h>
#include <linux/io.h>
#include <sys/ioccom.h>
#include <sys/types.h>
#include <frame.h>
#include <termio.h>
#include <string.h>
#include <stdlib.h>
#include <dev/pci/pcivar.h>

#include <autoconf.h>
#include <pmon.h>
#include <machine/cpu.h>
#include <machine/pio.h>

#include <pmon/dev/ns16550.h>
#include "loongson2_def.h"

#include "target/ls2k.h"

#include "cfi_flash.h"
#include "m25p80.h"

void ls2k1500_beep(void)
{
	unsigned int val;
    /*beep on*/
     val = readl(PHYS_TO_UNCACHED(0x10010440));
     val |= (1 << 0);
     writel(val, PHYS_TO_UNCACHED(0x10010440));
    //pwm0
    writel(0x10000, PHYS_TO_UNCACHED(0x100a0008));
    writel(0x7fff, PHYS_TO_UNCACHED(0x100a0004));
    writel(1, PHYS_TO_UNCACHED(0x100a000c));
    mdelay(500);
	//beep off
    writel(0, PHYS_TO_UNCACHED(0x100a000c));
    readl(PHYS_TO_UNCACHED(0x100e0800)) = 0;
    readl(PHYS_TO_UNCACHED(0x100e0900)) = 0;
}

void ls2k1500_init_before(void)
{
	unsigned int val;
	
	ls2k1500_beep();
	/*disable spi bulk read*/
	readq(0x800000001fe02400) =  0x1c000000UL;
	readq(0x800000001fe02440) = 0xffffffffff000000UL;
	readq(0x800000001fe02480) = 0x1c0000d4UL;

	readq(0x800000001fe02500) =  0x1c000000UL;
	readq(0x800000001fe02540) = 0xffffffffff000000UL;
	readq(0x800000001fe02580) = 0x1c0000d4UL;

#ifndef USE_LIO_FUNCTION
	/*map lio to memory to fix sys halt*/
	readq(0x800000001fe02408) =  0x1d000000UL;
	readq(0x800000001fe02448) = 0xffffffffff000000UL;
	readq(0x800000001fe02488) = 0x000000f0UL;

	readq(0x800000001fe02508) =  0x1d000000UL;
	readq(0x800000001fe02548) = 0xffffffffff000000UL;
	readq(0x800000001fe02588) = 0x000000f0UL;
#endif
//mul function config
//gmac
#ifdef USE_GMAC1_FUNCTION
        val = readl(PHYS_TO_UNCACHED(0x10010440));
        val |= (1 << 10);
        writel(val, PHYS_TO_UNCACHED(0x10010440));
#endif
#if 1
		val = readl(PHYS_TO_UNCACHED(0x10010440));
		//val &= (~(3 << 26));
		val |= (2 << 26);
		writel(val, PHYS_TO_UNCACHED(0x10010440));
		writel(0x45000000, PHYS_TO_UNCACHED(0x0fe00000000ULL | (0x7ULL << 11) | (0x0ULL << 8) | (0x10ULL)));
#endif
//uart
#ifdef USE_UART_FUNCTION
	//enable uart 9/10/11
        val = readl(PHYS_TO_UNCACHED(0x10010440));
        val |= (7 << 29);
        writel(val, PHYS_TO_UNCACHED(0x10010440));
	//select  IO uart0 base
        val = readl(PHYS_TO_UNCACHED(0x10010444));
        val |= ((1 << 18));
        writel(val, PHYS_TO_UNCACHED(0x10010444));
#ifdef UART0_USE_NODE
	//select  node uart0 base
        val = readl(PHYS_TO_UNCACHED(0x10010444));
        val &= (~(1 << 18));
        writel(val, PHYS_TO_UNCACHED(0x10010444));
#endif
	//enable uart 3/4/5
        val = readl(PHYS_TO_UNCACHED(0x10010444));
        val |= (7 << 20);
        writel(val, PHYS_TO_UNCACHED(0x10010444));

	//enable uart 6/7/8
        val = readl(PHYS_TO_UNCACHED(0x10010444));
        val |= (7 << 24);
        writel(val, PHYS_TO_UNCACHED(0x10010444));
#endif
//uart2
#ifdef USE_LIO_FOR_UART1_2
        val = readl(PHYS_TO_UNCACHED(0x10010440));
        val &= (~(3 << 20));
        val |= (2 << 20);
        writel(val, PHYS_TO_UNCACHED(0x10010440));
#else
        val = readl(PHYS_TO_UNCACHED(0x10010440));
        val &= (~(3 << 20));
        writel(val, PHYS_TO_UNCACHED(0x10010440));
#endif
//i2c
#ifdef USE_I2C_FUNCTION
        val = readl(PHYS_TO_UNCACHED(0x10010440));
        val &= (~(0xf << 6));
        val |= (0xf << 6);
        writel(val, PHYS_TO_UNCACHED(0x10010440));
#endif
//sata led
#ifdef USE_SATALED_FUNCTION
        val = readl(PHYS_TO_UNCACHED(0x10010440));
        val &= (~(0x1 << 13));
        val |= (0x1 << 13);
        writel(val, PHYS_TO_UNCACHED(0x10010440));
#endif

//can
#ifdef USE_CAN_FUNCTION
        val = readl(PHYS_TO_UNCACHED(0x10010444));
        val &= (~(0x3 << 0));
        val |= (0x3 << 0);
        writel(val, PHYS_TO_UNCACHED(0x10010444));
#endif

//pwm
#ifdef USE_PWM_FUNCTION
        val = readl(PHYS_TO_UNCACHED(0x10010440));
        val &= (~(0xf << 0));
        val |= (0xf << 0);
        writel(val, PHYS_TO_UNCACHED(0x10010440));
#endif

//LIO
#ifdef USE_LIO_FUNCTION
        val = readl(PHYS_TO_UNCACHED(0x10010440));
        val &= (~(3 << 20));
        val |= (1 << 20);
        writel(val, PHYS_TO_UNCACHED(0x10010440));

        val = readl(PHYS_TO_UNCACHED(0x1fe00404));
        val &= (~(1 << 15));
        val |= (1 << 15);
        writel(val, PHYS_TO_UNCACHED(0x1fe00404));

#ifdef LIO_WIDTH16
        val = readl(PHYS_TO_UNCACHED(0x1fe00404));
        val &= ~((1 << 7)|(1<<13));
        val |= ((1 << 7)|(1<<13));
        writel(val, PHYS_TO_UNCACHED(0x1fe00404));
#else
        val = readl(PHYS_TO_UNCACHED(0x1fe00404));
        val &= ~((1 << 7)|(1<<13));
        writel(val, PHYS_TO_UNCACHED(0x1fe00404));
#endif

        val = readl(PHYS_TO_UNCACHED(0x1fe00404));
        val &= ~((0x1f << 2)|(0x1f << 8));
        val |= ((0x1f << 2)|(0x1f << 8));
        writel(val, PHYS_TO_UNCACHED(0x1fe00404));
#endif

}

void ls2k1500_init_after(void)
{
#if NCFI_FLASH
	printf("cfi probe\n");
	cfi_probe();
#endif
/*#if NNAND
	printf("nand probe\n");
	ls2k_nand_init();
#endif
*/
#if  NM25P80
       printf("m25p80 probe.\n");
       ls_m25p_probe();
#endif
}

