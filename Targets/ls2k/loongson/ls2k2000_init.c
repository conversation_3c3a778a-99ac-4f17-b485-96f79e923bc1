#include <include/stdarg.h>
#include <include/stdio.h>
#include <include/file.h>
#include <linux/io.h>
#include <sys/ioccom.h>
#include <sys/types.h>
#include <frame.h>
#include <termio.h>
#include <string.h>
#include <stdlib.h>
#include <dev/pci/pcivar.h>

#include <autoconf.h>
#include <pmon.h>
#include <machine/cpu.h>
#include <machine/pio.h>

#include <pmon/dev/ns16550.h>
#include "loongson2_def.h"

#include "target/ls2k.h"
#include "m25p80.h"
#include "spinand_lld.h"

static void ls2k2000_mux_option()
{
 	unsigned int val_lo;
    unsigned int val_hi;

	val_lo = readl(LS2K_MUX_REG);
    val_hi = readl(LS2K_MUX_REG + 0x4);

//PWM0, GPIO4
#ifdef USE_PWM0_FUNCTION
    val_lo |= (0x1 << 0);
#elif  USE_GPIO4_FUNCTION
	val_lo &= ~(0x1 << 0);
#endif/*USE_PWM0_FUNCTION*/


//PWM1,GPIO5
#ifdef USE_PWM1_FUNCTION
    val_lo |= (0x1 << 1);
#elif USE_GPIO5_FUNCTION
	val_lo &= ~(0x1 << 1);
#endif/*USE_PWM1_FUNCTION*/


//PWM2,CAN4,GPU_UART,GPIO6,GPIO7
#if defined(USE_PWM2_FUNCTION) || defined(USE_PWM3_FUNCTION) || defined(USE_GPIO6_FUNCTION) || defined(USE_GPIO7_FUNCTION)
#ifdef USE_PWM2_FUNCTION
    val_lo |= (0x1 << 2);
#elif USE_GPIO6_FUNCTION
	val_lo &= ~(0x1 << 2);
	val_hi &= ~(0x1 << 4);
	val_hi &= ~(0x1 << 27);
#endif /*USE_PWM2_FUNCTION*/
#ifdef USE_PWM3_FUNCTION
    val_lo |= (0x1 << 3);
#elif USE_GPIO7_FUNCTION
	val_lo &= ~(0x1 << 3);
	val_hi &= ~(0x1 << 4);
	val_hi &= ~(0x1 << 27);
#endif/*USE_PWM3_FUNCTION*/

#elif  defined(USE_CAN4_FUNCTION)
    val_lo &= ~((0x1 << 2) | (0x1 << 3));
    val_hi |= (0x1 << 4);
#elif defined(USE_GPU_UART_FUNCTION)
    val_lo &= ~((0x1 << 2) | (0x1 << 3));
    val_hi &= ~(0x1 << 4);
    val_hi |= (0x1 << 27);
#endif/*defind(USE_PWM2_FUNCTION) || defined(USE_PWM3_FUNCTION) || defined(USE_GPIO6_FUNCTION) || defined(USE_GPIO7_FUNCTION)*/


//PWM4,PWM5,GPIO8,GPIO9,CAN5
#if defined(USE_PWM4_FUNCTION) || defined(USE_PWM5_FUNCTION)|| defined(USE_GPIO8_FUNCTION) || defined(USE_GPIO9_FUNCTION)

#ifdef USE_PWM4_FUNCTION
    val_lo |= (0x1 << 4);
#elif USE_GPIO8_FUNCTION
	val_lo &= ~(0x1 << 4);
	val_hi &= ~(0x1 << 5);
#endif/*USE_PWM4_FUNCTION*/
#ifdef USE_PWM5_FUNCTION
    val_lo |= (0x1 << 5);
#elif USE_GPIO9_FUNCTION
	val_lo &= ~(0x1 << 5);
	val_hi &= ~(0x1 << 5);
#endif/*USE_PWM5_FUNCTION*/

#elif defined(USE_CAN5_FUNCTION)
    val_lo &= ~((0x1 << 4) | (0x1 << 5));
    val_hi |= (0x1 << 5);
#endif/*defined(USE_PWM4_FUNCTION) || defined(USE_PWM5_FUNCTION) || defined(USE_GPIO8_FUNCTION) || defined(USE_GPIO9_FUNCTION)*/


//LPC,UART2,UART9,UART10,UART11,I2C2,I2C3,CAN2,CAN3,GPIO32~GPIO39
#ifdef USE_LPC_FUNCTION
    val_lo |= (0x1 << 14);
#elif USE_UART2_ALL_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_lo |= (0xf << 19);
    val_lo &= ~(0x7 << 29);
#else
#ifdef USE_UART2_HALF_ALL_FUNCTION 
    val_lo &= ~(0x1 << 14);
    val_lo |= (0x3 << 21);
    val_lo &= ~(0x1 << 31);
#else
#ifdef USE_CAN0_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_lo &= ~(0x1 << 22);
    val_hi |= (0x1 << 0);
#elif USE_UART2_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_hi &= ~(0x1 << 0);
    val_lo |= (0x1 << 22);
#elif USE_GPIO38_FUNCTION || USE_GPIO39_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_lo &= ~(0x1 << 22);
    val_hi |= (0x1 << 0);
#endif/*USE_CAN0_FUNCTION*/
#ifdef USE_CAN1_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_lo &= ~(0x1 << 21);
    val_hi |= (0x1 << 1);
#elif USE_UART11_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_lo |= (0x1 << 21);
    val_hi &= ~(0x1 << 1);
#elif USE_GPIO35_FUNCTION || USE_GPIO36_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_lo &= ~(0x1 << 21);
    val_hi &= ~(0x1 << 1);
#endif/*USE_UART11_FUNCTION*/
#endif/*USE_UART2_HALF_ALL_FUNCTION*/
#ifdef USE_UART9_HALF_ALL_FUNCTION	
    val_lo &= ~(0x1 << 14);
    val_lo |= (0x3 << 19);
    val_lo |= (0x1 << 29);
    val_lo &= ~(0x1 << 30);
#else
#ifdef USE_CAN2_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_hi |= (0x1 << 2);
#else
#ifdef USE_I2C2_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_hi &= ~(0x1 << 2);
    val_lo |= (0x1 << 6); 
    val_lo &= ~(0x1 << 19); 
#elif USE_UART9_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_hi &= ~(0x1 << 2);
    val_lo |= (0x1 << 19);
    val_lo |= (0x1 << 29);
#elif USE_GPIO33_FUNCTION || USE_GPIO34_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_hi &= ~(0x1 << 2);
    val_lo &= ~(0x1 << 6);
    val_lo &= ~(0x1 << 19);
#endif/*USE_I2C2_FUNCTION*/
#endif/*USE_CAN2_FUNCTION*/
#endif/*USE_UART9_HALF_ALL_FUNCTION*/
#ifdef USE_CAN3_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_hi |= (0x1 << 3);
#else
#ifdef USE_I2C3_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_hi &= ~(0x1 << 3);
    val_lo |= (0x1 << 7);
    val_lo &= ~(0x1 << 20);
#elif USE_UART10_FUNCTION	/*ls2k2000 has error*/
    val_lo &= ~(0x1 << 14);
    val_hi &= ~(0x1 << 3);
    val_lo |= (0x1 << 20);
    val_lo |= (0x1 << 30);
#elif USE_GPIO32_FUNCTION && USE_GPIO33_FUNCTION
    val_lo &= ~(0x1 << 14);
    val_hi &= ~(0x1 << 3);
    val_lo &= ~(0x1 << 7);  
    val_lo &= ~(0x1 << 20);
#endif/*USE_I2C3_FUNCTION*/
#endif/*USE_CAN3_FUNCTION*/
#endif/*USE_LPC_FUNCTION*/


//GMAC2,UART1,UART6,UART7,UART8,GPIO40~53
#ifdef USE_GMAC2_FUNCTION
    val_lo |= (0x1 << 10);
#elif  USE_UART1_ALL_FUNCTION
    val_lo &= ~(0x1 << 10);
    val_hi |= (0x7 << 10);
    val_hi &= ~(0x7 << 24);
#elif  USE_GPIO40_GPIO53_FUNCTION
    val_lo &= ~(0x1 << 10);
    val_hi &= ~(0xf << 10);
#else 
#ifdef USE_UART1_HALF_ALL_FUNCTION
    val_lo &= ~(0x1 << 10);
    val_hi |= (0x1 << 10);
    val_hi &= ~(0x1 << 26);
#else
#ifdef USE_UART1_FUNCTION 
    val_lo &= ~(0x1 << 10);
    val_hi |= (0x1 << 10);
#endif/*USE_UART1_FUNCTION*/
#ifdef USE_UART8_FUNCTION
    val_lo &= ~(0x1 << 10);
    val_hi |= (0x1 << 13);
    val_hi |= (0x1 << 26);
#endif/*USE_UART8_FUNCTION*/
#endif/*USE_UART1_HALF_ALL_FUNCTION*/
#ifdef USE_UART6_HALF_ALL_FUNCTION
    val_lo &= ~(0x1 << 10);
    val_hi |= (0x1 << 11);
    val_hi |= (0x1 << 24);
    val_hi &= ~(0x1 << 25);
#else
#ifdef  USE_UART6_FUNCTION
    val_lo &= ~(0x1 << 10);
    val_hi |= (0x1 << 11);
    val_hi |= (0x1 << 24);
#endif/*USE_UART6_FUNCTION*/
#ifdef USE_UART7_FUNCTION
    val_lo &= ~(0x1 << 10);
    val_hi |= (0x1 << 12);
    val_hi |= (0x1 << 25);
#endif/*USE_UART7_FUNCTION*/
#endif/*USE_UART6_HALF_ALL_FUNCTION*/
#endif/*USE_GMAC2_FUNCTION*/


//HDA0,I2S,HDA1,GPIO21~GPIO27
#ifdef USE_HDA0_FUNCTION
    val_lo |= (0x1 << 11);
#elif  USE_I2S_FUNCTION
    val_lo |= (0x2 << 11);
#elif  USE_HDA1_FUNCTION
    val_lo |= (0x3 << 11);
#elif  USE_GPIO21_GPIO27_FUNCTION
    val_lo &= ~(0x3 << 11); 
#endif/*USE_HDA0_FUNCTION*/


//UART0,UART3,UART4,UART5,AVS
#ifdef USE_UART0_ALL_FUNCTION
    val_lo &= ~(0x1 << 23);
    val_hi &= ~(0x7 << 20);
#else
#ifdef  USE_UART0_HALF_ALL_FUNCTION
    val_lo &= ~(0x1 << 23);
    val_hi |= (0x1 << 19);
    val_hi &= ~(0x1 << 22);
#else
#ifdef USE_UART0_FUNCTION
    val_lo &= ~(0x1 << 23);
    val_hi |= (0x1 << 19);
#endif/*USE_UART0_FUNCTION*/
#ifdef USE_UART5_FUNCTION
    val_lo &= ~(0x1 << 23);
    val_hi |= (0x1 << 22);
#endif/*USE_UART5_FUNCTION*/
#endif/*USE_UART0_HALF_ALL_FUNCTION*/
#ifdef USE_UART_HALF_UART3_FUNCTION
    val_lo &= ~(0x1 << 23);
    val_hi |= (0x1 << 20);
    val_hi &= ~(0x1 << 21);
#elif USE_AVS_FUNCTION
    val_lo |= (0x1 << 23);
    val_hi &= ~(0x3 << 6);
#else
#ifdef USE_UART3_FUNCTION
    val_lo &= ~(0x1 << 23);
    val_hi |= (0x1 << 20);
#endif/*USE_UART3_FUNCTION*/
#ifdef USE_UART4_FUNCTION
    val_lo &= ~(0x1 << 23);
    val_hi |= (0x1 << 21);
#endif/*USE_UART4_FUNCTION*/
#endif/*USE_UART_HALF_UART3_FUNCTION*/
#endif/*USE_UART0_ALL_FUNCTION*/


//EMMC,GPIO10~GPIO20
#ifdef USE_EMMC_FUNCTION
    val_lo |= (0x1 << 24);
#elif  USE_GPIO10_GPIO20_FUNCTION
    val_lo &= ~(0x1 << 24);
#endif/*USE_EMMC_FUNCTION*/


//SDIO,SPI1,GPIO54~GPIO59    
#ifdef USE_SDIO_FUNCTION
    val_lo |= (0x1 << 25); 
#elif USE_SPI1_FUNCTION
    val_lo &= ~(0x1 << 25); 
    val_lo |= (0x1 << 27);
#elif USE_GPIO54_GPIO59_FUNCTION
    val_lo &= ~(0x1 << 25);
    val_lo &= ~(0x1 << 27);
#endif/*USE_SDIO_FUNCTION*/

//GMACPHY1_LED, GPIO60~GPIO62
#ifdef USE_GMAC1_LED_FUNCTION
	val_lo |= (0x1 << 9);
#elif USE_GPIO60_GPIO62_FUNCTION
	val_lo &= ~(0x1 << 9);
#endif

// SATA_LEDn, GPIO63
#ifdef USE_SATA_LEDn_FUNCTION
	val_lo |= (0x1 << 13);
#elif USE_GPIO63_FUNCTION
	val_lo &= ~(0x1 << 13);
#endif

//LIO,DVO,GPIONODE0~GPIONODE31
#ifdef USE_LIO_FUNCTION
    val_hi |= (0x1 << 14);
    val_hi &= (0x2 << 14);
    val_hi |= (0x7 << 16);
#elif USE_DVO_FUNCTION
    val_hi &= ~(0x1 << 14);
    val_hi |= (0x2 << 14);
#elif USE_GPIO_NODE_FUNCTION
    val_hi &= ~(0x3 << 14);
#endif/*USE_LIO_FUNCTION*/
    
    writel(val_lo, LS2K_MUX_REG);
    writel(val_hi, LS2K_MUX_REG + 0x4);
} 

#define LS2K_DPM_BASE        (LS2K_ACPI_REG_BASE + (1 << 12))
#define LS2K_DPM_EN    		 (LS2K_DPM_BASE + 0x0)
#define LS2K_DPM_PWRUP_SEL   (LS2K_DPM_BASE + 0x4)
#define LS2K_DPM_TGT   		 (LS2K_DPM_BASE + 0X8)
#define LS2K_DPM_STS   		 (LS2K_DPM_BASE + 0xc)
#define LS2K_DPM_WAIT_TIME0		(LS2K_DPM_BASE + 0x10)
#define LS2K_DPM_WAIT_TIME1	    (LS2K_DPM_BASE + 0x14)
#define LS2K_DPM_WAIT_TIME2     (LS2K_DPM_BASE + 0X18)
#define LS2K_DPM_WAIT_TIME3     (LS2K_DPM_BASE + 0X1c)

#define USE_CLOSE_CORE0_CLOCK         0
#define USE_CLOSE_CORE0_CLOCK_POWER   0
  
#define USE_CLOSE_CORE1_CLOCK         0
#define USE_CLOSE_CORE1_CLOCK_POWER   0

#define USE_CLOSE_GPU_CLOCK           0
#define USE_CLOSE_GPU_CLOCK_POWER     0

#define USE_CLOSE_RIO0_CLOCK          0  
#define USE_CLOSE_RIO0_CLOCK_POWER    0

#define USE_CLOSE_RIO1_CLOCK          0 
#define USE_CLOSE_RIO1_CLOCK_POWER    0

#define USE_CLOSE_PCIE2_CLOCK         0
#define USE_CLOSE_PCIE2_CLOCK_POWER   0
 
static void ls2k2000_dpm_ctrl()
{
    unsigned int val;
    unsigned int val_sts, val_tgt;
    unsigned int regval;
    
    val = readq(LS2K_DPM_EN);

    if (USE_CLOSE_CORE0_CLOCK || USE_CLOSE_CORE0_CLOCK_POWER) {
        val |= (0x1 << 0);
        writeq(val, LS2K_DPM_EN);
	
        regval = readq(LS2K_DPM_PWRUP_SEL) | (0x1 << 0);
     	writeq(regval, LS2K_DPM_PWRUP_SEL);
	
        regval = readq(LS2K_DPM_WAIT_TIME0) &  (~(0xff << 0));
        regval |= 0x33;
        writeq(regval, LS2K_DPM_WAIT_TIME0);
        if (USE_CLOSE_CORE0_CLOCK) {
            regval = readq(LS2K_DPM_TGT) & (~(0x3));
	    regval |= 0x1;
	    writeq(regval, LS2K_DPM_TGT);
        } else if (USE_CLOSE_CORE0_CLOCK_POWER) {
	    regval = readq(LS2K_DPM_TGT) & ~(0x3 << 0);
	    regval |= (0x3 << 0);
	    writeq(regval, LS2K_DPM_TGT);
        }

	val_sts = readq(LS2K_DPM_STS) & (0x3 << 0);
       	val_tgt = readq(LS2K_DPM_TGT) & (0x3 << 0);
       	if(val_sts == val_tgt)
	    printf("DPM close core0  success\r\n");
	else
	    printf("DPM close core0  failed!\r\n");
    }

    if (USE_CLOSE_CORE1_CLOCK || USE_CLOSE_CORE1_CLOCK_POWER) {
	val |= (0x1 << 1);
	writel(val, LS2K_DPM_EN);

        regval = readq(LS2K_DPM_PWRUP_SEL) | (0x1 << 1);
        writeq(regval, LS2K_DPM_PWRUP_SEL);

	regval = readq(LS2K_DPM_WAIT_TIME0) & ~(0xff << 8);
	regval |= (0x33 << 8);
	writeq(regval, LS2K_DPM_WAIT_TIME0);

        if (USE_CLOSE_CORE1_CLOCK) {
	    regval = readq(LS2K_DPM_TGT) & ~(0x3 << 2);
	    regval |= (0x1 << 2);
	    writeq(regval, LS2K_DPM_TGT);
        } else if (USE_CLOSE_CORE1_CLOCK_POWER)  {
	    regval = readq(LS2K_DPM_TGT) & ~(0x3 << 2);
	    regval |= (0x3 << 2);
	    writeq(regval, LS2K_DPM_TGT);
        }
  
	val_sts = readq(LS2K_DPM_STS) & (0x3 << 2);
	val_tgt = readq(LS2K_DPM_TGT) & (0x3 << 2);

	if(val_sts == val_tgt)
            printf("DPM close core1 success\r\n");
	else
	    printf("DPM close core1 failed!\r\n");
    }

    if (USE_CLOSE_GPU_CLOCK || USE_CLOSE_GPU_CLOCK_POWER) {
	val |= (0x1 << 2);
        writeq(val, LS2K_DPM_EN);
    
	regval = readq(LS2K_DPM_PWRUP_SEL) | (0x1 << 2);
	writeq(regval, LS2K_DPM_PWRUP_SEL);
	
	regval = readq(LS2K_DPM_WAIT_TIME0) & (~(0xff << 16));
        regval |= (0x33 << 16);
	writeq(regval, LS2K_DPM_WAIT_TIME0);

        if (USE_CLOSE_GPU_CLOCK) {
	    regval = readq(LS2K_DPM_TGT) & (~(0x3 << 4));
	    regval |= (0x1 << 4);
	    writeq(regval, LS2K_DPM_TGT);
        } else if (USE_CLOSE_GPU_CLOCK_POWER) {
	    regval = readq(LS2K_DPM_TGT) & (~(0x3 << 4));
	    regval |= (0x3 << 4);
	    writeq(regval, LS2K_DPM_TGT);
        }
	val_sts = readq(LS2K_DPM_STS) & (0x3 << 4);
	val_tgt = readq(LS2K_DPM_TGT) & (0x3 << 4);
	if(val_sts == val_tgt)
	    printf("DPM close GPU success\r\n");
	else
           printf("DPM close GPU failed!\r\n");
    }

    if (USE_CLOSE_RIO0_CLOCK || USE_CLOSE_RIO0_CLOCK_POWER) {
	val |= (0x1 << 3);
	writel(val, LS2K_DPM_EN);

	regval = readq(LS2K_DPM_PWRUP_SEL) | (0x1 << 3);
	writeq(regval, LS2K_DPM_PWRUP_SEL);
	
	regval = readq(LS2K_DPM_WAIT_TIME0) & ~(0xff << 24);
	regval |= (0x33 << 24);
	writeq(regval, LS2K_DPM_WAIT_TIME0);
        if  (USE_CLOSE_RIO0_CLOCK) {
	    regval = readq(LS2K_DPM_TGT) & ~(0x3 << 6);
	    regval |= (0x1 << 6);
	    writeq(regval, LS2K_DPM_TGT);
        } else if (USE_CLOSE_RIO0_CLOCK_POWER) {
	    regval = readq(LS2K_DPM_TGT) & ~(0x3 << 6);
	    regval |= (0x3 << 6);
	    writeq(regval, LS2K_DPM_TGT);
        }
	val_sts = readq(LS2K_DPM_STS) & (0x3 << 6);
	val_tgt = readq(LS2K_DPM_TGT) & (0x3 << 6);
	if(val_sts == val_tgt)
	    printf("DPM close rio0 success\r\n");
	else
            printf("DPM close rio0 failed!\r\n");
    }

    if (USE_CLOSE_RIO1_CLOCK ||  USE_CLOSE_RIO1_CLOCK_POWER) {
	val |= (0x1 << 4);
	writel(val, LS2K_DPM_EN);

	regval = readq(LS2K_DPM_PWRUP_SEL) | (0x1 << 4);
	writeq(regval, LS2K_DPM_PWRUP_SEL);
	
	regval = readq(LS2K_DPM_WAIT_TIME1) & ~(0xff << 0);
	regval |= (0x33 << 0);
	writeq(regval, LS2K_DPM_WAIT_TIME1);
        if (USE_CLOSE_RIO1_CLOCK) {
	    regval = readq(LS2K_DPM_TGT) & ~(0x3 << 8);
	    regval |= (0x1 << 8);
	    writeq(regval, LS2K_DPM_TGT);
        } else if (USE_CLOSE_RIO1_CLOCK_POWER) {
	    regval = readq(LS2K_DPM_TGT) & ~(0x3 << 8);
	    regval |= (0x3 << 8);
	    writeq(regval, LS2K_DPM_TGT);
        }
	    val_sts = readq(LS2K_DPM_STS) & (0x3 << 8);
	    val_tgt = readq(LS2K_DPM_TGT) & (0x3 << 8);
	if(val_sts == val_tgt)
            printf("DPM close rio1 success\r\n");
	else
            printf("DPM close rio1 clock failed!\r\n");
    }

    if (USE_CLOSE_PCIE2_CLOCK || USE_CLOSE_PCIE2_CLOCK_POWER) {
	val |= (0x1 << 5);
	writel(val, LS2K_DPM_EN);

	regval = readq(LS2K_DPM_PWRUP_SEL) | (0x1 << 5);
	writeq(regval, LS2K_DPM_PWRUP_SEL);
	
	regval = readq(LS2K_DPM_WAIT_TIME1) & ~(0xff << 8);
	regval |= (0x33 << 8);
	writeq(regval, LS2K_DPM_WAIT_TIME1);
        if (USE_CLOSE_PCIE2_CLOCK) {
	    regval = readq(LS2K_DPM_TGT) & ~(0x3 << 10);
	    regval |= (0x1 << 10);
	    writeq(regval, LS2K_DPM_TGT);
        } else if (USE_CLOSE_PCIE2_CLOCK_POWER) {
	    regval = readq(LS2K_DPM_TGT) & ~(0x3 << 10);
	    regval |= (0x3 << 10);
	    writeq(regval, LS2K_DPM_TGT);
        }
	val_sts = readq(LS2K_DPM_STS) & (0x3 << 10);
	val_tgt = readq(LS2K_DPM_TGT) & (0x3 << 10);
	if(val_sts == val_tgt)
            printf("DPM close pcie2 success\r\n");
	else
            printf("DPM close pcie2 failed!\r\n");
    }
}

void ls2k2000_init_before()
{
    unsigned int val_lo;
    unsigned int val_hi;
	unsigned int val;
    
	/*disable spi bulk read*/
    readq(0x800000001fe02400) =  0x1c000000UL;
    readq(0x800000001fe02440) = 0xffffffffff000000UL;
    readq(0x800000001fe02480) = 0x1c0000d4UL;

    readq(0x800000001fe02500) =  0x1c000000UL;
    readq(0x800000001fe02540) = 0xffffffffff000000UL;
    readq(0x800000001fe02580) = 0x1c0000d4UL;
    
    ls2k2000_mux_option();


#ifdef LS2K2000_PAI_OPTION
/*
 * Init for acpi reboot
 */
readq(0x80000000100d0038) = 0x1000;
readq(0x80000000100d0030) = 0x2;
readq(0x80000000100d0034) = 0x1; 
readq(0x80000000100d0030) = 0x0; 
#endif

#ifdef LS2K2000_NUC_OPTION
/*
 * PWM0 for fan
 */
    readl(LS2K_PWM0_CTRL) &= ~1;
    writel(0, LS2K_PWM0_LOW);
    writel(10000, LS2K_PWM0_FULL);
    readl(LS2K_PWM0_CTRL) |= 1;

/*
 * PWM2 for BEEP
 */
    readl(LS2K_PWM2_CTRL) &= ~1;
    writel(10000, LS2K_PWM2_LOW);
    writel(20000, LS2K_PWM2_FULL);
    readl(LS2K_PWM2_CTRL) |= 1;
    mdelay(500);
    readl(LS2K_PWM2_CTRL) &= ~1;

#endif

#ifdef LS2K2000_B2K_OPTION
    /*
     * gpio4 for light
     */
    set_gpio_direction(LS2K_GPIO_OEN_REG,4,0,0);
    set_gpio_val(LS2K_GPIO_O_REG,4,1,0);
    /*PWM3,PWM4,PWM5 for dvo*/
    readl(LS2K_PWM5_CTRL) &= ~0x1;
    writel(250000, LS2K_PWM5_FULL);
    writel(0,LS2K_PWM5_LOW);
    readl(LS2K_PWM5_CTRL) |= 0x1;

    readl(LS2K_PWM4_CTRL) &= ~0x1;
    writel(250000, LS2K_PWM4_FULL);
    writel(0,LS2K_PWM4_LOW);
    readl(LS2K_PWM4_CTRL) |= 0x1;

    readl(LS2K_PWM3_CTRL) &= ~0x1;
    writel(250000, LS2K_PWM3_FULL);
    writel(50000,LS2K_PWM3_LOW);
    readl(LS2K_PWM3_CTRL) |= 0x1; 
#endif 

#ifdef LS2K2000_Q2K201Z0_OPTION
/* set gpio1 for sata */
    set_gpio_direction(LS2K_GPIO_OEN_REG,1,0,0);
    set_gpio_val(LS2K_GPIO_O_REG,1,0,0);
#endif

#ifdef LS2K2000_Q2K20C0_OPTION

/* SET GPIO0 OUTPUT HIGH FOR BEEP */
    set_gpio_direction(LS2K_GPIO_OEN_REG,BEEP_GPIO,0,0);
    set_gpio_val(LS2K_GPIO_O_REG,BEEP_GPIO,1,0);
    mdelay(500);
    set_gpio_val(LS2K_GPIO_O_REG,BEEP_GPIO,0,0);

    #ifdef CLOSE_WDT
	readl(LS2K_MUX_REG) |= (0x1 << 2);
    #endif

#endif 
#ifdef USE_GMAC2_FUNC
        val = readl(PHYS_TO_UNCACHED(0x10010440));
        val |= (1 << 10);
        writel(val, PHYS_TO_UNCACHED(0x10010440));
#endif
}

void ls2k2000_init_after(void)
{

#if  NM25P80
    printf("m25p80 probe.\n");
    ls_m25p_probe();

#endif

#if NSPINAND_LLD
    printf("spinand probe.\n");
    ls2k_spi_nand_probe();
#endif
	
    ls2k2000_dpm_ctrl();

}



