/*
/*
 * Copyright (c) 2020 <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>@loongson.cn)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */

#ifndef _KERNEL
#define _KERNEL
#endif

#include <platform.h>
#include <asm.h>
#include <regnum.h>
#include <cpu.h>
#include <pte.h>

#include "pmon/dev/ns16550.h"
#include "target/bonito.h"

#include "loongson2_def.h"

LEAF(clear_mailbox)
	csrrd   t0, 0x20
	andi    t1, t0, 0x3	/* core id */
	slli.d	t1, t1, 8
	/* get node id shift offset */
	li.d	t2, CORES_PER_NODE
	ctz.w	t2, t2
	li.d	t3, NODE_OFFSET
	sub.d	t2, t3, t2

	li.d	t3, ~(CORES_PER_NODE - 1)
	andi    t3, t3, 0x1ff
	and     t3, t0, t3	/* node id */
	sll.d   t2, t3, t2

	or      t1, t2, t1
	li.d    t2, NODE0_CORE0_BUF0
	or      t1, t1, t2
	st.d    zero, t1, FN_OFF
	st.d    zero, t1, SP_OFF
	st.d    zero, t1, GP_OFF
	st.d    zero, t1, A1_OFF

	jirl zero, ra, 0x0
END(clear_mailbox)

LEAF(get_core_id)
       csrrd   a0, 0x20
       andi    a0, a0, 0x1ff
       jirl    zero, ra, 0x0
END(get_core_id)

slave_main:
	.globl slave_main
#ifdef ACPI_SUPPORT
	bl	clear_mailbox

	bl	get_core_id	//get a0
	/*
	 * don't changing the following register
	 * a0, ap own cpu number set by get_core_id
	 * t2, node 0 mail box address set by clear_mailbox
	 */
1:
	li.w	t0, 0x1000

2:
	addi.w  t0, t0, -1
	bnez    t0, 2b

	ld.w    t0, t1, FN_OFF  //mailbox0
	beqz    t0, 1b
	ld.d    t0, t1, FN_OFF

	move    ra, t0

	ld.d    a1, t1, A1_OFF  //mailbox3

	li.d	t3, PHYS_TO_CACHED(0x84000000)

	slli.d  a2, a0, 0x9
	or      t0, a2, t3
	move	sp,	t0
	addi.d  sp, sp, -8

	jirl    zero, ra, 0x0		# jump to initlialize AP info function with "a0=ap cpu number"

	.globl asm_wait_for_kernel
asm_wait_for_kernel:
#endif
	bl      clear_mailbox
	/*
	 * don't changing the following register
	 * t1, each node mail box address
	 */

waitforinit:
	li.w    t0, 0x1000

idle1000:
	addi.w  t0, t0, -1
	bnez    t0, idle1000
	/*csr finally filled the low 32 bits*/
	ld.w    t0, t1, FN_OFF
	beqz    t0, waitforinit

	ld.d    t0, t1, FN_OFF
	li.d    t2, CACHED_MEMORY_ADDR
	or      t0, t0, t2
	move    ra, t0

	li.d    t3, CACHED_MEMORY_ADDR

	ld.d    t0, t1, SP_OFF
	or      t0, t0, t3
	move    sp, t0

	ld.d    t0, t1, GP_OFF
	or      t0, t0, t3
	move    gp, t0

#ifdef	ACPI_SUPPORT
	ld.d    a1, t1, A1_OFF
	or	a1, a1, t3
#endif
	/* slave core jump to kernel, byebye */
	move    t0, ra
	bl	1f
1:
	addi.d	ra, ra, 0x8
	jirl    zero, t0, 0x0
	b     	slave_main
	/* end slave_main */


LEAF(get_cpuprid)
	cpucfg	a0, zero
	jirl    zero, ra, 0
END(get_cpuprid)

LEAF(ls7a_version)
	li.d	a0, PHYS_TO_UNCACHED(PCIE_CONF_BASE | 0x100)
	ld.bu	a0, a0, 0x8
	jirl	zero, ra, 0
END(ls7a_version)
LEAF(ls7a_perf_enh)
	li.d    a0, PHYS_TO_UNCACHED(0xe0010013ff8)
	ld.w    a1, a0, 0x0
	li.d    a0, 0
	li.d    a2, 0x7A120001
	bne     a1, a2, 1f
	li.d    a0, LS7A_PERF_ENH
1:
	jirl    zero, ra, 0
END(ls7a_perf_enh)
