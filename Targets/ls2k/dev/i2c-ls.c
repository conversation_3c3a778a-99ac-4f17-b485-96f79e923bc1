
 /*
  * This file is for I2C test
  * Author: <PERSON> shuai
  */
#include <sys/linux/types.h>
#include <pmon.h>
#include <stdio.h>
#include <stdarg.h>
#include <machine/pio.h>
#include "target/ls2k.h"
#include <string.h>

#define PRER_LO_REG	0x0
#define PRER_HI_REG	0x1
#define CTR_REG    	0x2
#define TXR_REG    	0x3
#define RXR_REG    	0x3
#define CR_REG     	0x4
#define SR_REG     	0x4
#define SLV_CTRL_REG  0x7

//#define I2C_DEBUG
#ifdef  I2C_DEBUG
#define i2c_debug 	printf
#else 
#define i2c_debug(fmt, args...)
#endif


#define write_reg(reg, val)	outb((i2c_base + (reg)), (val))
#define read_reg(reg)				inb((i2c_base + (reg)))
unsigned long long base_addr[] = {
	PHYS_TO_UNCACHED(0x1fe00120),
	PHYS_TO_UNCACHED(0x1fe00130),
	LS2K_I2C2_REG_BASE,
	LS2K_I2C3_REG_BASE
};

unsigned long long i2c_base;

static void i2c_stop(void)
{
again:
	write_reg(CR_REG, CR_STOP);
	read_reg(SR_REG);
	while (read_reg(SR_REG) & SR_BUSY)
		goto again;
}

static int i2c_tx_byte(unsigned char data, unsigned char opt)
{
	int times = 1000000;

	write_reg(TXR_REG, data);
	write_reg(CR_REG, opt);
	i2c_debug("-----func:%s------line:%d\n", __func__, __LINE__);
	while ((read_reg(SR_REG) & SR_TIP) && times--) ;	
	if (times < 0) {
		printf("ls_i2c_tx_byte SR_TIP can not ready!\n");
		i2c_stop();
		return -1;
	}

	i2c_debug("-----func:%s------line:%d\n", __func__, __LINE__);

	if (read_reg(SR_REG) & SR_NOACK) {
		i2c_debug("Slave dev has no ack, Pls check the hardware!\n");
		i2c_stop();
		return -1;
	}
	i2c_debug("-----func:%s------line:%d\n", __func__, __LINE__);

	return 0;
}

int  ls2k_i2c_write(unsigned char dev_addr, unsigned char reg_addr, unsigned char *buf, int count){
    	int i;

    	if (i2c_tx_byte(dev_addr << 1, CR_START | CR_WRITE) < 0)
	    return 0;

	if (i2c_tx_byte(reg_addr & 0xff, CR_WRITE) < 0)
	    return 0;

	for (i = 0; i < count; i++) {
	    if (i2c_tx_byte(buf[i] & 0xff, CR_WRITE) < 0) {
	        return 0;
	    }
	}

	i2c_stop();

	for (i = 0; i < count; i++)
	    i2c_debug("0x%x <= 0x%x \n", reg_addr + i, buf[i]);
	return 1;
}

int ls2k_i2c_read(unsigned char  dev_addr, unsigned char reg_addr, unsigned char *buf, int count){
	int i;
	
    	if (i2c_tx_byte((dev_addr << 1), CR_START | CR_WRITE) < 0)
	    return 0;

	if (i2c_tx_byte(reg_addr & 0xff, CR_WRITE) < 0)
	    return 0;

	if (i2c_tx_byte(((dev_addr << 1 )| 0x1), CR_START | CR_WRITE) < 0)
	    return 0;

	for (i = 0; i < count; i++) {
	    write_reg(CR_REG, ((i == count - 1) ? (CR_READ | CR_ACK) : CR_READ));
	    while (read_reg(SR_REG) & SR_TIP) ;
	    buf[i] = read_reg(RXR_REG);
	}

	i2c_stop();

	return 1;

}


int  ls2k_i2c_init_host(int busno, int scl_clk){
        
	unsigned char prer_h, prer_l;

	i2c_base = base_addr[busno];

	
	prer_l = (unsigned char)scl_clk;
	prer_h = (unsigned char)((scl_clk >> 8) && 0xff);

	i2c_debug("prer_l:0x%x\n", prer_l);
	i2c_debug("prer_h:0x%x\n", prer_h);

	read_reg(CTR_REG) &= ~0x80;
	write_reg(PRER_LO_REG, prer_l);
	write_reg(PRER_HI_REG, prer_h);
	read_reg(CTR_REG) |= 0x80;

	return 0;
}

int ls2k_i2c_init_slave(unsigned char slave_dev_addr)
{
    	write_reg(CTR_REG, 0xc0);
	write_reg(SLV_CTRL_REG, slave_dev_addr | 0x80);

	i2c_debug("slave_base_addr=%x\n", slave_dev_addr);
    	return 0;
}

static int cmd_i2c_init_host(int argc,char **argv)
{

	unsigned int bus, i;
       unsigned int scl_clk;

	if(argc<3){
	    i2c_debug("err: please use correct cmd format, eg.i2c_init_host 0 0x271\n");
	    return 0;
	}

	i2c_debug("argc =%d\n", argc);

	bus=strtoul(argv[1],0,0);

	i2c_base = base_addr[bus];

	i2c_debug("i2c_base=%llx\n", i2c_base);

        scl_clk = strtoul(argv[2],0,0);
	
	if(bus < 2){
    	    scl_clk = 74800000/ (4 * scl_clk) - 1;
	}
	else{
	    scl_clk = 37400000 / (4 * scl_clk) - 1;
	}
	
	i2c_debug("clk_s=%d\n",scl_clk);
	
	if(bus == 2)
		read_reg(SLV_CTRL_REG) &= ~0x80;

	ls2k_i2c_init_host(bus, scl_clk);//0x271
}

static int cmd_i2c_init_slave(int argc,char **argv){
    	int slave_dev_addr;

	if( argc != 2)
	{
	    i2c_debug("err:please use correct cmd format\n");
	    return 0;
	}   

	i2c_base = base_addr[2];
	
	i2c_debug("i2c_base=%llx\n", i2c_base);
	
	slave_dev_addr = strtoul(argv[1],0,0);

	ls2k_i2c_init_slave(slave_dev_addr);
	
	return 0;
}



static int cmd_i2c_write(int argc,char **argv)
{
    	unsigned int bus;
    	unsigned char *s;
    	unsigned char dev_addr, reg_addr;
    	unsigned char buf[32];
    	int i,v,count;

    	if(argc != 5)
    	{
	    	i2c_debug("err:please use correct cmd format\n");
		    return 0;
		}

    	bus = strtoul(argv[1],0,0);
    	i2c_base = base_addr[bus];

    	dev_addr = strtoul(argv[2],0,0);

    	reg_addr = strtoul(argv[3],0,0);

    	s = argv[4];

    	count = strlen(s) / 3 + 1;

    	for (i = 0; i < count; i++) {
	    gethex(&v, s, 2);
	    buf[i] = v;
	    s += 3;
	    printf("buf[i] = 0x%x\n", buf[i]);
	}

	ls2k_i2c_write(dev_addr, reg_addr, buf, count);

	return 0;
}

static int cmd_i2c_read(int argc,char **argv)
{
    	unsigned int bus;
    	unsigned char dev_addr, reg_addr;
    	unsigned char buf[32];
    	int i,v,count;

    	if(argc !=5)
    	{
	    i2c_debug("err:please use correct cmd format\n");
	    return 0;
    	}

    	bus = strtoul(argv[1],0,0);
    	i2c_base = base_addr[bus];

    	dev_addr = strtoul(argv[2],0,0);

    	reg_addr = strtoul(argv[3],0,0);

    	count = strtoul(argv[4], NULL, 0);

    	ls2k_i2c_read(dev_addr, reg_addr, buf, count);

    	for (i = 0; i < count; i++)
				printf("0x%x: 0x%02x \n", reg_addr + i, buf[i]);
}

static const Cmd Cmds[] = {
	{"i2c_cmd"},
  {"i2c_init_host", "bus_num i2c_freq(40000hz)", 0, "Init i2c to host mode", cmd_i2c_init_host, 1, 99, 0},
	{"i2c_init_slave", "bus_num dev_addr", 0, "Init i2c to slave mode", cmd_i2c_init_slave, 1, 99, 0},
  {"i2c_write", "bus_num dev_addr reg_addr data ", 0, "Write date to slave device ", cmd_i2c_write, 1, 99, 0},
  {"i2c_read", "bus_num dev_addr reg_addr count", 0, "Read data from slave device ", cmd_i2c_read, 1, 99, 0},
	{0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));
static void init_cmd()
{
	cmdlist_expand(Cmds, 1);
}
