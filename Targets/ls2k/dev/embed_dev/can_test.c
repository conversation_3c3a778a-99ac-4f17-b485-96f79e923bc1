#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/device.h>
#include <sys/queue.h>
#include <limits.h>
#include <ctype.h>
#include <cpu.h>

#include <pmon.h>
#include <types.h>

/* CONF addr. TODO: move to Bonito.h?*/
#define HT1_CONF_BASE_ADDR  PHYS_TO_UNCACHED(0xfdfe000000)
#define HT1_MEM_BASE_ADDR  UNCACHED_MEMORY_ADDR
#define HEADER_ADDR(X,Y)   (HT1_CONF_BASE_ADDR | (X << 11) | (Y << 8))
#define APB_DEV_HEADER_ADDR     HEADER_ADDR(2, 0)
#define CONFBUS_HEADER_ADDR     HEADER_ADDR(21, 0)
#define PCI_DEV_BASE_ADDR0_OFFSET   0x10
#define PCI_DEV_BASE_ADDR0_OFFSET_h 0x14
#define CONF_SB_OFFSET      0x430
#define CONF_PAD_OFFSET     0x440

#define max(a, b) ((a) > (b) ? (a) : (b))
#define min(a, b) ((a) > (b) ? (b) : (a))
#define clamp(val, lo, hi) min((typeof(val))max(val, lo), hi)
#define CAN_CALC_SYNC_SEG	1

#define REG_MOD			0x00
#define REG_CR			0x00
#define REG_CMR			0x01
#define REG_SR			0x02
#define REG_IR			0x03
#define REG_IER			0x04
#define REG_ACR			0x04
#define REG_AMR			0x05
#define REG_FFDEP		0x09
#define REG_BITNO		0x0B
#define REG_ECC			0x0C
#define REG_EMLR		0x0D
#define REG_RXERR		0x0E
#define REG_TXERR		0x0F
#define REG_ACCC0		0x10
#define REG_ACCC1		0x11
#define REG_ACCC2		0x12
#define REG_ACCC3		0x13
#define REG_ACCM0		0x14
#define REG_ACCM1		0x15
#define REG_ACCM2		0x16
#define REG_ACCM3		0x17
#define REG_RMCR		0x1D
#define REG_FRAC		0x1E
#define REG_INTL		0x1F
#define REG_INTH		0x20

#define REG_BTR0		0x06
#define REG_BTR1		0x07
#define REG_OCR			0x08

#define CAN_SM_FI_RTR	0x10
#define CAN_EM_FI_EFF	0x80
#define CAN_EM_FI_RTR	0x40

#define SM_SFF_TID1		0x0a
#define SM_SFF_TID2		0x0b
#define SM_SFF_TBUF		0x0c
#define SM_SFF_RID1		0x14
#define SM_SFF_RID2		0x15
#define SM_SFF_RBUF		0x16

#define EM_FI			0x10
#define EM_SFF_ID1		0x11
#define EM_SFF_ID2		0x12
#define EM_SFF_BUF		0x13
#define EM_EFF_ID1		0x11
#define EM_EFF_ID2		0x12
#define EM_EFF_ID3		0x13
#define EM_EFF_ID4		0x14
#define EM_EFF_BUF		0x15

#define MODE_RM			0x1
#define MODE_LOM		0x2
#define MODE_STM		0x4
#define MODE_AFM		0x8
#define MODE_SM			0x10
#define MODE_FRC		0x40

#define CMD_TR    0x01
#define CMD_RBB		0x04
#define CMD_SRR		0x10
#define CMD_EFF		0x80

#define INT_RCV		0x01
#define INT_FIFO	0x10

struct can_bittiming_const {
	unsigned int tseg1_min;  /* Time segement 1 = prop_seg + phase_seg1 */
	unsigned int tseg1_max;
	unsigned int tseg2_min;  /* Time segement 2 = phase_seg2 */
	unsigned int tseg2_max;
	unsigned int sjw_max;    /* Synchronisation jump width */
	unsigned int brp_min;    /* Bit-rate prescaler */
	unsigned int brp_max;
	unsigned int brp_inc;
};

struct can_bittiming_reg {
	unsigned char btr0;
	unsigned char btr1;
	unsigned char btrd_frac;
	unsigned char btrd_inter_l;
	unsigned char btrd_inter_h;
};

struct can_cfg {
	unsigned long ref_clk;
	unsigned int bitrate;
	unsigned int real_bitrate;
	unsigned char port_num;
	unsigned char eff;
	unsigned char acr_id[4];
	unsigned char amr_msk[4];
	unsigned char can_mode;
	struct can_bittiming_reg btr;
};

struct can_frame {
	unsigned int can_id;
	unsigned char ide;
	unsigned char rtr;
	unsigned char data_len;
	unsigned char *data;
};

struct can_bittiming_const BTC = {
	.tseg1_min = 1,
	.tseg1_max = 16,
	.tseg2_min = 1,
	.tseg2_max = 8,
	.sjw_max   = 4,
	.brp_min   = 1,
	.brp_max   = 64,
	.brp_inc   = 1,
};

struct can_cfg CFG[6] = {0};
int CFGED[6] = {0};

uint64_t can_base_address;
uint64_t can_base_addrs[6] = {0};
uint64_t confbus_base_address;
uint64_t confsb_base_address;
uint64_t confpad_base_address;

void can_addr_config()
{
	uint64_t confbus_base_address_temp;
	unsigned int temp;
	unsigned int i;
	unsigned int num;
	unsigned int err_flag;

	if (can_base_addrs[0])
		return;

	printf("CAN TEST begin\n");

	can_base_address  = *((volatile unsigned int *)(APB_DEV_HEADER_ADDR + PCI_DEV_BASE_ADDR0_OFFSET));
	can_base_address  = can_base_address | HT1_MEM_BASE_ADDR;
	can_base_address  = can_base_address & 0xfffffffffffffff0;
	can_base_addrs[0] = can_base_address | 0x0000000000001000;
	can_base_addrs[1] = can_base_address | 0x0000000000001100;
	can_base_addrs[2] = can_base_address | 0x0000000000001200;
	can_base_addrs[3] = can_base_address | 0x0000000000001300;
	can_base_addrs[4] = can_base_address | 0x0000000000001400;
	can_base_addrs[5] = can_base_address | 0x0000000000001500;
	printf("can_base_addr : READ can base addr ... Done\n");

	confbus_base_address      = *((volatile unsigned int *)(CONFBUS_HEADER_ADDR + PCI_DEV_BASE_ADDR0_OFFSET));
	confbus_base_address_temp = *((volatile unsigned int *)(CONFBUS_HEADER_ADDR + PCI_DEV_BASE_ADDR0_OFFSET_h));
	confbus_base_address      = confbus_base_address | (confbus_base_address_temp << 32);
	confbus_base_address      = confbus_base_address & 0xfffffffffffffff0;
	confbus_base_address      = confbus_base_address | HT1_MEM_BASE_ADDR;
	confpad_base_address      = confbus_base_address | CONF_PAD_OFFSET;
	confsb_base_address       = confbus_base_address | CONF_SB_OFFSET;
	//set can sel to enable can0/1/2/3  confbus_base_address_temp is used as a temp only
	confbus_base_address_temp = *((volatile unsigned long long*)confpad_base_address);
	confbus_base_address_temp = confbus_base_address_temp | 0x0000003f00000000;
	*((volatile unsigned long long*)confpad_base_address) = confbus_base_address_temp;
	//set pwm sel 0 to enable can4/5 confbus_base_address_temp is used as a temp only
	confbus_base_address_temp = *((volatile unsigned long long*)confpad_base_address);
	confbus_base_address_temp = confbus_base_address_temp & 0xffffffffffffffc0;
	*((volatile unsigned long long*)confpad_base_address) = confbus_base_address_temp;
}

void can_sf_rest()
{
	uint64_t confbus_base_address_temp;
	unsigned int temp;
	//softreset test begin
	*(volatile unsigned char*)(can_base_addrs[0] + 0x6) = 0xf;
	*(volatile unsigned char*)(can_base_addrs[1] + 0x6) = 0xf;
	*(volatile unsigned char*)(can_base_addrs[2] + 0x6) = 0xf;
	*(volatile unsigned char*)(can_base_addrs[3] + 0x6) = 0xf;
	*(volatile unsigned char*)(can_base_addrs[4] + 0x6) = 0xf;
	*(volatile unsigned char*)(can_base_addrs[5] + 0x6) = 0xf;

	temp = *(volatile unsigned char*)(can_base_addrs[0] + 0x6);
	if (temp != 0xf)
			printf("can0 softreset test initial error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[1] + 0x6);
	if (temp != 0xf)
			printf("can1 softreset test initial error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[2] + 0x6);
	if (temp != 0xf)
			printf("can2 softreset test initial error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[3] + 0x6);
	if (temp != 0xf)
			printf("can3 softreset test initial error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[4] + 0x6);
	if (temp != 0xf)
			printf("can4 softreset test initial error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[5] + 0x6);
	if (temp != 0xf)
			printf("can5 softreset test initial error\n");

	//set can softreset, confbus_base_address_temp is used as a temp only;
	confbus_base_address_temp = *((volatile unsigned long long*)confsb_base_address);
	confbus_base_address_temp = confbus_base_address_temp | 0x0100000000000000;
	*((volatile unsigned long long*)confsb_base_address) = confbus_base_address_temp;
	confbus_base_address_temp = confbus_base_address_temp & 0xfeffffffffffffff;
	*((volatile unsigned long long*)confsb_base_address) = confbus_base_address_temp;

	temp = *(volatile unsigned char*)(can_base_addrs[0] + 0x6);
	if (temp != 0x00)
			printf("softreset test  error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[1] + 0x6);
	if (temp != 0x00)
			printf("softreset test  error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[2] + 0x6);
	if (temp != 0x00)
			printf("softreset test  error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[3] + 0x6);
	if (temp != 0x00)
			printf("softreset test  error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[4] + 0x6);
	if (temp != 0x00)
			printf("softreset test  error\n");

	temp = *(volatile unsigned char*)(can_base_addrs[5] + 0x6);
	if (temp != 0x00)
			printf("softreset test  error\n");
}

void can_dump_frame(struct can_frame *frame)
{
	int data_len, i;

	if (frame->ide == 1)
		printf("Extended frame:\n");
	else
		printf("Standard frame:\n");

	printf("Can id is 0x%x.\n", frame->can_id);

	if (frame->rtr == 1) {
		printf("This is a Remote frame.\n");
		return;
	}

	data_len = (int)frame->data_len;
	printf("Data len is %d.\n", data_len);

	for (i = 0; i < data_len; i++) {
		printf("Data[%d] : 0x%x\n", i, frame->data[i]);
	}
}

static int can_update_sample_point(const struct can_bittiming_const *btc,
			  unsigned int sample_point_nominal, unsigned int tseg,
			  unsigned int *tseg1_ptr, unsigned int *tseg2_ptr,
			  unsigned int *sample_point_error_ptr)
{
	unsigned int sample_point_error, best_sample_point_error = UINT_MAX;
	unsigned int sample_point, best_sample_point = 0;
	unsigned int tseg1, tseg2;
	int i;

	for (i = 0; i <= 1; i++) {
		tseg2 = tseg + CAN_CALC_SYNC_SEG - (sample_point_nominal * (tseg + CAN_CALC_SYNC_SEG)) / 1000 - i;
		tseg2 = clamp(tseg2, btc->tseg2_min, btc->tseg2_max);
		tseg1 = tseg - tseg2;
		if (tseg1 > btc->tseg1_max) {
			tseg1 = btc->tseg1_max;
			tseg2 = tseg - tseg1;
		}

		sample_point = 1000 * (tseg + CAN_CALC_SYNC_SEG - tseg2) / (tseg + CAN_CALC_SYNC_SEG);
		sample_point_error = abs(sample_point_nominal - sample_point);

		if ((sample_point <= sample_point_nominal) && (sample_point_error < best_sample_point_error)) {
			best_sample_point = sample_point;
			best_sample_point_error = sample_point_error;
			*tseg1_ptr = tseg1;
			*tseg2_ptr = tseg2;
		}
	}

	if (sample_point_error_ptr)
		*sample_point_error_ptr = best_sample_point_error;

	return best_sample_point;
}

static unsigned int can_bitr_calc(unsigned int target_bitrate, unsigned long ref_clk, struct can_bittiming_reg *btr,
											struct can_bittiming_const *btc, unsigned char btrd_flag)
{
	unsigned int prop_seg, phase_seg1, phase_seg2, sjw;
	unsigned int bitrate;			/* current bitrate */
	unsigned int bitrate_error;		/* difference between current and nominal value */
	unsigned int best_bitrate_error = UINT_MAX;
	unsigned int sample_point_error;	/* difference between current and nominal value */
	unsigned int best_sample_point_error = UINT_MAX;
	unsigned int sample_point_nominal;	/* nominal sample point */
	unsigned int best_tseg = 0;		/* current best value for tseg */
	unsigned int best_brp = 0;		/* current best value for brp */
	unsigned int brp, brp_min, brp_max, tsegall, tseg, tseg1 = 0, tseg2 = 0;
	unsigned int brp_inc = btc->brp_inc;		/* current best value for brp */

	if (target_bitrate > 800000)
		sample_point_nominal = 750;
	else if (target_bitrate > 500000)
		sample_point_nominal = 800;
	else
		sample_point_nominal = 875;

	if (!btrd_flag) {
		/* sja1000 clock frequency divide by 2 */
		ref_clk /= 2;
		brp_min = btc->brp_min;
		brp_max = btc->brp_max;
	} else {
		/* Prescalar can not less than 2, otherwise frequency will not be stable */
		brp_min = 0x200;
		brp_max = btc->brp_max * 0x100;
		ref_clk *= 0x200;
//		brp_inc = 0x10;
	}

	for (tseg = (btc->tseg1_max + btc->tseg2_max) * 2 + 1;
     tseg >= (btc->tseg1_min + btc->tseg2_min) * 2; tseg--) {
		tsegall = CAN_CALC_SYNC_SEG + tseg / 2;

		/* Compute all possible tseg choices (tseg=tseg1+tseg2) */
		brp = ref_clk / (tsegall * target_bitrate) + tseg % 2;

		/* choose brp step which is possible in system */
		brp = (brp / brp_inc) * brp_inc;

		if ((brp < brp_min) || (brp > brp_max))
			continue;

		bitrate = ref_clk / (brp * tsegall);
		bitrate_error = abs(target_bitrate - bitrate);

		/* tseg brp biterror */
		if (bitrate_error > best_bitrate_error)
			continue;

		/* reset sample point error if we have a better bitrate */
		if (bitrate_error < best_bitrate_error)
			best_sample_point_error = UINT_MAX;

		can_update_sample_point(btc, sample_point_nominal, tseg / 2, &tseg1, &tseg2, &sample_point_error);
		if (sample_point_error > best_sample_point_error)
			continue;

		best_sample_point_error = sample_point_error;
		best_bitrate_error = bitrate_error;
		best_tseg = tseg / 2;
		best_brp = brp;

		if (bitrate_error == 0 && sample_point_error == 0)
			break;
	}

	can_update_sample_point(btc, sample_point_nominal, best_tseg, &tseg1, &tseg2, NULL);
	prop_seg = tseg1 / 2;
	phase_seg1 = tseg1 - prop_seg;
	phase_seg2 = tseg2;

	/* bt->sjw is at least 1 -> sanitize upper bound to sjw_max */
	if (sjw > btc->sjw_max)
		sjw = btc->sjw_max;
	/* bt->sjw must not be higher than tseg2 */
	if (tseg2 < sjw)
		sjw = tseg2;

	brp = best_brp;

	printf("brp is 0x%x\n", brp);
	if (!btrd_flag) {
		btr->btr0 = ((brp - 1) & 0x3f) | (((sjw - 1) & 0x3) << 6);
	} else {
		btr->btr0 = (((sjw - 1) & 0x3) << 6);
#if 0
		btr->btrd_inter_h = (brp / 100) >> 8;
		btr->btrd_inter_l = (brp / 100) & 0xff;
		btr->btrd_frac = ((brp % 100) / 0x100) & 0xff;
#else
		btr->btrd_inter_h = brp >> 16;
		btr->btrd_inter_l = (brp >> 8) & 0xff;
		btr->btrd_frac = brp & 0xff;
#endif
		printf("btr->btrd_inter_h is 0x%x, btr->btrd_inter_l is %x, btr->btrd_frac is %x\n", btr->btrd_inter_h, btr->btrd_inter_l, btr->btrd_frac);
	}

	btr->btr1 = ((prop_seg + phase_seg1 - 1) & 0xf) | (((phase_seg2 - 1) & 0x7) << 4);
	if (target_bitrate > 1000000)
		btr->btr1 |= 0x80;

	printf("btr->btr0 is 0x%x, btr->btr1 is 0x%x\n", btr->btr0, btr->btr1);
	/* real bitrate */
	bitrate = ref_clk / (brp * (CAN_CALC_SYNC_SEG + tseg1 + tseg2));

	return bitrate;
}

int _can_parse_data(unsigned char *buf, unsigned char *data)
{
	int data_len = 0;
	unsigned char *nptr = data;
	unsigned char c, num;
	unsigned char digit = 0, tmp = 0, cnt = 0;

	if (*(data + 1) == 'x')
		nptr = data + 2;

	nptr--;
	while ((c = *++nptr) != 0) {

		if (data_len > 8)
			return 8;

		if (isdigit(c))
			digit = c - '0';
		else if(isalpha(c))
			digit = c - (isupper(c) ? 'A' : 'a') + 10;
		else
			break;

		if (digit < 0 || digit >= 16)
			break;

		cnt++;
		if (cnt < 2) {
			num = digit;
		} else {
			num = num << 4;
			num |= digit;
			buf[data_len++] = num;
			num = 0;
			cnt = 0;
		}
	}

	if (cnt == 1 && data_len < 8) {
		buf[data_len++] = num;
	}

	return data_len;
}

void _can_config(struct can_cfg *cfg)
{
	unsigned char port_num, eff, cfg_mode;
	unsigned char cmr = 0, btrd_flag = 0;
	struct can_bittiming_reg *btr = &cfg->btr;
	char *base;
	int i;

	if (!cfg) {
		printf("Invalid arguments in %s.\n", __func__);
		return;
	}

	port_num  = cfg->port_num;
	if (!can_base_addrs[cfg->port_num]) {
		can_addr_config();
	}

	base = can_base_addrs[port_num];
	eff = cfg->eff;
	cfg_mode = cfg->can_mode;

	if (cfg_mode & MODE_FRC) {
		btrd_flag = 1;
	}

//	if ((!cfg->real_bitrate) || (btr->btr0 != readb(base + REG_BTR0)) || (btr->btr1 != readb(base + REG_BTR1))) {
		cfg->real_bitrate = can_bitr_calc(cfg->bitrate, cfg->ref_clk, btr, &BTC, btrd_flag);
		printf("Real bitrate is %d\n", cfg->real_bitrate);
//	}

	writeb(MODE_RM, base + REG_CR);
	if (btrd_flag) {
#if 1
		writeb(btr->btrd_frac, base + REG_FRAC);
		writeb(btr->btrd_inter_l, base + REG_INTL);
		writeb(btr->btrd_inter_h, base + REG_INTH);
#else
		writeb(btr->btrd_inter_h, base + REG_FRAC);
		writeb(btr->btrd_frac, base + REG_INTL);
		writeb(btr->btrd_inter_l, base + REG_INTH);
#endif
		printf("frac is 0x%x, intl is 0x%x, inth is 0x%x\n", readb(base + REG_FRAC), readb(base + REG_INTL), readb(base + REG_INTH));
	}

	writeb(btr->btr0, base + REG_BTR0);
	writeb(btr->btr1, base + REG_BTR1);
#if 0
	if (cfg->bitrate == 800000 && btrd_flag) {
		writeb(0x80, base + REG_FRAC);
		writeb(0, base + REG_INTH);
		writeb(12, base + REG_INTL);
		writeb(0xc0, base + REG_BTR0);
		writeb(0x11, base + REG_BTR1);
		printf("tmp for 800k\n");
	}

	if (cfg->bitrate == 400000 && btrd_flag) {
		writeb(0x80, base + REG_FRAC);
		writeb(0, base + REG_INTH);
		writeb(12, base + REG_INTL);
		writeb(0xc0, base + REG_BTR0);
		writeb(0x25, base + REG_BTR1);
		printf("tmp for 400k\n");
	}
#endif
	if (!eff) {
		writeb(cmr, base + REG_CMR);
		writeb(cfg->acr_id[0], base + REG_ACR);
		writeb(cfg->amr_msk[0], base + REG_AMR);
		if (btrd_flag)
			writeb(0x5e, base + REG_CR);			// Enable IRQ
		else
			writeb(0x3e, base + REG_CR);			// Enable IRQ
	} else {
		cmr |= CMD_EFF;
		writeb(cmr, base + REG_CMR);
		int i;
		for (i = 0; i < 4; i++) {
			writeb(cfg->acr_id[i], base + REG_ACCC0 + i);
			writeb(cfg->amr_msk[i], base + REG_ACCM0 + i);
		}
		writeb(cfg_mode, base + REG_MOD);
		printf("REG_MOD reg val is 0x%x\n", readb(base + REG_MOD));
		writeb(0xff, base + REG_IER);
	}
}

void _can_rx_sm(char *base, struct can_frame *frame)
{
	unsigned char reg_val = 0, i;
	unsigned int can_id = 0;
	if (!base || !frame) {
		printf("Invalid arguments in %s.\n", __func__);
		return;
	}

	can_id |= readb(base + SM_SFF_RID1) << 3;
	reg_val = readb(base + SM_SFF_RID2);
	can_id |= (reg_val & 0xe0) >> 5;

	frame->can_id   = can_id;
	frame->data_len = reg_val & 0xf;

	if (reg_val & CAN_SM_FI_RTR) {
		frame->rtr = 1;
	} else {
		frame->rtr = 0;
		for (i = 0; i < frame->data_len; i++) {
			frame->data[i] = readb(base + SM_SFF_RBUF + i);
		}
	}
}

void _can_rx_em(char *base, struct can_frame *frame)
{
	unsigned char dreg, fi, i;
	unsigned int can_id = 0;
	if (!base || !frame) {
		printf("Invalid arguments in %s.\n", __func__);
		return;
	}

	fi = readb(base + EM_FI);

	if (fi & CAN_EM_FI_EFF) {
		/* extended frame format (EFF) */
		frame->ide = 1;
		dreg = EM_EFF_BUF;
		can_id = (readb(base + EM_EFF_ID1) << 21)
				| (readb(base + EM_EFF_ID2) << 13)
				| (readb(base + EM_EFF_ID3) << 5)
				| (readb(base + EM_EFF_ID4) >> 3);
	} else {
		/* standard frame format (SFF) */
		frame->ide = 0;
		dreg = EM_SFF_BUF;
		can_id = (readb(base + EM_SFF_ID1) << 3)
				| (readb(base + EM_SFF_ID2) >> 5);
	}

	frame->data_len = fi & 0xf;
	frame->can_id = can_id;

	if (fi & CAN_EM_FI_RTR) {
		frame->rtr = 1;
	} else {
		frame->rtr = 0;
		for (i = 0; i < frame->data_len; i++) {
			frame->data[i] = readb(base + dreg + i);
		}
	}
}

void _can_tx_sm(char *base, struct can_frame *frame)
{
	unsigned char fi, i;
	unsigned int can_id = 0;
	if (!base || !frame) {
		printf("Invalid arguments in %s.\n", __func__);
		return;
	}

	can_id = frame->can_id;

	fi = (can_id & 0x7) << 5;
	fi |= frame->data_len;
	if (frame->rtr)
		fi |= CAN_SM_FI_RTR;

	writeb((can_id & 0x7f8) >> 3, base + SM_SFF_TID1);
	writeb(fi, base + SM_SFF_TID2);

	for (i = 0; i < frame->data_len; i++) {
		writeb(frame->data[i], base + SM_SFF_TBUF + i);
	}
}

void _can_tx_em(char *base, struct can_frame *frame)
{
	unsigned char dreg, fi, data_len, i;
	unsigned int can_id = 0;
	if (!base || !frame) {
		printf("Invalid arguments in %s.\n", __func__);
		return;
	}

	fi = data_len = frame->data_len;
	can_id = frame->can_id;

	if (frame->rtr)
		fi |= CAN_EM_FI_RTR;

	if (frame->ide) {
		/* extended frame format (EFF) */
		fi |= CAN_EM_FI_EFF;
		dreg = EM_EFF_BUF;
		writeb(fi, base + EM_FI);
		writeb((can_id & 0x1fe00000) >> 21, base + EM_EFF_ID1);
		writeb((can_id & 0x001fe000) >> 13, base + EM_EFF_ID2);
		writeb((can_id & 0x00001fe0) >> 5, base + EM_EFF_ID3);
		writeb((can_id & 0x0000001f) << 3, base + EM_EFF_ID4);
	} else {
		/* standard frame format (SFF) */
		writeb(fi, base + EM_FI);
		dreg = EM_SFF_BUF;
		writeb((can_id & 0x000007f8) >> 3, base + EM_SFF_ID1);
		writeb((can_id & 0x00000007) << 5, base + EM_SFF_ID2);
	}

	for (i = 0; i < frame->data_len; i++) {
		writeb(frame->data[i], base + dreg + i);
	}
}

void _can_rx(struct can_cfg *cfg, unsigned char depth)
{
	unsigned char reg_val, acr_id, sig;
	char *base;
	struct can_frame frame = {0};
	unsigned char buf[8] = {0};
	int cnt = 0;

	if (!cfg) {
		printf("Invalid arguments in %s.\n", __func__);
		return;
	}

	base = can_base_addrs[cfg->port_num];
	frame.data = &buf[0];

	/* choose interrupt mode */
	if (depth) {
		sig = INT_FIFO;
		writeb(depth, base + REG_FFDEP);
	} else {
		sig = INT_RCV;
	}

	/*  clear interrupt ? */
	reg_val = readb(base + REG_IR);

	/* wait for interrupt signal */
	do {
		if (cnt++ == 10000) {
			printf(".");
			cnt = 0;
		}
		reg_val = readb(base + REG_IR);
	} while(!(reg_val & sig));

	if (!cfg->eff) {
		_can_rx_sm(base, &frame);
	} else {
		_can_rx_em(base, &frame);
	}

	/* release receive buffer */
	writeb(CMD_RBB, base + REG_CMR);

	printf("CAN%d receive a frame:\n", cfg->port_num);
	can_dump_frame(&frame);
	printf("\n");
}

void _can_tx(struct can_cfg *cfg, unsigned char frame_type,
			unsigned int can_id, char *data, int data_len)
{
	struct can_frame frame = {0};
	unsigned char buf[8] = {0};
	char *base;

	if (!cfg || !data) {
		printf("Invalid arguments in %s.\n", __func__);
		return;
	}

	memcpy(buf, data, 8);
	base = can_base_addrs[cfg->port_num];
	frame.data = &buf[0];
	frame.can_id = can_id;
	frame.data_len = data_len;
	if (frame.data_len)
		frame.rtr = 0;
	else
		frame.rtr = 1;

	if (!cfg->eff) {
		_can_tx_sm(base, &frame);
	} else {
		if (frame_type)
			frame.ide = 1;
		else
			frame.ide = 0;
		_can_tx_em(base, &frame);
	}

	/* send buffer */
	if (cfg->can_mode & MODE_STM)
		writeb(CMD_SRR, base + REG_CMR);
	delay(1000);
	writeb(CMD_TR, base + REG_CMR);

	printf("CAN%d send a frame:\n", cfg->port_num);
	can_dump_frame(&frame);
	printf("\n");
}

void _can_dump_reg(struct can_cfg *cfg)
{
	if (!cfg) {
		printf("Invalid arguments in %s.\n", __func__);
		return;
	}

	unsigned int port_num = cfg->port_num;
	char *base = can_base_addrs[port_num];
	int i;

	printf("\nCAN %d:\n", port_num);
	printf("Controller address is 0x%llx\n", base);

	if (cfg->eff) {
		printf("Rx reg:\n");
		for (i = 0; i < 14; i++)
			printf("Reg[%d] : 0x%02x ", i + EM_FI, readb(base + EM_FI + i));
		printf("\n");
		writeb(MODE_RM, base + REG_MOD);
		printf("Mode     reg: 0x%02x\n", readb(base + REG_MOD));
		printf("SR       reg: 0x%02x\n", readb(base + REG_SR));
		printf("IR       reg: 0x%02x\n", readb(base + REG_IR));
		printf("IER      reg: 0x%02x\n", readb(base + REG_IER));
		printf("AMR      reg: 0x%02x\n", readb(base + REG_AMR));
		printf("BTR0     reg: 0x%02x\n", readb(base + REG_BTR0));
		printf("BTR1     reg: 0x%02x\n", readb(base + REG_BTR1));
		printf("BITNO    reg: 0x%02x\n", readb(base + REG_BITNO));
		printf("EMLR     reg: 0x%02x\n", readb(base + REG_EMLR));
		printf("RXEER    reg: 0x%02x\n", readb(base + REG_RXERR));
		printf("TXEER    reg: 0x%02x\n", readb(base + REG_TXERR));
		for (i = 0; i < 4; i++)
			printf("ACCC[%d]  reg: 0x%02x\n", i, readb(base + REG_ACCC0 + i));
		for (i = 0; i < 4; i++)
			printf("ACCM[%d]  reg: 0x%02x\n", i, readb(base + REG_ACCM0 + i));
		printf("FRAC	 reg: 0x%02x\n", readb(base + REG_FRAC));
		printf("INTL	 reg: 0x%02x\n", readb(base + REG_INTL));
		printf("INTH	 reg: 0x%02x\n", readb(base + REG_INTH));
		writeb(0x0, base + REG_MOD);
	} else {
		printf("Tx reg:\n");
		for (i = 0; i < 10; i++)
			printf("Reg[%d] : 0x%02x ", i + SM_SFF_TID1, readb(base + SM_SFF_TID1 + i));
		printf("\n");
		printf("Rx reg:\n");
		for (i = 0; i < 10; i++)
			printf("Reg[%d] : 0x%02x ", i + SM_SFF_RID1, readb(base + SM_SFF_RID1 + i));
		printf("\n");
		writeb(MODE_RM, base + REG_CR);
		printf("CR       reg: 0x%02x\n", readb(base + REG_CR));
		printf("SR       reg: 0x%02x\n", readb(base + REG_SR));
		printf("IR       reg: 0x%02x\n", readb(base + REG_IR));
		printf("ACR      reg: 0x%02x\n", readb(base + REG_ACR));
		printf("AMR      reg: 0x%02x\n", readb(base + REG_AMR));
		printf("BTR0     reg: 0x%02x\n", readb(base + REG_BTR0));
		printf("BTR1     reg: 0x%02x\n", readb(base + REG_BTR1));
		writeb(0x0, base + REG_CR);
	}
	printf("\n");
}

void _can_dump_cfg(struct can_cfg *cfg)
{
	if (!cfg) {
		printf("Invalid arguments in %s.\n", __func__);
		return;
	}

	printf("\nCAN %d:\n", cfg->port_num);
	printf("Reference clock frequency: %d Hz.\n", cfg->ref_clk);
	printf("Bitrate:                   %d\n", cfg->bitrate);
	printf("Calculate bitrate:         %d\n", cfg->real_bitrate);

	unsigned char mode = cfg->can_mode;
	if (cfg->eff) {
		printf("Work in Extended mode: ");

		if (mode & MODE_LOM)
			printf("Listen only");

		if (mode & MODE_STM)
			printf("Selftest");

		if (mode & MODE_AFM)
			printf("Single filter");
		else
			printf("Double filter");

		if (mode & MODE_SM)
			printf("Sleep mode");

		if (mode & MODE_FRC)
			printf("Fraction divider");
	} else {
		printf("Work in Standard mode: ");

		if (mode & MODE_FRC)
			printf("Fraction divider");
		else
			printf("None extra mode");
	}
	printf(".\n\n");
}

/* real CAN config function */
static void can_config(int argc, char **argv)
{
	if (argc != 6 && argc != 8) {
		printf("Usage:\n");
		printf("can_config [port number] [clock frequency] [bitrate] [can_mode] [EFF] [acr id](optional) [amr msk](optional)\n");
		printf("PS: Port number must within [0, 5].\n");
		printf("    Hz is clock frequency unit.\n");
		printf("    CAN mode(BIT 1 ~ BIT 4 only worked in extended mode):\n");
		printf("    BIT 1: Only listen; BIT 2: Selftest; BIT 3: Single filter; BIT 4: Sleep; BIT 6: Fraction divider\n");
		printf("    EFF: 0 Standard work mode;1 Extended work mode.\n");
		printf("    ACR and AMR must be given at same time.\n");
		printf("Example:\n");
		printf("can_config 0 10000000 200000 0x44 1 0xa0 0x00\n");
		printf("CAN 0 will work in extended mode with selftest and double filter and fraction divider.\n");
		printf("CAN 0 transmit bitrate is 200Kbps with 10Mhz referency clock frequency.\n");
		printf("CAN 0 only receive unique frames which ID[10 ~ 3] is 0xa0 or ID[28 ~ 21] is 0xa0.\n");
		return;
	}

	unsigned char port_num	= (unsigned char)strtoul(argv[1], NULL, 0);
	if (port_num < 0 || port_num > 5) {
		printf("Wrong port num!\n");
		return;
	}
	CFG[port_num].port_num	= port_num;

	unsigned long ref_clk = (unsigned int)strtoul(argv[2], NULL, 0);
	unsigned int bitrate = (unsigned int)strtoul(argv[3], NULL, 0);

	if (CFG[port_num].ref_clk != ref_clk || CFG[port_num].bitrate != bitrate) {
		CFG[port_num].ref_clk = ref_clk;
		CFG[port_num].bitrate = bitrate;
		CFG[port_num].real_bitrate = 0;
	}

	CFG[port_num].can_mode	= (unsigned char)strtoul(argv[4], NULL, 0);
	CFG[port_num].eff				= (unsigned char)strtoul(argv[5], NULL, 0);

	if (CFG[port_num].bitrate > CFG[port_num].ref_clk) {
		printf("Bitrate too high.\n");
		return;
	}

	int i;
	if (argc == 8) {
		_can_parse_data(CFG[port_num].acr_id, (unsigned char*)argv[6]);
		_can_parse_data(CFG[port_num].amr_msk, (unsigned char*)argv[7]);
	} else {
		for (i = 0; i < 4; i++) {
			CFG[port_num].amr_msk[i] = 0xff;
		}
	}

	can_addr_config();
	_can_config(&CFG[port_num]);
	CFGED[port_num] = 1;
}

/* real CAN transmit test function */
static void can_tx_test (int argc, char **argv)
{
	unsigned char buf[8] = {0};

	if (argc != 6) {
		printf("Usage:\n");
		printf("can_tx [port_num] [frame type] [can id] [data] [send times]\n");
		printf("PS: Must config can port before send.\n");
		printf("    Frame type: 0 Standard frame; 1 Extended frame.\n");
		printf("Example:\n");
		printf("can_tx 0 0 0x500 11223344556677 1\n");
		printf("CAN 0 send a standard frame: 0xa0 0x08 0x11 0x22 0x33 0x44 0x55 0x66 0x77\n");
		return;
	}

	unsigned char port_num	= (unsigned char)strtoul(argv[1], NULL, 0);
	if (port_num < 0 || port_num > 5) {
		printf("Wrong port num!\n");
		return;
	}

	if (!CFGED[port_num]) {
		printf("Must config can port before send.\n");
		return;
	}

	unsigned char frame_type = (unsigned char)strtoul(argv[2], NULL, 0);
	unsigned int can_id = (unsigned int)strtoul(argv[3], NULL, 0);
	char *data = argv[4];
	int data_len = _can_parse_data(buf, data);
	unsigned long times = strtoul(argv[5], NULL, 0);

	if (can_id > 0x3fffffff) {
			printf("Id overflow.\n");
			return;
	}

	if (!CFG[port_num].eff) {
		if (can_id > 0x7ff) {
			printf("Id overflow.\n");
			return;
		}

		if (frame_type) {
			printf("Wrong frame type.\n");
			return;
		}
	}

	while (times--) {
		_can_tx(&CFG[port_num], frame_type, can_id, buf, data_len);
		delay(10000);
	}
}

/* real CAN transmit test function */
static void can_rx_test (int argc, char **argv)
{
	unsigned char sig;
	if (argc != 4) {
		printf("Usage:\n");
		printf("can_rx [port_num] [interrupt type] [frame numbers]\n");
		printf("PS: Must config can port before send.\n");
		printf("    Interrupt type: 0 receive interruppt; Otherwise fifo full alarm interrupt, and the fifo depth will be set to the value.\n");
		printf("Example:\n");
		printf("can_rx 0 25 2\n");
		printf("CAN 0 will receive 2 frames when fifo full alarm be triggered, and fifo depth is 25 bytes.\n");
	return;
	}

	unsigned char port_num	= (unsigned char)strtoul(argv[1], NULL, 0);
	if (port_num < 0 || port_num > 5) {
		printf("Wrong port num!\n");
		return;
	}

	if (!CFGED[port_num]) {
		printf("Must config can port before receive.\n");
		return;
	}

	unsigned char depth = (unsigned char)strtoul(argv[2], NULL, 0);
	unsigned long num = strtoul(argv[3], NULL, 0);

	while (num--) {
		_can_rx(&CFG[port_num], depth);
	}
}

static void can_quick_test(int argc, char **argv)
{
	unsigned char can0_buf[8] = {0x11,0x22,0x33,0x44,0x55,0x66,0x77,0x88};
	unsigned char can1_buf[8] = {0x12,0x34,0x56,0x78,0x90,0x6c,0xde,0xf0};
	unsigned char can_mode = 0;
	unsigned char eff = 0;
	unsigned int bitrate = 800000;
	unsigned long ref_clk = 50000000;
	int i;
	int port_a,port_b;
	printf("CAN 0 and CAN 1 transmit test.\n");



	if (argc <3)
	{
		printf("Usage:\n");
		printf("can_quick_test [port_tx] [port_rx] [....]\n");
		return;
	}

	port_a = (unsigned char)strtoul(argv[1], NULL, 0);
	port_b = (unsigned char)strtoul(argv[2], NULL, 0);

	if (argc >= 4)
		can_mode = (unsigned char)strtoul(argv[3], NULL, 0);

	if (argc >= 5)
		eff = (unsigned char)strtoul(argv[4], NULL, 0);

	if (argc >= 7) {
		bitrate = (unsigned int)strtoul(argv[5], NULL, 0);
		ref_clk = (unsigned long)strtoul(argv[6], NULL, 0);
	}

	CFG[port_a].port_num = port_a;
	CFG[port_a].ref_clk = ref_clk;
	CFG[port_a].bitrate = bitrate;
	CFG[port_a].can_mode = can_mode;
	CFG[port_a].eff = eff;
	CFG[port_a].acr_id[0] = 0xa0;

	CFG[port_b].port_num = port_b;
	CFG[port_b].ref_clk = ref_clk;
	CFG[port_b].bitrate = bitrate;
	CFG[port_b].can_mode = can_mode;
	CFG[port_b].eff = eff;
	CFG[port_b].acr_id[0] = 0xb1;

	for (i = 0; i < 4; i++) {
		CFG[port_a].amr_msk[i] = 0xff;
		CFG[port_b].amr_msk[i] = 0xff;
	}

	can_addr_config();
	_can_config(&CFG[port_a]);
	CFGED[port_a] = 1;
	_can_dump_cfg(&CFG[port_a]);

	_can_config(&CFG[port_b]);
	CFGED[port_b] = 1;
	_can_dump_cfg(&CFG[port_b]);

	_can_tx(&CFG[port_a], 0, CFG[port_b].acr_id[0] << 3, can0_buf, 8);
	_can_rx(&CFG[port_b], 0);
	_can_tx(&CFG[port_b], 0, CFG[port_a].acr_id[0] << 3, can1_buf, 8);
	_can_rx(&CFG[port_a], 0);
}

static void can_long_test(int argc, char **argv)
{
	char buf[10][8] = {{0x11,0x11,0x11,0x11,0x11,0x11,0x11,0x11},
										 {0x22,0x22,0x22,0x22,0x22,0x22,0x22,0x22},
										 {0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33},
										 {0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44},
										 {0x55,0x55,0x55,0x55,0x55,0x55,0x55,0x55},
										 {0x66,0x66,0x66,0x66,0x66,0x66,0x66,0x66},
										 {0x77,0x77,0x77,0x77,0x77,0x77,0x77,0x77},
										 {0x88,0x88,0x88,0x88,0x88,0x88,0x88,0x88},
										 {0x99,0x99,0x99,0x99,0x99,0x99,0x99,0x99},
										 {0xaa,0xaa,0xaa,0xaa,0xaa,0xaa,0xaa,0xaa}};

	if (argc != 5) {
		printf("Usage:\n");
		printf("can_long_test [port_num] [frame type] [can id] [send times]\n");
		printf("PS: Must config can port before send.\n");
		printf("    Frame type: 0 Standard frame; 1 Extended frame.\n");
		return;
	}

	unsigned char port_num	= (unsigned char)strtoul(argv[1], NULL, 0);
	if (port_num < 0 || port_num > 5) {
		printf("Wrong port num!\n");
		return;
	}

	if (!CFGED[port_num]) {
		printf("Must config can port before send.\n");
		return;
	}

	unsigned char frame_type = (unsigned char)strtoul(argv[2], NULL, 0);
	unsigned int can_id = (unsigned int)strtoul(argv[3], NULL, 0);
	unsigned long times = strtoul(argv[4], NULL, 0);

	if (can_id > 0x3fffffff) {
			printf("Id overflow.\n");
			return;
	}

	if (!CFG[port_num].eff) {
		if (can_id > 0x7ff) {
			printf("Id overflow.\n");
			return;
		}

		if (frame_type) {
			printf("Wrong frame type.\n");
			return;
		}
	}

	if (times) {
		while (times--) {
			_can_tx(&CFG[port_num], frame_type, can_id, buf[times % 10], 8);
		}
	} else {
		unsigned int cnt = 0;
		while(1) {
			cnt++;
			_can_tx(&CFG[port_num], frame_type, can_id, buf[cnt % 10], 8);
		}
	}
}

static void can_dump_cfg(int argc, char **argv)
{
	if (argc != 2) {
		printf("Usage:\n");
		printf("can_dump_cfg [port_num]\n");
		return;
	}

	unsigned char port_num	= (unsigned char)strtoul(argv[1], NULL, 0);
	if (port_num < 0 || port_num > 5) {
		printf("Wrong port num!\n");
		return;
	}

	if (!CFGED[port_num]) {
		printf("Must config can port before dump.\n");
		return;
	}

	_can_dump_cfg(&CFG[port_num]);
}

static void can_dump_reg(int argc, char **argv)
{
	if (argc != 2) {
		printf("Usage:\n");
		printf("can_dump_reg [port_num]\n");
		return;
	}

	unsigned char port_num	= (unsigned char)strtoul(argv[1], NULL, 0);
	if (port_num < 0 || port_num > 5) {
		printf("Wrong port num!\n");
		return;
	}

	if (!CFGED[port_num]) {
		printf("Must config can port before dump.\n");
		return;
	}

	_can_dump_reg(&CFG[port_num]);
}

static const Cmd Cmds[] = {
	{"ls2k2100 can"},
	{"can_config", "", 0, "test the can function", can_config, 1, 99, 0},
	{"can_tx", "", 0, "test the can function", can_tx_test, 1, 99, 0},
	{"can_rx", "", 0, "test the can function", can_rx_test, 1, 99, 0},
	{"can_softrest", "", 0, "CAN soft reset", can_sf_rest, 1, 99, 0},
	{"can_quick_test", "", 0, "CAN quick test", can_quick_test, 1, 99, 0},
	{"can_long_test", "", 0, "CAN long reset", can_long_test, 1, 99, 0},
	{"can_dump_cfg", "", 0, "Dump CAN config srtuct", can_dump_cfg, 1, 99, 0},
	{"can_dump_reg", "", 0, "Dump CAN controller reg value", can_dump_reg, 1, 99, 0},
	{0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));
static void init_cmd()
{
				cmdlist_expand(Cmds, 1);
}
