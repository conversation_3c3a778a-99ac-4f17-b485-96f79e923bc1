#include <stdio.h>
#include <stdlib.h>
#include <pmon.h>
#include "rio.h"

#define HT1_CONF_BASE_ADDR  PHYS_TO_UNCACHED(0xfdfe000000)
#define HT1_MEM_BASE_ADDR         0x8000000000000000
#define HT1_MEM_BASE_ADDR_CACHED  0x9000000000000000
#define CONFBUS_BASE_ADDR   0x10010000
#define LS7A_CONFBUS_BASE_ADDR  (HT1_MEM_BASE_ADDR | CONFBUS_BASE_ADDR)

#define HEADER_ADDR(X,Y)   (HT1_CONF_BASE_ADDR | (X << 11) | (Y << 8))

#define CONFBUS_HEADER_ADDR     HEADER_ADDR(21, 0)
#define RIO0_HEADER_ADDR        HEADER_ADDR(24, 0)
#define RIO1_HEADER_ADDR        HEADER_ADDR(27, 0)

#define write_reg32(a, v) (*(volatile unsigned int *)(a) = (v)) 
#define read_reg32(a) *(volatile unsigned int *)(a)

#define mod_fld(reg, field, v) {reg &=~M_##field; reg |= ((v&m_##field)<<S_##field);}
#define get_fld(reg, field)    ((reg>>S_##field) & (M_##field >> S_##field)) 

extern void rio_init(uint32_t, uint8_t);
/* RAPID IO */
enum {
	LS_RAPIDIO_SPEED_1_25 = 0,
	LS_RAPIDIO_SPEED_2_5,
	LS_RAPIDIO_SPEED_3_125,
	LS_RAPIDIO_SPEED_5,
	LS_RAPIDIO_SPEED_6_25,
};

volatile unsigned long rio_axi_base_addr = 0;
volatile unsigned long rio_apb_base_addr = 0;
unsigned long long confbus_base_addr = LS7A_CONFBUS_BASE_ADDR; 

void set_apb_base_soft(uint argc, void* argv[])
{
    uint32_t rab_apb_csr = 0;
    uint32_t page_sel = (uint32_t) strtoul(argv[1], 0, 0);
    page_sel = page_sel >> 11; // page_sel = addr / 2kB
    mod_fld(rab_apb_csr, RAB_APB_CSR_PAGE_SEL, page_sel);
    write_reg32(rio_apb_base_addr+RAB_APB_CSR, rab_apb_csr);
}

void rio_addr_cfg(uint8_t port)
{
	unsigned long long header;
	unsigned long long bar0;
	unsigned int bar0_low;
	unsigned int bar0_high;
	unsigned long long confbus_base_addr = PHYS_TO_UNCACHED(0x10010000);
	uint32_t t;

	if (port == 0) {
		t = 0x3fffffff;
		readl(confbus_base_addr|0x3890) = t;
		readl(confbus_base_addr|0x3894) = 0;
		t = 0xfffffff;
		readl(confbus_base_addr|0x3898) = t;
		readl(confbus_base_addr|0x389c) = 0;
		
		header = (PHYS_TO_UNCACHED(0xfdfe000000) | (24 << 11) | (0 << 8));
	} else {
		t = 0x3fffffff;
		readl(confbus_base_addr|0x38c0) = t;
		readl(confbus_base_addr|0x38c4) = 0;
		t = 0xfffffff;
		readl(confbus_base_addr|0x38c8) = t;
		readl(confbus_base_addr|0x38cc) = 0;

		header = (PHYS_TO_UNCACHED(0xfdfe000000) | (27 << 11) | (0 << 8));
	}

	readl(header + 0x04) = 0x147;
	readl(header + 0x10) = 0x40000000;
	readl(header + 0x18) = 0x5ff92000;

	rio_axi_base_addr = (PHYS_TO_UNCACHED(readl(header + 0x10) & 0xfffffff0));
	rio_apb_base_addr = (PHYS_TO_UNCACHED(readl(header + 0x18) & 0xfffffff0));

	printf("rio_axi_base_addr: 0x%llx\n", rio_axi_base_addr);
	printf("rio_apb_base_addr: 0x%llx\n", rio_apb_base_addr);
}

unsigned long long get_confbus_base_addr(volatile unsigned long long header_base_address)
{
    volatile unsigned long long tmp_data;
    tmp_data = *((volatile unsigned int *)(header_base_address + 0x10));
    tmp_data = tmp_data & 0xfffffff0;
    tmp_data = tmp_data | HT1_MEM_BASE_ADDR;

    return tmp_data;
}

unsigned long long get_rio_axi_base_addr(volatile unsigned long long header_base_address)
{
    volatile unsigned long long tmp_data;
    tmp_data = *((volatile unsigned int *)(header_base_address + 0x10));
    tmp_data = tmp_data & 0xfffffff0;
    tmp_data = tmp_data | HT1_MEM_BASE_ADDR;

    return tmp_data;
}

unsigned long long get_rio_apb_base_addr(volatile unsigned long long header_base_address)
{
    volatile unsigned long long tmp_data;
    tmp_data = *((volatile unsigned int *)(header_base_address + 0x18));
    tmp_data = tmp_data & 0xfffffff0;
    tmp_data = tmp_data | HT1_MEM_BASE_ADDR;

    return tmp_data;
}

void get_rio_base_addr(uint32_t port_id)
{
    if(port_id == 0x0){
        rio_axi_base_addr = get_rio_axi_base_addr(RIO0_HEADER_ADDR);
        rio_apb_base_addr = get_rio_apb_base_addr(RIO0_HEADER_ADDR);
    }else{
        rio_axi_base_addr = get_rio_axi_base_addr(RIO1_HEADER_ADDR);
        rio_apb_base_addr = get_rio_apb_base_addr(RIO1_HEADER_ADDR);
    }
}

#define MSG_DESC_ADDR_PHY	0x90100000
#define DSTX_DESC_ADDR_PHY	0x90100000
#define DSRX_DESC_ADDR_PHY	0x90200000

//data streaming
void init_ds_tx_desc()
{
	int i;
	unsigned long long desc_addr = PHYS_TO_CACHED(DSTX_DESC_ADDR_PHY);
	printf("tx desc address is 0x%llx\n", desc_addr);

	//can not use single desc and multi desc in one chain
#if 1
	//one single
	readl(desc_addr + 0x0) = 0x1d | (1 << 16);
	readl(desc_addr + 0x4) = 0;
	readl(desc_addr + 0x8) = 0;
	readl(desc_addr + 0xc) = (DSTX_DESC_ADDR_PHY + 0x20) >> 5;
	readl(desc_addr + 0x10) = 0;

	desc_addr += 0x20;
#else
	//17 multi
	for (i = 0; i < 17; i++) {
		if (i == 0) //headr
			readl(desc_addr + 0x0) = 0x7 | (1 << 16);
		else if (i == 16)
			readl(desc_addr + 0x0) = 0x1 | (1 << 5) | (0x1000 << 16);
		else if (i == 1)
			readl(desc_addr + 0x0) = 0x3 | (1 << 4) | (0x1000 << 16);
		else
			readl(desc_addr + 0x0) = 0x3 | (0x1000 << 16);

		readl(desc_addr + 0x4) = 0;
		readl(desc_addr + 0x8) = 0;
		readl(desc_addr + 0xc) = ((desc_addr & 0xffffffff) + 0x20) >> 5;
		desc_addr += 0x20;
	}
#endif
}

void init_ds_rx_desc()
{
	int i;
	unsigned long long desc_addr = PHYS_TO_CACHED(DSRX_DESC_ADDR_PHY);
	printf("rx desc address is 0x%llx\n", desc_addr);

	for (i = 0; i < 10; i++) {
		if (i == 9) //headr
			readl(desc_addr + 0x0) = 0x3;
		else
			readl(desc_addr + 0x0) = 0x5;

		readl(desc_addr + 0x4) = 0;
		readl(desc_addr + 0x8) = 0;
		readl(desc_addr + 0xc) = 0x20000 * (i + 1);
		readl(desc_addr + 0x10) = (desc_addr + 0x20) >> 5;
		desc_addr += 0x20;
	}
}

void ds_loopback()
{
	unsigned long long mem_s, mem_d;
	int i;

	mem_s = PHYS_TO_CACHED(0);
	mem_d = PHYS_TO_CACHED(0x20000);
	init_ds_tx_desc();
	init_ds_rx_desc();

	set_apb_base(0x22800);

	readl(rio_apb_base_addr + 0x328 + 0x800) = DSRX_DESC_ADDR_PHY >> 5;
	readl(rio_apb_base_addr + 0x32c + 0x800) = 0x3 << 1;
	//enable rx engine
	readl(rio_apb_base_addr + 0x220 + 0x800) = 0x1;

	readl(rio_apb_base_addr + 0x530 + 0x800) = DSTX_DESC_ADDR_PHY >> 5;
	//enable tx engine
	readl(rio_apb_base_addr + 0x528 + 0x800) = 0x7;
	printf("loop back done, check memory addr is 0x%llx\n", PHYS_TO_CACHED(0x20000));

	for (i=0; i<0x4000; i+=4) {
		if (readl(mem_s + i) != readl(mem_d + i)) {
			printf("data error at 0x%llx, expect data is 0x%x, real data is 0x%x\n", mem_d + i, readl(mem_s + i), readl(mem_d + i));
		}
	}
}

void init_msg_rx_desc()
{
	int i;
	unsigned long long desc_addr = PHYS_TO_CACHED(MSG_DESC_ADDR_PHY);
	for (i = 0; i < 10; i++) {
		if (i == 9)
			readl(desc_addr + 0x0) = (1 << 0) | (1 << 2) | (3 << 4);
		else
			readl(desc_addr + 0x0) = (1 << 0) | (1 << 1) | (3 << 4);

		readl(desc_addr + 0x4) = 0;
		readl(desc_addr + 0x8) = 0;
		readl(desc_addr + 0xc) = (MSG_DESC_ADDR_PHY + (i + 1) * 0x10) >> 4;
		desc_addr += 0x10;
	}
}

void init_msg_tx_desc()
{
	int i;
	unsigned long long desc_addr = PHYS_TO_CACHED(MSG_DESC_ADDR_PHY);
	for (i = 0; i < 10; i++) {
		if (i == 9)
			readl(desc_addr + 0x0) = (1 << 0) | (1 << 2) | (1 << 16);
		else
			readl(desc_addr + 0x0) = (1 << 0) | (1 << 1) | (1 << 16);

		readl(desc_addr + 0x4) = (1 << 2) | (1 << 8) | (4 << 18);
		readl(desc_addr + 0x8) = 0;
		readl(desc_addr + 0xc) = (MSG_DESC_ADDR_PHY + (i + 1) * 0x10) >> 4;
		desc_addr += 0x10;
	}
}

void rio_msg_rx_test()
{
	int i;

	set_apb_base(0x23000);
	readl(rio_apb_base_addr + 0xc80) = 0xf;//enable cached

	init_msg_rx_desc();
	set_apb_base(0x20000);

	readl(rio_apb_base_addr + 0x604 + 0x800) = MSG_DESC_ADDR_PHY >> 4;
	readl(rio_apb_base_addr + 0x600 + 0x800) = (3 << 0) | (1 << 6);
	printf("rio 1 channel(0x604) msg rcv enable done.\n");
}

void rio_msg_tx_test()
{
	int i;

	set_apb_base(0x23000);
	readl(rio_apb_base_addr + 0xc80) = 0xf;//enable cached

	for (i = 0; i < 64; i++)
		readb(PHYS_TO_CACHED(i)) = i;

	init_msg_tx_desc();
	set_apb_base(0x20000);

	readl(rio_apb_base_addr + 0x504 + 0x800) = MSG_DESC_ADDR_PHY >> 4;
	readl(rio_apb_base_addr + 0x500 + 0x800) = (3 << 0);
	printf("rio 1 channel msg tx enable done, send mbox 1.\n");
}

//AMAP TYPE
#define MAINTENANCE      0
#define NREAD_NWRITE     1
#define NREAD_NWRITE_R   2
#define NREAD_SWRITE     3

void rab_apio_amap_init(uint32_t n, uint32_t type, uint32_t bar_size, uint32_t bar_base_addr)
{
    uint32_t amap_ctrl     = 0;
    uint32_t amap_size     = 0;
    uint32_t amap_axi_base = 0;
    uint32_t amap_rio_base = 0;
    uint32_t size = bar_size >> 10;
    uint32_t base = bar_base_addr >> 10;

    mod_fld(amap_ctrl, RAB_APIO_AMAP_CTRL_EN, 0x1);
    mod_fld(amap_ctrl, RAB_APIO_AMAP_CTRL_TYPE, type);
    mod_fld(amap_ctrl, RAB_APIO_AMAP_CTRL_DEST_ID, 0x1);
    mod_fld(amap_size, RAB_APIO_AMAP_SIZE_WIN_SIZE, size);
    mod_fld(amap_axi_base, RAB_APIO_AMAP_ABAR_AXI_WIN_BASE, base);
    mod_fld(amap_axi_base, RAB_APIO_AMAP_ABAR_AXI_WIN_BASE_UP, 0x0);
    mod_fld(amap_rio_base, RAB_APIO_AMAP_RBAR_RIO_ADDR_BASE, base);

    write_reg32(rio_apb_base_addr+RAB_APIO_AMAP_0_CTRL+0x10*n, amap_ctrl);
    write_reg32(rio_apb_base_addr+RAB_APIO_AMAP_0_SIZE+0x10*n, amap_size);
    write_reg32(rio_apb_base_addr+RAB_APIO_AMAP_0_AXI_BASE+0x10*n, amap_axi_base);
    write_reg32(rio_apb_base_addr+RAB_APIO_AMAP_0_RIO_BASE+0x10*n, amap_rio_base);
}

void rab_apio_init()
{
    uint32_t rab_apio_ctrl = 0;
    mod_fld(rab_apio_ctrl, RAB_APIO_CTRL_PIO_EN, 1);
    mod_fld(rab_apio_ctrl, RAB_APIO_CTRL_MEM_MAP_EN, 1);
    mod_fld(rab_apio_ctrl, RAB_APIO_CTRL_MNTN_MAP_EN, 1);
    write_reg32(rio_apb_base_addr+RAB_APIO_CTRL, rab_apio_ctrl);

    printf("begin to config rio_axi bar\n");
    //bar 0 set to maintenace access
    rab_apio_amap_init(0, MAINTENANCE, 0x10000, 0x0);            //64KB 0x0     - 0x10000
    //bar 1 set to NREAD & NWRITE
    rab_apio_amap_init(1, NREAD_NWRITE, 0x10000, 0x10000);       //64KB 0x10000 - 0x10000
    //bar 2 set to NREAD & NWRITE_R
    rab_apio_amap_init(2, NREAD_NWRITE_R, 0x8000, 0x20000);      //32KB 0x20000 - 0x28000
    //bar 3 set to NREAD & NWRITE_R
    rab_apio_amap_init(3, NREAD_NWRITE_R, 0x10000, 0x28000);     //64KB 0x28000 - 0x38000
    //bar 4 set to maintenace access
    rab_apio_amap_init(4, MAINTENANCE, 0x10000, 0x40000);        //64KB 0x40000 - 0x50000
    //bar 5 set to NREAD & NWRITE
    rab_apio_amap_init(5, NREAD_NWRITE, 0x10000, 0x50000);       //64KB 0x50000 - 0x60000
    //bar 6 set to NREAD & NWRITE_R
    rab_apio_amap_init(6, NREAD_NWRITE_R, 0x10000, 0x60000);     //64KB 0x60000 - 0x70000
    //bar 7 set to NREAD & NWRITE_R
    rab_apio_amap_init(7, NREAD_NWRITE_R, 0x1000000, 0x1000000); //16MB 0x1000000-0x2000000
    printf("config rio_axi bar ended\n");
}

void set_apb_base(uint32_t apb_base_addr)
{
    uint32_t rab_apb_csr = 0;
    uint32_t page_sel;
    page_sel = apb_base_addr >> 11; // page_sel = addr / 2kB
    mod_fld(rab_apb_csr, RAB_APB_CSR_PAGE_SEL, page_sel);
    write_reg32(rio_apb_base_addr+RAB_APB_CSR, rab_apb_csr);
}

void rab_wdma_rio_up16_cfg()
{
    write_reg32(rio_apb_base_addr+RAB_WDMA_0_RIO_UPPER16_ADDR, 0x1122);
    write_reg32(rio_apb_base_addr+RAB_WDMA_1_RIO_UPPER16_ADDR, 0x3344);
    write_reg32(rio_apb_base_addr+RAB_WDMA_2_RIO_UPPER16_ADDR, 0x5566);
    write_reg32(rio_apb_base_addr+RAB_WDMA_3_RIO_UPPER16_ADDR, 0x7788);
}

void rab_rdma_rio_up16_cfg()
{
    write_reg32(rio_apb_base_addr+RAB_RDMA_0_RIO_UPPER16_ADDR, 0x99aa);
    write_reg32(rio_apb_base_addr+RAB_RDMA_1_RIO_UPPER16_ADDR, 0xbbcc);
    write_reg32(rio_apb_base_addr+RAB_RDMA_2_RIO_UPPER16_ADDR, 0xddee);
    write_reg32(rio_apb_base_addr+RAB_RDMA_3_RIO_UPPER16_ADDR, 0xff00);
}

void rab_dma_init()
{
    //set high 2KB base
    uint32_t win_offset = 0x22800;
    set_apb_base(win_offset); 
    rab_wdma_rio_up16_cfg();
    rab_rdma_rio_up16_cfg();
}

void rab_rpio_init()
{//set RIO ID 2 AXI map table
    uint32_t win_offset = 0;
    set_apb_base(win_offset); 
    uint32_t rab_rpio_ctrl = 0;
    mod_fld(rab_rpio_ctrl, RAB_RPIO_CTRL_PIO_EN, 1);
    write_reg32(rio_apb_base_addr+RAB_RPIO_CTRL_0, rab_rpio_ctrl);
    //set RIO_PIO Mapping Lookup Table Entry, 16 entrys
    //set window size to 256MB, enable entry, set axi_addr[31:28] = i
    int i;
    uint32_t rab_rpio_amap_lut;
    for (i=0;i<16;i++)
    {
        rab_rpio_amap_lut = 0;
        mod_fld(rab_rpio_amap_lut, RAB_RPIO_AMAP_LUT_EN, 1);
        mod_fld(rab_rpio_amap_lut, RAB_RPIO_AMAP_LUT_WINSIZE, 8); //256MB
        mod_fld(rab_rpio_amap_lut, RAB_RPIO_AMAP_LUT_U_AXI_BASEADDR, 0<<14);//0x00000000 
        //write_reg32(rio_apb_base_addr+RAB_RPIO_AMAP_LUT_0+i*4, (i<<24)+0x5);
        //write_reg32(rio_apb_base_addr+RAB_RPIO_AMAP_LUT_0 + 1 * 4, rab_rpio_amap_lut); // default dest ID = 1 => rio AXI map table window 1
        write_reg32(rio_apb_base_addr+RAB_RPIO_AMAP_LUT_0 + i * 4, rab_rpio_amap_lut); // default dest ID = 1 => rio AXI map table window 1
		printf("init id %d rio win\n", i);
    }
}

void rio_win_cfg(int argc, char** argv)
{
	set_apb_base(0);
	readl(rio_apb_base_addr+RAB_CTRL) = 0xf;

	rab_apio_init();
	rab_rpio_init();
}

int rio_apio_test(uint32_t port_id)
{
    uint32_t t;
    uint32_t r;
    get_rio_base_addr(port_id);
    //win0 maintenance write and read
    printf("RIO port %d 0\n",port_id);
    //printf("RIO port %d 0\n");
    t = 0xa5a5a5a5;
    *(volatile unsigned int*)(rio_axi_base_addr+0x120) = t;
    r = *(volatile unsigned int*)(rio_axi_base_addr+0x120);
		printf("run in func %s, line %d\n", __func__, __LINE__);
    if (r != 0xa5a5a5)
    {
        printf("RIO_APIO ERR 0, we want 0xa5a5a5, while we get %x\n", r);
        return 1;
    }
    //win2 NREAD_NWRITE_R
    printf("1\n");
    *(volatile unsigned int*)(rio_axi_base_addr+0x20100) = t;
    r = *(volatile unsigned int*)(rio_axi_base_addr+0x20100);
    if (r != 0xa5a58005)
    {
        printf("RIO_APIO ERR 1, we want 0xa5a58005, while we get %x\n", r);
        return 2;
    }
    //win3 NREAD_NWRITE_R
    printf("2\n");
    *(volatile unsigned int*)(rio_axi_base_addr+0x29100) = t;
    r = *(volatile unsigned int*)(rio_axi_base_addr+0x29100);
    if (r != 0)
    {
        printf("RIO_APIO ERR 2, we want 0, while we get %x\n", r);
        return 3;
    }
    //win7 NREAD_NWRITE_R
    printf("w7\n");
    r = *(volatile unsigned int*)(rio_axi_base_addr+0x1000100);
    //if (r != 0x10)
    //{
        printf("RIO_APIO read 0x41000100, we get %x\n", r);
        //return 4;
    //}
    //win7 NREAD_NWRITE_R
    printf("4\n");
    int i;
    uint32_t r0, r4, r8, rc;
    uint32_t t0, t4, t8, tc;
    for (i=0;i<4;i++)
    {
        r0 = *(volatile unsigned int*)(rio_axi_base_addr+0x1000110+i*16);
        r4 = *(volatile unsigned int*)(rio_axi_base_addr+0x1000114+i*16);
        r8 = *(volatile unsigned int*)(rio_axi_base_addr+0x1000118+i*16);
        rc = *(volatile unsigned int*)(rio_axi_base_addr+0x100011c+i*16);
        //t0 = 0x11+i;
        //t4 = 0;
        //t8 = 0x11+i;
        //tc = 0;
        //if(r0!=t0 || r4!=t4 || r8!=t8 || rc!=tc)
        //{
            printf("RIO_APIO read 0x%x, we get %x\n", rio_axi_base_addr+0x1000110+i*16, r0);
            printf("RIO_APIO read 0x%x, we get %x\n", rio_axi_base_addr+0x1000110+i*16, r4);
            printf("RIO_APIO read 0x%x, we get %x\n", rio_axi_base_addr+0x1000110+i*16, r8);
            printf("RIO_APIO read 0x%x, we get %x\n", rio_axi_base_addr+0x1000110+i*16, rc);
        //    return 5;
        //}
    }
    return 0;
}

void rio_apio_soft(int argc, char **argv)
{
	int port = 0;
	if (argc > 1)
		port = (int)strtoul(argv[1], NULL, 0);
	rio_apio_test(port);
    rio_wdma_test_desp_reg(port);
    rio_rdma_test_desp_reg(port);
}

#define WDMA 1
#define RDMA 0

void rio_dma_intr_cfg(uint32_t w_rdma)
{
    //enable rio wdma interrupt
    //enable rio int in global interrupt enable reg 
    //only check status by wait_interrupt
    //read dev capability
    uint32_t rab_capa = 0; 
    rab_capa = read_reg32(rio_apb_base_addr+RAB_CAPA);

    //enable wdma / rdma and set prefetch to 8
    uint32_t rab_ctrl = 0; 
    rab_ctrl = read_reg32(rio_apb_base_addr+RAB_CTRL);
    if (w_rdma == WDMA)
    {
        mod_fld(rab_ctrl, RAB_CTRL_WDMA_EN, 1);
    }
    else
    {
        mod_fld(rab_ctrl, RAB_CTRL_RDMA_EN, 1);
    }
    mod_fld(rab_ctrl, RAB_CTRL_PRE_SIZE_DMA_DESC_ARR, 3);
    write_reg32(rio_apb_base_addr+RAB_CTRL, rab_ctrl);

    //enable interrupt for wdma / rdma
    uint32_t rab_intr_enab_gnrl = 0;
    rab_intr_enab_gnrl = read_reg32(rio_apb_base_addr+RAB_INTR_ENAB_GNRL);
    if (w_rdma == WDMA)
    {
        mod_fld(rab_intr_enab_gnrl, RAB_INTR_ENAB_GNRL_WDMA, 1);
    }
    else
    {
        mod_fld(rab_intr_enab_gnrl, RAB_INTR_ENAB_GNRL_RDMA, 1);
    }
    write_reg32(rio_apb_base_addr+RAB_INTR_ENAB_GNRL, rab_intr_enab_gnrl);
    if (w_rdma == WDMA)
    //set wdma interrupt generate condition
    {
        uint32_t rab_intr_enab_wdma = 0;
        mod_fld(rab_intr_enab_wdma, RAB_INTR_ENAB_WDMA_CHAIN_DESC_XFER_COMPLT, 0xf);
        mod_fld(rab_intr_enab_wdma, RAB_INTR_ENAB_WDMA_DESC_XFER_COMPLT, 0xf);
        write_reg32(rio_apb_base_addr+RAB_INTR_ENAB_WDMA, rab_intr_enab_wdma);
    }
    else
    //set rdma interrupt generate condition
    {
        uint32_t rab_intr_enab_rdma = 0;
        mod_fld(rab_intr_enab_rdma, RAB_INTR_ENAB_RDMA_CHAIN_DESC_XFER_COMPLT, 0xf);
        mod_fld(rab_intr_enab_rdma, RAB_INTR_ENAB_RDMA_DESC_XFER_COMPLT, 0xf);
        write_reg32(rio_apb_base_addr+RAB_INTR_ENAB_RDMA, rab_intr_enab_rdma);
    }
}

void rio_dma_ctrl_n_cfg(uint32_t w_rdma, uint32_t n, uint32_t start)
{
    if (start != 1) start = 0;
    uint32_t rab_dma_ctrl = 0;
    if (w_rdma == WDMA)
    {
        mod_fld(rab_dma_ctrl, RAB_DMA_CTRL_START, start);
        mod_fld(rab_dma_ctrl, RAB_DMA_CTRL_DEST_ID, 0x1);
        mod_fld(rab_dma_ctrl, RAB_DMA_CTRL_PRIO, 0x2);
        mod_fld(rab_dma_ctrl, RAB_DMA_CTRL_LCL_DESC_AVLB, 0x1);
        write_reg32(rio_apb_base_addr+RAB_WDMA_0_CTRL+0x10*n, rab_dma_ctrl);
    }
    else
    {
        mod_fld(rab_dma_ctrl, RAB_DMA_CTRL_START, start);
        mod_fld(rab_dma_ctrl, RAB_DMA_CTRL_DEST_ID, 0x1);
        mod_fld(rab_dma_ctrl, RAB_DMA_CTRL_PRIO, 0x2);
        mod_fld(rab_dma_ctrl, RAB_DMA_CTRL_LCL_DESC_AVLB, 0x1);
        write_reg32(rio_apb_base_addr+RAB_RDMA_0_CTRL+0x10*n, rab_dma_ctrl);
    }
}

void rio_dma_ctrl_cfg(uint32_t w_rdma)
{
    if (w_rdma == WDMA)
    //WDMA 0 ~ 3 ctrl
    //dest id is 16'h1, priority is 2'h2, use local descriptor
    {
        rio_dma_ctrl_n_cfg(WDMA, 0, 0);
        rio_dma_ctrl_n_cfg(WDMA, 1, 0);
        rio_dma_ctrl_n_cfg(WDMA, 2, 0);
        rio_dma_ctrl_n_cfg(WDMA, 3, 0);
    }
    else
    //RDMA 0 ~ 3 ctrl
    //dest id is 16'h1, priority is 2'h2, descriptor load from axi
    {
        rio_dma_ctrl_n_cfg(RDMA, 0, 0);
        rio_dma_ctrl_n_cfg(RDMA, 1, 0);
        rio_dma_ctrl_n_cfg(RDMA, 2, 0);
        rio_dma_ctrl_n_cfg(RDMA, 3, 0);
    }
}

void wdma_desc_cfg(uint32_t engine_n, uint32_t desc_n)
{
    //now write WDMA engine i, descriptor j
    uint32_t rab_dma_iaddr_desc_sel = 0;
    mod_fld(rab_dma_iaddr_desc_sel, RAB_DMA_IADDR_DESC_SEL_W, 0x1);
    mod_fld(rab_dma_iaddr_desc_sel, RAB_DMA_IADDR_DESC_SEL_ENGINE, engine_n);
    mod_fld(rab_dma_iaddr_desc_sel, RAB_DMA_IADDR_DESC_SEL_DESC, desc_n);
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_SEL, rab_dma_iaddr_desc_sel);

    //axi_addr[36:34] = 3'h0, transfer length is 256 word(1KB)
    //{axi_addr[36:34],5'h0,axi_addr[37],word_cnt[17:0], desp_axi_addr[37:35],next_desp_valid, valid
    uint32_t rab_dma_iaddr_desc_ctrl = 0;
    mod_fld(rab_dma_iaddr_desc_ctrl, RAB_DMA_IADDR_DESC_CTRL_VLD, 0x1);
    mod_fld(rab_dma_iaddr_desc_ctrl, RAB_DMA_IADDR_DESC_CTRL_XFER_LEN, 0x40000); //word cnt, here is 1KB
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_CTRL, rab_dma_iaddr_desc_ctrl);

    //source addr, in WDMA it is axi address
    //axi_source[31:0] >> 2; axi byte addr = 0x400000
    uint32_t wdma_src_addr = 0x400000+(engine_n<<8) + desc_n<<18 ; //1KB increment 
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_SRC_ADDR, wdma_src_addr);

    //dest addr, in WDMA it is RIO address
    //rio_dest_addr >> 2; rio byte addr = 0x400000
    uint32_t wdma_dest_addr = 0x500000+(engine_n<<8) + desc_n<<18; //1KB increment
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_DEST_ADDR, wdma_dest_addr);

    //next descriptor word addr
    uint32_t wdma_next_addr = 0x0;
    //if (desc_n != 7) wdma_next_addr = 0x1;
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_NEXT_ADDR, wdma_next_addr);
}

void rdma_desc_cfg(uint32_t engine_n, uint32_t desc_n)
{
    //write RDMA descriptor to local regs
    //now write RDMA engine i, descriptor j
    uint32_t rab_dma_iaddr_desc_sel = 0;
    mod_fld(rab_dma_iaddr_desc_sel, RAB_DMA_IADDR_DESC_SEL_W, 0x0);
    mod_fld(rab_dma_iaddr_desc_sel, RAB_DMA_IADDR_DESC_SEL_ENGINE, engine_n);
    mod_fld(rab_dma_iaddr_desc_sel, RAB_DMA_IADDR_DESC_SEL_DESC, desc_n);
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_SEL, rab_dma_iaddr_desc_sel);
    //axi_addr[36:34] = 3'h0, transfer length is 16'h10 32-bit word
    //{axi_addr[36:34],5'h0,axi_addr[37],word_cnt[17:0], desp_axi_addr[37:35],next_desp_valid, valid
    uint32_t rab_dma_iaddr_desc_ctrl = 0;
    mod_fld(rab_dma_iaddr_desc_ctrl, RAB_DMA_IADDR_DESC_CTRL_VLD, 0x1);
    mod_fld(rab_dma_iaddr_desc_ctrl, RAB_DMA_IADDR_DESC_CTRL_XFER_LEN, 0x40000);
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_CTRL, rab_dma_iaddr_desc_ctrl);
    //source addr, in RDMA it is RIO address
    //source[33:0] >> 2; RIO_ADDR = 0x301c0000
    uint32_t rdma_src_addr = 0x401000+engine_n*0x80000 + desc_n<<18; //increments by 2GB
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_SRC_ADDR, rdma_src_addr);
    //dest addr, in RDMA it is axi address
    //axi_dest_addr >> 2; RIO_ADDR = 0x302c0000
    uint32_t rdma_dest_addr = 0x502000+engine_n*0x80000 + desc_n<<18; //increments by 2GB
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_DEST_ADDR, rdma_dest_addr);
    //next descriptor word addr
    uint32_t rdma_next_addr = 0x0;
    //if(desc_n != 7) rdma_next_addr = 0x1;
    write_reg32(rio_apb_base_addr+RAB_DMA_IADDR_DESC_NEXT_ADDR, rdma_next_addr);
}

void rio_dma_desc_cfg(uint32_t w_rdma, uint32_t desc_n)
{
    //change page select
    uint32_t win_offset = 0x20800;
    set_apb_base(win_offset); //set base to 0x20800
    rio_dma_ctrl_cfg(w_rdma);
    if (w_rdma)
    {
        //write WDMA descriptor to local regs
        wdma_desc_cfg(0, desc_n);
        printf("RIO WDMA Now start\n");
    }
    else
    {
        //write RDMA descriptor to local regs
        rdma_desc_cfg(0, desc_n);
        printf("RIO RDMA Now start\n");
    }
    //Ctrl 0 W/R
    //dest id is 16'h1, priority is 2'h2, use local descriptor, DMA start
    rio_dma_ctrl_n_cfg(w_rdma, 0, 1);
}

void wait_interrupt(uint32_t port_id)
{
    //need a while to wait interrupt 
    volatile unsigned int tmp_data;
    volatile unsigned long long confbus_base_addr;
    confbus_base_addr=get_confbus_base_addr(CONFBUS_HEADER_ADDR);
    tmp_data = *((volatile unsigned int*)(confbus_base_addr + 0x1380));
		printf("run in func %s, line %d\n", __func__, __LINE__);
    while ((tmp_data & (0x8 >> port_id)) == 0x0)
    {
        tmp_data = *((volatile unsigned int*)(confbus_base_addr + 0x1380));
				printf("0x1380: 0x%x", tmp_data);
    }
}

void rio_dma_intr_process(uint32_t w_rdma)
{
    //change page select
    if (w_rdma)
    {
        set_apb_base(0); 
        uint32_t rab_apb_csr = 0;
        rab_apb_csr = read_reg32(rio_apb_base_addr+RAB_APB_CSR);
    }
    else 
    {
        set_apb_base(0x20000); 
    }
    //difference generated from original stimulus axi_master_1.v
    //these two cfg should above be equivalent
    uint32_t rab_stat = 0;
    rab_stat = read_reg32(rio_apb_base_addr+RAB_STAT);
    while (rab_stat != 0)
    {
        rab_stat = read_reg32(rio_apb_base_addr+RAB_STAT);
    }
    if (w_rdma)
    {
        uint32_t rab_intr_stat_wdma;
        rab_intr_stat_wdma = read_reg32(rio_apb_base_addr+RAB_INTR_STAT_WDMA);
        uint32_t wdma_des_xfer_complt;
        uint32_t wdma_xfer_abrt;
        wdma_des_xfer_complt = get_fld(rab_intr_stat_wdma, RAB_INTR_STAT_WDMA_DESC_XFER_COMPLT);
        wdma_xfer_abrt       = get_fld(rab_intr_stat_wdma, RAB_INTR_STAT_WDMA_XFER_ABRT);
        if (wdma_des_xfer_complt != 0x1 || wdma_xfer_abrt != 0 )
        {
            printf("RIO WDMA STAT ERR, STAT is %x\n", rab_intr_stat_wdma);
        }
        //clear int status in WDMA
        mod_fld(rab_intr_stat_wdma, RAB_INTR_STAT_WDMA_CHAIN_DESC_XFER_COMPLT, 0xf);
        mod_fld(rab_intr_stat_wdma, RAB_INTR_STAT_WDMA_DESC_XFER_COMPLT, 0xf);
        mod_fld(rab_intr_stat_wdma, RAB_INTR_STAT_WDMA_XFER_ABRT, 0xf);
        write_reg32(rio_apb_base_addr+RAB_INTR_STAT_WDMA, rab_intr_stat_wdma);
    }
    else
    {
        uint32_t rab_intr_stat_rdma;
        rab_intr_stat_rdma = read_reg32(rio_apb_base_addr+RAB_INTR_STAT_RDMA);
        uint32_t rdma_des_xfer_complt;
        uint32_t rdma_xfer_abrt;
        rdma_des_xfer_complt = get_fld(rab_intr_stat_rdma, RAB_INTR_STAT_RDMA_DESC_XFER_COMPLT);
        rdma_xfer_abrt       = get_fld(rab_intr_stat_rdma, RAB_INTR_STAT_RDMA_XFER_ABRT);
        if (rdma_des_xfer_complt != 0x1 || rdma_xfer_abrt != 0 )
        {
            printf("RIO RDMA STAT ERR, STAT is %x\n", rab_intr_stat_rdma);
        }
        //clear int status in RDMA  
        mod_fld(rab_intr_stat_rdma, RAB_INTR_STAT_RDMA_CHAIN_DESC_XFER_COMPLT, 0xf);
        mod_fld(rab_intr_stat_rdma, RAB_INTR_STAT_RDMA_DESC_XFER_COMPLT, 0xf);
        mod_fld(rab_intr_stat_rdma, RAB_INTR_STAT_RDMA_XFER_ABRT, 0xf);
        write_reg32(rio_apb_base_addr+RAB_INTR_STAT_RDMA, rab_intr_stat_rdma);
    }
    //read in global interrupt status reg
    //use wait_interrupt to check status only
}

void rio_wdma_test_desp_reg(uint32_t port_id)
{
	int i = 0;
	get_rio_base_addr(port_id);
	printf("RIO %d WDMA test Now begin\n",port_id);
	rio_dma_intr_cfg(WDMA);

	for (i = 0; i < 8; i++) {
		rio_dma_desc_cfg(WDMA, i);
		wait_interrupt(port_id);
		printf("rio WDMA done round %d, begin to clear interrupt\n", i);
		rio_dma_intr_process(WDMA);
		uint32_t tmp_data = *((volatile unsigned int*)(0x8000000010011380));
		printf("==================================0x1380: 0x%x\n", tmp_data);
		printf("RIO WDMA Now end\n");
	}
}

void rio_rdma_test_desp_reg(uint32_t port_id)
{
	int i = 0;
	get_rio_base_addr(port_id);
	printf("RIO %d RDMA test Now begin\n",port_id);

	for (i=0; i<8; i++) {
		rio_dma_intr_cfg(RDMA);
		rio_dma_desc_cfg(RDMA, i);
		wait_interrupt(port_id);
		printf("rio RDMA done round %d, begin to clear interrupt\n", i);
		rio_dma_intr_process(RDMA);
		printf("RIO RDMA Now end\n");
	}
}

static void rio_tsi_windows()
{
	rio_addr_cfg(0);

	//set device id to 0
	readl(rio_apb_base_addr + 0x1010) = 0x4c00;
	readl(rio_apb_base_addr + 0x860) = 0;
	//set local conf space to high
	readl(rio_apb_base_addr + 0x85c) = 0xf;

	rio_msg_rx_test();
}

static void rio_db(int argc, char **argv)
{
	uint16_t descid = 0, info;

	if (argc > 3) {
		printf("riodb (destid) (info)\n");
		return;
	}
	if (argc > 1)
		descid = (uint16_t)strtoul(argv[1], 0, NULL);
	if (argc > 2)
		info = (uint16_t)strtoul(argv[2], 0, NULL);

	readl(rio_apb_base_addr + 0x480) = 1;

	readl(rio_apb_base_addr + 0x404) = info;
	readl(rio_apb_base_addr + 0x400) = 1 | (descid << 16);
}

static void _rio_init(int argc, char **argv)
{
	uint8_t port = 0;
	if (argc < 2 || argc > 3) {
		printf("rio_init [1 ~ 4] (0 ~ 1)\n");
		printf("1: 2.5G, 2: 3.125G, 3: 5G, 4: 6.25G\n");
		printf("0: F1, 1: G0\n");
		return;
	}

	uint32_t speed = (uint32_t)strtoul(argv[1], 0, NULL);
	if (argc == 3)
		port = (uint8_t)strtoul(argv[2], 0, NULL);
	rio_init(speed, port);
	rio_addr_cfg(port);
}

static const Cmd Cmds[] = {
    {"Misc"},
    {"rio_init", "rapidio loopback ", 0, "rio_in", _rio_init, 1, 5, 0},
    {"set_apb", "set_apb_base", "0", "set_apb", set_apb_base_soft, 1, 5, 0},
    {"rio_apio", "rapidio loopback ", 0, "rio_in", rio_apio_soft, 1, 5, 0},
    {"rio_db", "rapidio loopback ", 0, "rio_in", rio_db, 1, 5, 0},
    {"rio_msg_tx", "rapidio loopback ", 0, "rio_in", rio_msg_tx_test, 1, 5, 0},
    {"rio_msg_rx", "rapidio loopback ", 0, "rio_in", rio_msg_rx_test, 1, 5, 0},
    {"rio_ds", "rapidio loopback ", 0, "rio_in", ds_loopback, 1, 5, 0},
    {"rio_tsi", "rapidio loopback ", 0, "rio_in", rio_tsi_windows, 1, 5, 0},
    {"rio_win", "rapidio loopback ", 0, "rio_in", rio_win_cfg, 1, 5, 0},
    {0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));
static void init_cmd()
{
    cmdlist_expand(Cmds, 1);
}
