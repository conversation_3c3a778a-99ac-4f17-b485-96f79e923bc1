#include <platform.h>
#include <sys/types.h>
#include "pmon.h"

#include "../loongson/loongson3_def.h"
#include "target/mem_ctrl.h"
#include "../../../pmon/arch/loongarch/common/ddr4/ddr4_param_debug.c"

#include "../../../pmon/arch/loongarch/early_printf.c"
#include "../../../pmon/arch/loongarch/ls7a/ht.h"
#include "../loongson/ht_link.c"
#include "../../../pmon/arch/loongarch/ls7a/ls7a_init.c"
#include "../../../pmon/arch/loongarch/loongarch_delay.c"

ddr_ctrl mm_ctrl_info;
i2c_param i2c_node_info;

extern int ddr4_init (uint64_t node_num, ddr_ctrl *mm_ctrl);

void mm_feature_init(void)
{
#ifdef LOONGSON_3D6000
	mm_ctrl_info.mc_type = LS3D6000_MC_TYPE;
#elif LOONGSON_3C6000
	mm_ctrl_info.mc_type = LS3C6000_MC_TYPE;
#elif LOONGSON_3D5000
	mm_ctrl_info.mc_type = LS3D5000_MC_TYPE;
#elif LOONGSON_3C5000
	mm_ctrl_info.mc_type = LS3C5000_MC_TYPE;
#else
	mm_ctrl_info.mc_type = LS3A5000_MC_TYPE;
#endif
	mm_ctrl_info.mc_interleave_offset = INTERLEAVE_OFFSET;
	mm_ctrl_info.mc_regs_base = DDR_CFG_BASE;
	mm_ctrl_info.cache_mem_base = 0xa000000000000000;
	mm_ctrl_info.dimm_info_in_flash_offset = DIMM_INFO_IN_FLASH_OFFS;
	mm_ctrl_info.ddr_freq = DDR_FREQ;
	mm_ctrl_info.ddr_freq_2slot = DDR_FREQ_2SLOT;
	mm_ctrl_info.node_offset = NODE_OFFSET;
	mm_ctrl_info.tot_node_num = TOT_NODE_NUM;
	mm_ctrl_info.node_mc_num = MC_PER_NODE;
#ifdef BONITO_100M
	mm_ctrl_info.ref_clk = 100;
#elif BONITO_25M
	mm_ctrl_info.ref_clk = 25;
#endif
	mm_ctrl_info.spi_base = SPI_BASE;
	mm_ctrl_info.uart_base = UART_BASE;
	mm_ctrl_info.l2xbar_conf_addr = L2XBAR_CONF_ADDR;
	mm_ctrl_info.channel_width = 64;
	mm_ctrl_info.dll_bypass = 0;
	//if you want change kernel high start address you should change the macro
	mm_ctrl_info.mem_base = HIGH_MEM_WIN_BASE_ADDR;
	/* mm_ctrl is global variable */
#ifdef QUICK_START
	mm_ctrl_info.table.enable_early_printf      = 0;
#else
	mm_ctrl_info.table.enable_early_printf      = 1;
#endif
	mm_ctrl_info.table.ddr_fast_init            = 0;
	mm_ctrl_info.table.enable_half_freq         = 0;
#ifdef DDR3_MODE
	mm_ctrl_info.table.ddr_param_store          = 0;
	mm_ctrl_info.table.ddr3_dimm                = 1;
	mm_ctrl_info.table.enable_mc_vref_training  = 0;
	mm_ctrl_info.table.enable_ddr_vref_training = 0;
	mm_ctrl_info.table.enable_bit_training      = 0;
	mm_ctrl_info.table.disable_dimm_ecc         = 1;
#else
	mm_ctrl_info.table.ddr_param_store          = 1;
	mm_ctrl_info.table.ddr3_dimm                = 0;
	mm_ctrl_info.table.enable_mc_vref_training  = 1;
	mm_ctrl_info.table.enable_ddr_vref_training = 1;
	mm_ctrl_info.table.enable_bit_training      = 1;
	mm_ctrl_info.table.disable_dimm_ecc         = 0;
#endif
	mm_ctrl_info.table.low_speed                = 0;
	mm_ctrl_info.table.auto_ddr_config          = 1;
	mm_ctrl_info.table.enable_ddr_leveling      = 1;
	mm_ctrl_info.table.print_ddr_leveling       = 0;
	mm_ctrl_info.table.vref_training_debug      = 0;
	mm_ctrl_info.table.bit_training_debug       = 1;
	mm_ctrl_info.table.enable_write_training    = 1;
	mm_ctrl_info.table.debug_write_training     = 0;
	mm_ctrl_info.table.print_dll_sample         = 0;
	mm_ctrl_info.table.disable_dq_odt_training  = 1;
	mm_ctrl_info.table.lvl_debug                = 0;
	mm_ctrl_info.table.disable_dram_crc         = 1;
	mm_ctrl_info.table.two_t_mode_enable        = 0;
	mm_ctrl_info.table.disable_read_dbi         = 1;
	mm_ctrl_info.table.disable_write_dbi        = 1;
	mm_ctrl_info.table.disable_dm               = 0;
	mm_ctrl_info.table.preamble2                = 0;
	mm_ctrl_info.table.set_by_protocol          = 1;
	mm_ctrl_info.table.param_set_from_spd_debug = 0;
	mm_ctrl_info.table.refresh_1x               = 1;
	mm_ctrl_info.table.spd_only                 = 0;
	mm_ctrl_info.table.ddr_debug_param          = 0;
	mm_ctrl_info.table.ddr_soft_clksel          = 1;
#ifdef LS_STR
	mm_ctrl_info.table.str                      = 1;
#else
	mm_ctrl_info.table.str                      = 0;
#endif
#ifdef DDR3_MODE
	mm_ctrl_info.vref.vref_value                = 0x78;
	mm_ctrl_info.table.pda_mode                 = 0;
#else
	mm_ctrl_info.vref.vref_value                = 0x50;
	mm_ctrl_info.table.pda_mode                 = 1;
#endif
	mm_ctrl_info.vref.vref_range                = 0;
	mm_ctrl_info.table.signal_test              = 0;
	mm_ctrl_info.vref.mc_vref_adjust            = 0x0;
	mm_ctrl_info.vref.ddr_vref_adjust           = 0x0;
	mm_ctrl_info.vref.vref_init                 = 0x20;
	mm_ctrl_info.data.rl_manualy                = 0;
	mm_ctrl_info.data.bit_width                 = 64;
	mm_ctrl_info.data.nc16_map                  = 0;
	mm_ctrl_info.data.gate_mode                 = 0;
#if defined(LOONGSON_3D5000) || (defined(LOONGSON_3C6000) && (TOT_NODE_NUM == 4)) || (defined(LOONGSON_3D6000)) || (defined(LOONGSON_3B6000))
	mm_ctrl_info.data.pad_reset_po              = 0x0;
#else
	mm_ctrl_info.data.pad_reset_po              = 0x2;
#endif
	mm_ctrl_info.data.wrlevel_count_low         = 0x0;
	mm_ctrl_info.vref.vref_bits_per             = 0x0;
	mm_ctrl_info.vref.vref_bit                  = 0x0;
	mm_ctrl_info.data.ref_manualy               = 0x0;

	mm_ctrl_info.param.dll_ck_mc0               = 0x44;
	mm_ctrl_info.param.dll_ck_mc1               = 0x44;
#if defined(LOONGSON_3C5000) || defined(LOONGSON_3C6000)
	mm_ctrl_info.param.dll_ck_mc2               = 0x44;
	mm_ctrl_info.param.dll_ck_mc3               = 0x44;
#endif
	mm_ctrl_info.param.RCD                      = 0;
	mm_ctrl_info.param.RP                       = 0;
	mm_ctrl_info.param.RAS                      = 0;
	mm_ctrl_info.param.REF                      = 0;
	mm_ctrl_info.param.RFC                      = 0;

	mm_ctrl_info.ocd.pad_clk_ocd                = PAD_CLK_OCD;
	mm_ctrl_info.ocd.pad_ctrl_ocd               = PAD_CTRL_OCD;
	mm_ctrl_info.ocd.pad_ds_split               = PAD_DS_SPLIT_ALL;
	mm_ctrl_info.ocd.ddr_out_ocd                = 0;

	mm_ctrl_info.odt.rtt_nom_1r_1slot           = RTT_NOM;
	mm_ctrl_info.odt.rtt_park_1r_1slot          = RTT_PARK;
	mm_ctrl_info.odt.mc_dqs_odt_1cs             = MC_DQS_ODT;
	mm_ctrl_info.odt.mc_dq_odt_1cs              = MC_DQ_ODT;

	mm_ctrl_info.odt.rtt_nom_2r_1slot           = RTT_NOM_2RANK;
	mm_ctrl_info.odt.rtt_park_2r_1slot          = RTT_PARK_2RANK;

	mm_ctrl_info.odt.rtt_nom_1r_2slot_cs0       = RTT_NOM_CS0;
	mm_ctrl_info.odt.rtt_park_1r_2slot_cs0      = RTT_PARK_CS0;
	mm_ctrl_info.odt.rtt_nom_1r_2slot_cs1       = RTT_NOM_CS1;
	mm_ctrl_info.odt.rtt_park_1r_2slot_cs1      = RTT_PARK_CS1;

	mm_ctrl_info.odt.rtt_nom_2r_2slot_cs0       = RTT_NOM_2R_CS0;
	mm_ctrl_info.odt.rtt_park_2r_2slot_cs0      = RTT_PARK_2R_CS0;
	mm_ctrl_info.odt.rtt_nom_2r_2slot_cs2       = RTT_NOM_2R_CS2;
	mm_ctrl_info.odt.rtt_park_2r_2slot_cs2      = RTT_PARK_2R_CS2;

	mm_ctrl_info.odt.mc_dqs_odt_2cs             = MC_DQS_ODT_2CS;
	mm_ctrl_info.odt.mc_dq_odt_2cs              = MC_DQ_ODT_2CS;

	mm_ctrl_info.sameba_adj                     = MC_PHY_REG_DATA_070;
	mm_ctrl_info.samebg_adj                     = MC_PHY_REG_DATA_078;
	mm_ctrl_info.samec_adj                      = MC_PHY_REG_DATA_080;
	mm_ctrl_info.samecs_adj                     = MC_PHY_REG_DATA_090;
	mm_ctrl_info.diffcs_adj                     = MC_PHY_REG_DATA_098;

	mm_ctrl_info.i2c_node                       = &i2c_node_info;

	/* paster parameter */
	mm_ctrl_info.paster.mc0_enable              = MC0_ENABLE;
	mm_ctrl_info.paster.mc1_enable              = MC1_ENABLE;

	mm_ctrl_info.paster.mc0_memsize             = MC0_MEMSIZE;
	mm_ctrl_info.paster.mc0_dram_type           = MC0_DRAM_TYPE;
	mm_ctrl_info.paster.mc0_dimm_type           = MC0_DIMM_TYPE;
	mm_ctrl_info.paster.mc0_module_type         = MC0_MODULE_TYPE;
	mm_ctrl_info.paster.mc0_cid_num             = MC0_CID_NUM;
	mm_ctrl_info.paster.mc0_ba_num              = MC0_BA_NUM;
	mm_ctrl_info.paster.mc0_bg_num              = MC0_BG_NUM;
	mm_ctrl_info.paster.mc0_csmap               = MC0_CSMAP;
	mm_ctrl_info.paster.mc0_dram_width          = MC0_DRAM_WIDTH;
	mm_ctrl_info.paster.mc0_module_width        = MC0_MODULE_WIDTH;
	mm_ctrl_info.paster.mc0_sdram_capacity      = MC0_SDRAM_CAPACITY;
	mm_ctrl_info.paster.mc0_col_num             = MC0_COL_NUM;
	mm_ctrl_info.paster.mc0_row_num             = MC0_ROW_NUM;
	mm_ctrl_info.paster.mc0_addr_mirror         = MC0_ADDR_MIRROR;
	mm_ctrl_info.paster.mc0_bg_mirror           = MC0_BG_MIRROR;

	mm_ctrl_info.paster.mc1_memsize             = MC1_MEMSIZE;
	mm_ctrl_info.paster.mc1_dram_type           = MC1_DRAM_TYPE;
	mm_ctrl_info.paster.mc1_dimm_type           = MC1_DIMM_TYPE;
	mm_ctrl_info.paster.mc1_module_type         = MC1_MODULE_TYPE;
	mm_ctrl_info.paster.mc1_cid_num             = MC1_CID_NUM;
	mm_ctrl_info.paster.mc1_ba_num              = MC1_BA_NUM;
	mm_ctrl_info.paster.mc1_bg_num              = MC1_BG_NUM;
	mm_ctrl_info.paster.mc1_csmap               = MC1_CSMAP;
	mm_ctrl_info.paster.mc1_dram_width          = MC1_DRAM_WIDTH;
	mm_ctrl_info.paster.mc1_module_width        = MC1_MODULE_WIDTH;
	mm_ctrl_info.paster.mc1_sdram_capacity      = MC1_SDRAM_CAPACITY;
	mm_ctrl_info.paster.mc1_col_num             = MC1_COL_NUM;
	mm_ctrl_info.paster.mc1_row_num             = MC1_ROW_NUM;
	mm_ctrl_info.paster.mc1_addr_mirror         = MC1_ADDR_MIRROR;
	mm_ctrl_info.paster.mc1_bg_mirror           = MC1_BG_MIRROR;
	mm_ctrl_info.param_reg_array                = &param_info;
}

void mm_feature_init_7a(void)
{
	mm_ctrl_info.mc_type = LS7A2000_MC_TYPE;
	mm_ctrl_info.mc_interleave_offset = 8;
	mm_ctrl_info.mc_regs_base = LS7A_DDR4_CFG_BASE;
	mm_ctrl_info.cache_mem_base = 0xa0000e0000000000 | TEMP_GMEM_ADDR;
	mm_ctrl_info.dimm_info_in_flash_offset = LS7A_DIMM_INFO_IN_FLASH_OFFS;
	mm_ctrl_info.node_offset = NODE_OFFSET;
	mm_ctrl_info.ddr_freq = DDR_FREQ;
	mm_ctrl_info.tot_node_num = 0;
	mm_ctrl_info.node_mc_num = 1;
#ifdef BONITO_100M
	mm_ctrl_info.ref_clk = 100;
#elif BONITO_25M
	mm_ctrl_info.ref_clk = 25;
#endif
	mm_ctrl_info.spi_base = SPI_BASE;
	mm_ctrl_info.uart_base = UART_BASE;
	mm_ctrl_info.l2xbar_conf_addr = L2XBAR_CONF_ADDR;
	mm_ctrl_info.channel_width = 32;
	mm_ctrl_info.dll_bypass = 0;
	//if you want change kernel high start address you should change the macro
	mm_ctrl_info.mem_base = HIGH_MEM_WIN_BASE_ADDR;
	/* mm_ctrl is global variable */
#ifdef QUICK_START
	mm_ctrl_info.table.enable_early_printf      = 0;
#else
	mm_ctrl_info.table.enable_early_printf      = 1;
#endif
	mm_ctrl_info.table.ddr_param_store          = 1;
	mm_ctrl_info.table.ddr_fast_init            = 0;
	mm_ctrl_info.table.enable_half_freq         = 0;
	mm_ctrl_info.table.ddr3_dimm                = 0;
	mm_ctrl_info.table.auto_ddr_config          = 0;
	mm_ctrl_info.table.enable_ddr_leveling      = 1;
	mm_ctrl_info.table.print_ddr_leveling       = 0;
	mm_ctrl_info.table.enable_mc_vref_training  = 1;
	mm_ctrl_info.table.vref_training_debug      = 0;
	mm_ctrl_info.table.enable_ddr_vref_training = 1;
	mm_ctrl_info.table.enable_bit_training      = 1;
	mm_ctrl_info.table.bit_training_debug       = 0;
	mm_ctrl_info.table.enable_write_training    = 1;
	mm_ctrl_info.table.debug_write_training     = 0;
	mm_ctrl_info.table.print_dll_sample         = 0;
	mm_ctrl_info.table.disable_dq_odt_training  = 1;
	mm_ctrl_info.table.lvl_debug                = 0;
	mm_ctrl_info.table.disable_dram_crc         = 1;
	mm_ctrl_info.table.two_t_mode_enable        = 0;
	mm_ctrl_info.table.disable_dimm_ecc         = 1;
	mm_ctrl_info.table.disable_read_dbi         = 1;
	mm_ctrl_info.table.disable_write_dbi        = 1;
	mm_ctrl_info.table.disable_dm               = 0;
	mm_ctrl_info.table.set_by_protocol          = 1;
	mm_ctrl_info.table.param_set_from_spd_debug = 0;
	mm_ctrl_info.table.refresh_1x               = 1;
	mm_ctrl_info.table.spd_only                 = 0;
	mm_ctrl_info.table.ddr_debug_param          = 0;
	mm_ctrl_info.table.ddr_soft_clksel          = 1;
	mm_ctrl_info.table.str                      = 0;
	mm_ctrl_info.table.pda_mode                 = 1;
	mm_ctrl_info.table.signal_test              = 0;
	mm_ctrl_info.vref.vref_range                = 0;
	mm_ctrl_info.vref.vref_value                = 0x50;
	mm_ctrl_info.vref.mc_vref_adjust            = 0x0;
	mm_ctrl_info.vref.ddr_vref_adjust           = 0x0;
	mm_ctrl_info.vref.vref_init                 = 0x20;
	mm_ctrl_info.data.rl_manualy                = 0;
	mm_ctrl_info.data.bit_width                 = 32;
	mm_ctrl_info.data.nc16_map                  = 0;
	mm_ctrl_info.data.gate_mode                 = 2;
	mm_ctrl_info.data.pad_reset_po              = 0x0;

	mm_ctrl_info.data.wrlevel_count_low         = 0x0;
	mm_ctrl_info.vref.vref_bits_per             = 0x0;
	mm_ctrl_info.vref.vref_bit                  = 0x0;
	mm_ctrl_info.data.ref_manualy               = 0x0;
	/* 7A2000 only need mc0 */
	mm_ctrl_info.param.dll_ck_mc0               = 0x44;

	mm_ctrl_info.ocd.pad_clk_ocd                = PAD_CLK_OCD;
	mm_ctrl_info.ocd.pad_ctrl_ocd               = PAD_CTRL_OCD;
	mm_ctrl_info.ocd.pad_ds_split               = PAD_DS_SPLIT_ALL;

	mm_ctrl_info.odt.rtt_nom_1r_1slot           = RTT_NOM_7A;
	mm_ctrl_info.odt.rtt_park_1r_1slot          = RTT_PARK_7A;
	mm_ctrl_info.odt.mc_dqs_odt_1cs             = MC_DQS_ODT;
	mm_ctrl_info.odt.mc_dq_odt_1cs              = MC_DQ_ODT;

	mm_ctrl_info.odt.rtt_nom_2r_1slot           = RTT_NOM_2RANK;
	mm_ctrl_info.odt.rtt_park_2r_1slot          = RTT_PARK_2RANK;

	mm_ctrl_info.odt.rtt_nom_1r_2slot_cs0       = RTT_NOM_CS0;
	mm_ctrl_info.odt.rtt_park_1r_2slot_cs0      = RTT_PARK_CS0;
	mm_ctrl_info.odt.rtt_nom_1r_2slot_cs1       = RTT_NOM_CS1;
	mm_ctrl_info.odt.rtt_park_1r_2slot_cs1      = RTT_PARK_CS1;

	mm_ctrl_info.odt.rtt_nom_2r_2slot_cs0       = RTT_NOM_2R_CS0;
	mm_ctrl_info.odt.rtt_park_2r_2slot_cs0      = RTT_PARK_2R_CS0;
	mm_ctrl_info.odt.rtt_nom_2r_2slot_cs2       = RTT_NOM_2R_CS2;
	mm_ctrl_info.odt.rtt_park_2r_2slot_cs2      = RTT_PARK_2R_CS2;

	mm_ctrl_info.odt.mc_dqs_odt_2cs             = MC_DQS_ODT_2CS;
	mm_ctrl_info.odt.mc_dq_odt_2cs              = MC_DQ_ODT_2CS;

	mm_ctrl_info.sameba_adj                     = MC_PHY_REG_DATA_070;
	mm_ctrl_info.samebg_adj                     = MC_PHY_REG_DATA_078;
	mm_ctrl_info.samec_adj                      = MC_PHY_REG_DATA_080;
	mm_ctrl_info.samecs_adj                     = MC_PHY_REG_DATA_090;
	mm_ctrl_info.diffcs_adj                     = MC_PHY_REG_DATA_098;

	/* paster parameter */
	mm_ctrl_info.paster.mc0_enable              = 1;
	mm_ctrl_info.paster.mc1_enable              = 0;

	mm_ctrl_info.paster.mc0_memsize             = GMEM_MEMSIZE;
	mm_ctrl_info.paster.mc0_dram_type           = GMEM_DRAM_TYPE;
	mm_ctrl_info.paster.mc0_dimm_type           = GMEM_DIMM_TYPE;
	mm_ctrl_info.paster.mc0_module_type         = GMEM_MODULE_TYPE;
	mm_ctrl_info.paster.mc0_cid_num             = GMEM_CID_NUM;
	mm_ctrl_info.paster.mc0_ba_num              = GMEM_BA_NUM;
	mm_ctrl_info.paster.mc0_bg_num              = GMEM_BG_NUM;
	mm_ctrl_info.paster.mc0_csmap               = GMEM_CSMAP;
	mm_ctrl_info.paster.mc0_dram_width          = GMEM_DRAM_WIDTH;
	mm_ctrl_info.paster.mc0_module_width        = GMEM_MODULE_WIDTH;
	mm_ctrl_info.paster.mc0_sdram_capacity      = GMEM_SDRAM_CAPACITY;
	mm_ctrl_info.paster.mc0_col_num             = GMEM_COL_NUM;
	mm_ctrl_info.paster.mc0_row_num             = GMEM_ROW_NUM;
	mm_ctrl_info.paster.mc0_addr_mirror         = GMEM_ADDR_MIRROR;
	mm_ctrl_info.paster.mc0_bg_mirror           = GMEM_BG_MIRROR;
	mm_ctrl_info.param_reg_array                = &param_info_ls7a;
}

void spd_i2c_init(void)
{
	uint64_t i, j;
#ifdef LOONGSON_3D5000
	for (i = 0; i < TOT_NODE_NUM; i++) {
		for (j = 0; j < MC_PER_NODE; j++) {
			i2c_node_info.i2c_mc[i][j].i2c_base = (i / 2 * 2 << NODE_OFFSET) | PHYS_TO_UNCACHED(0x1fe00130 + j / 2 * 8);
			i2c_node_info.i2c_mc[i][j].devid.slot0_addr = (j % 2 * 2 + 0x0) + i % 2 * 4;
			i2c_node_info.i2c_mc[i][j].devid.slot1_addr = (j % 2 * 2 + 0x1) + i % 2 * 4;
		}
	}
#elif LOONGSON_3C5000
	for (i = 0; i < TOT_NODE_NUM; i++) {
		for(j = 0; j < MC_PER_NODE; j++) {
			i2c_node_info.i2c_mc[i][j].i2c_base = (i << NODE_OFFSET) | PHYS_TO_UNCACHED(0x1fe00120);
		}
		i2c_node_info.i2c_mc[i][0].devid.slot0_addr = 0x4;
		i2c_node_info.i2c_mc[i][0].devid.slot1_addr = 0x5;

		i2c_node_info.i2c_mc[i][1].devid.slot0_addr = 0x0;
		i2c_node_info.i2c_mc[i][1].devid.slot1_addr = 0x1;

		i2c_node_info.i2c_mc[i][2].devid.slot0_addr = 0x2;
		i2c_node_info.i2c_mc[i][2].devid.slot1_addr = 0x3;

		i2c_node_info.i2c_mc[i][3].devid.slot0_addr = 0x6;
		i2c_node_info.i2c_mc[i][3].devid.slot1_addr = 0x7;
	}
#elif LOONGSON_3C5000L
	for (i = 0; i < TOT_NODE_NUM; i++) {
		i2c_node_info.i2c_mc[i][0].i2c_base = (i / 4 * 4 << NODE_OFFSET) | PHYS_TO_UNCACHED(0x1fe00120);
		i2c_node_info.i2c_mc[i][0].devid.slot0_addr = i % 4 * 2 + 0x0;
		i2c_node_info.i2c_mc[i][0].devid.slot1_addr = i % 4 * 2 + 0x1;
	}
#elif defined(LOONGSON_3E6000) //single support,dual and four TODO
	for (i = 0; i < TOT_NODE_NUM; i++) {
		for(j = 0; j < MC_PER_NODE; j++) {
			i2c_node_info.i2c_mc[i][j].i2c_base = (i / 4 * 4 << NODE_OFFSET) | PHYS_TO_UNCACHED(0x1fe00130 + (i / 2) * 0x8);

			if ((!(i % 2) && j < 2) || ((i % 2) && j > 1)) {
				i2c_node_info.i2c_mc[i][j].devid.slot0_addr = 0xff; // 0 1 2 3
				i2c_node_info.i2c_mc[i][j].devid.slot1_addr = 0xff; // 4 5 6 7
			} else {
				i2c_node_info.i2c_mc[i][j].devid.slot0_addr = j + 0x0; // 0 1 2 3
				i2c_node_info.i2c_mc[i][j].devid.slot1_addr = j + 0x4; // 4 5 6 7
			}

		}
	}
#elif LOONGSON_3D6000 //single support,dual and four TODO
	for (i = 0; i < TOT_NODE_NUM; i++) {
		for(j = 0; j < MC_PER_NODE; j++) {
			if(i % 2)
				i2c_node_info.i2c_mc[i][j].i2c_base = (i / 2 * 2 << NODE_OFFSET) | PHYS_TO_UNCACHED(0x1fe00138 - (j / 2) * 0x8);
			else
				i2c_node_info.i2c_mc[i][j].i2c_base = (i / 2 * 2 << NODE_OFFSET) | PHYS_TO_UNCACHED(0x1fe00130 + (j / 2) * 0x8);

			i2c_node_info.i2c_mc[i][j].devid.slot0_addr = j + 0x0;
			i2c_node_info.i2c_mc[i][j].devid.slot1_addr = j + 0x4;
		}
	}
#elif LOONGSON_3C6000
	for (i = 0; i < TOT_NODE_NUM; i++) {
		for(j = 0; j < MC_PER_NODE; j++) {
			i2c_node_info.i2c_mc[i][j].i2c_base = (i << NODE_OFFSET) | PHYS_TO_UNCACHED(0x1fe00130);
			i2c_node_info.i2c_mc[i][j].devid.slot0_addr = j + 0x0;
			i2c_node_info.i2c_mc[i][j].devid.slot1_addr = j + 0x4;
		}
	}
#else
	for (i = 0; i < TOT_NODE_NUM; i++) {
		for(j = 0; j < MC_PER_NODE; j++) {
			i2c_node_info.i2c_mc[i][j].i2c_base = (i << NODE_OFFSET) | PHYS_TO_UNCACHED(0x1fe00120);
			i2c_node_info.i2c_mc[i][j].devid.slot0_addr = j * 2 + 0x0;
			i2c_node_info.i2c_mc[i][j].devid.slot1_addr = j * 2 + 0x1;
		}
	}
#endif
}
#ifdef LOONGSON3A5000 
static uint8_t CheckPowerStatus(void)
{
	uint32_t system_s;
	system_s = readl(LS7A_ACPI_PMCON_RTC_REG);
	pr_info("ACPI_PMCON_RTC_REG = 0x%x\n",system_s);
	if (system_s & (0x1 << 1)) { // Check if PWR_FLR is 1.
		readl(LS7A_ACPI_PMCON_RTC_REG) |= (0x1 << 1);
		pr_info("Resume from G3.\n");
		return -1;
	}
	system_s = readl(LS7A_ACPI_PM1_STS_REG);
	pr_info("ACPI_PM1_STS_REG = 0x%x\n",system_s);
	if (system_s & (0x1 << 11)) { // Check if PRBTNOR_STS is 1.
		readl(LS7A_ACPI_PM1_STS_REG) |= (0x1 << 11);
		pr_info("Power Button Override Event. Resume from G2.\n");
		return -1;
	}
	pr_info("Power Status Ok\n");
	return 0;
}
#endif

void chip_feature_init()
{
	ls3c_cfg_t.config = &ls7a_cfg_t.config;
	ls3c_cfg_t.config->ls3a_chip_name = STR(CPU_TYPE);
	ls3c_cfg_t.config->ls7a_chip_name = STR(BRIDGE_TYPE);
	ls3c_cfg_t.config->ls3a_chip_type = CAT(LS,CAT(CPU_TYPE,_CHIP_TYPE));
	ls3c_cfg_t.config->ls7a_chip_type = CAT(LS,CAT(BRIDGE_TYPE,_CHIP_TYPE));

	ls3c_cfg_t.config->ls3a_node_num = TOT_NODE_NUM;
	ls3c_cfg_t.config->ls7a_node_num = TOT_7A_NUM;

	ls3c_cfg_t.config->pci_conf_base = PCIE_CONF_BASE;
	ls3c_cfg_t.config->ls7a_con_type = CHIPSET_MODE;
	ls3c_cfg_t.config->speedup       = 0;

#ifdef LS7A_2WAY_CONNECT
	ls3c_cfg_t.config->ls7a_2way_connect = 1;
#else
	ls3c_cfg_t.config->ls7a_2way_connect = 0;
#endif
	ls3c_cfg_t.config->ls7a_ht64 = HT64;
}

void core_exec(uint32_t core_id, uint64_t param, void *func)
{
	uint64_t node = core_id / 32;
	uint32_t sub_node = (core_id % 32) / 4;
	uint32_t core_index = (core_id & 0x3);
	uint64_t base = PHYS_TO_UNCACHED(0x1fe01000 | (node << 44) | (sub_node << 16) | (core_index << 8));
	pr_info("---------- %d's job is ready!\n", core_id);
	readl(PHYS_TO_UNCACHED(0x1fe01040)) = 0x80000000 | (core_id << 16);
	while(readl(base + 0x38) != 0x1234);
	readq(PHYS_TO_UNCACHED(0x1fe01020)) = 0;
	/*A1_OFF*/
	*(volatile unsigned long *)(base + 0x38) = param;
	/*FN_OFF*/
	*(volatile unsigned long *)(base + 0x20) = func;

	while(!readq(PHYS_TO_UNCACHED(0x1fe01020)));
	pr_info("---------- %d's job is done!\n", core_id);
}

void cache_stage()
{
	uint64_t i;
#ifdef LS7A2000
	pr_info("VERSION: ls7a.a %s\n", ls7a_bin_version);
#endif
	pr_info("run in cache!\n");

	chip_feature_init();
#ifdef LOONGSON_3C6000
#if (TOT_NODE_NUM > 1)
	lcl_port_set(3);
	lcl_pre_init();
	lcl_init(3);
#endif
	fix_ls3c6000_gpio_to_reset();
	for (i = 0; i < TOT_NODE_NUM; i++) {
		set_gpio_reset_pcie(i);
		init_3c6000_pcie(i);
#if !defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 4)
		i++;
#endif
	}
	pcie_bridge_init();
#else
	cpu_ht_link();
	ls3a7a_ht_linkup();
#endif

	ls7a_fix_addr();
	mm_feature_init();

	ls7a_feature_init();
	spd_i2c_init();
	ddr4_init(TOT_NODE_NUM, &mm_ctrl_info);
	pr_info("mem init done!\n");
#ifdef LOONGSON3A5000 
	if(CheckPowerStatus())
	    readl(LS7A_ACPI_PM1_CNT_REG) &= (~(0x7 << 10));
#endif
	ls7a_init();
	pr_info("ls7a init done!\n");

	if (((readl(LS7A_ACPI_PM1_CNT_REG) >> 10) & 0x7) == SLEEP_TYPE_S3) {
#ifdef LOONGSON_3C6000
		readl(0x80000efdfe000050) = 0xd17005;
#endif
		realinit_loongarch();
	} else {
		init_loongarch();
	}
}
