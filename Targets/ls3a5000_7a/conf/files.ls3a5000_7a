# $Id: files.ls7a
#
# ls7a Target specific files
#
file    Targets/ls3a5000_7a/loongson/cpulib.S
file    Targets/ls3a5000_7a/loongson/serial.S
file	Targets/ls3a5000_7a/loongson/tgt_machdep.c
file	Targets/ls3a5000_7a/loongson/resume.c
file	Targets/ls3a5000_7a/loongson/tgt_bootparam.c

#ls7a used same file with 3A3000_7A branch
file	Targets/ls3a5000_7a/pci/pci_machdep.c
file	Targets/ls3a5000_7a/pci/ls7a_pci.c
file	pmon/arch/loongarch/ls7a/ls7a_irq.c

define	localbus { [base = -1 ] }
device	localbus
attach	localbus at mainbus
file	Targets/ls3a5000_7a/dev/localbus.c		localbus
file	Targets/ls3a5000_7a/dev/fl_env.c
#file	Targets/ls3a5000_7a/dev/eeprom.c
file	Targets/ls3a5000_7a/dev/i2c-ls.c
file	Targets/ls3a5000_7a/dev/ls7a_gpio.c
file	Targets/ls3a5000_7a/dev/w83795.c
file	Targets/ls3a5000_7a/dev/superio.c
file	Targets/ls3a5000_7a/dev/hda_verb.c
file	Targets/ls3a5000_7a/dev/dvfs.c

#dc
file	Targets/ls3a5000_7a/dev/dc.c
file	Targets/ls3a5000_7a/dev/ls7a_gmac_mac_init.c
file	Targets/ls3a5000_7a/dev/ls7a_spi_w.c
file	Targets/ls3a5000_7a/dev/load_dtb.c		cmd_dtb
file	Targets/ls3a5000_7a/dev/signal_test.c

#Gmac
file	sys/dev/gmac/synopGMAC_Dev.c
file	sys/dev/gmac/synopGMAC_network_interface.c
file	sys/dev/gmac/synopGMAC_pci_bus_interface.c
file	sys/dev/gmac/synopGMAC_plat.c
file	Targets/ls3a5000_7a/dev/gmac_test.c

file	pmon/arch/loongarch/ls3a/dma_coherent.c

device	syn: ether, ifnet
attach	syn at localbus
file	sys/dev/gmac/if_gmac.c gmac

# Ethernet driver for Discovery ethernet
device	gt: ether, ifnet, ifmedia
attach	gt at localbus
file	sys/dev/ic/if_gt.c			gt

#
# Media Indepedent Interface (mii)
#
include "sys/dev/mii/files.mii"

# Various PCI bridges

include "sys/dev/pci/files.pci"

#For 7A pci gmac
device	pcisyn:ether, ifnet
attach	pcisyn at pci

#
# Machine-independent ATAPI drivers
#

include "sys/dev/ata/files.ata"

#
# SCSI framework
#
include "sys/scsi/files.scsi"

#
# Custom application files
#
include "pmon/custom/files.custom"

#
# Multi Processors application files
#
include "pmon/arch/loongarch/acpi/mp/files.mp"
