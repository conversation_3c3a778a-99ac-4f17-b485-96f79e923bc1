#
# LS3C5000
#
machine		ls3a5000_7a	loongarch	# CPU Architecture, Platform

include "conf/LOONGSON_PUBILC"

#
# Platform options
#
option		LS_STR
option		LS3_HT			        # Enable the IO cache coherent of HT
option		SOFT_CLKSEL
option		NODE_OFFSET=44
option		BOOTCORE_ID=0
option		HIGH_MEM_WIN_BASE_ADDR=0x80000000
option		BONITO_100M
#option		BONITO_25M
option		HT0_FREQ=1600
#option		DDR3_MODE
option		DDR_FREQ=700
option		DDR_FREQ_2SLOT=600   # Frequency of dual slot memory with one mc

#####################################################################
#                        3C5000x1                                   #
#####################################################################
option		CORE_FREQ=2200
option		MC_PER_NODE=4	     # 3C5000L:1 3A5000/3A6000:2 3C5000/3D5000:4
#option		MCC			         # memory cache coherency mode, recommend with TOT_NODE_NUM >= 2
option		TOT_NODE_NUM=1
option		CORES_PER_NODE=16
option		LOONGSON3A5000
option		LOONGSON_3C5000     # LOONGSON3A5000 must be selected if this option is selected
option		FLAT_MODE
option		CHIPSET_MODE=0       # 0: HT link; 1: Connected to 7A via PCIE; 2: Connected to 3C via PCIE

#####################################################################
#                        3D5000x4                                   #
#####################################################################
# option		CORE_FREQ=2000
# option		MC_PER_NODE=4	     # 3C5000L:1 3A5000/3A6000:2 3C5000/3D5000:4
# option		MCC			         # memory cache coherency mode, recommend with TOT_NODE_NUM >= 2
# option		TOT_NODE_NUM=8
# option		CORES_PER_NODE=16
# option		LOONGSON3A5000
# option		LOONGSON_3C5000     # LOONGSON3A5000 must be selected if this option is selected
# option		LOONGSON_3D5000     # LOONGSON3C5000 must be selected if this option is selected
# option		FLAT_MODE

#####################################################################
#                        3C5000Lx8                                  #
#####################################################################
# option		CORE_FREQ=2000
# option		MC_PER_NODE=1	     # 3C5000L:1 3A5000/3A6000:2 3C5000/3D5000:4
# option		MCC			         # memory cache coherency mode, recommend with TOT_NODE_NUM >= 2
# option		TOT_NODE_NUM=8
# option		CORES_PER_NODE=4
# option		LOONGSON3A5000

#####################################################################
#                        7A2000                                     #
#####################################################################
option		TOT_7A_NUM=1         # (TOT_7A_NUM == 2) should change pcietree.asl
#option		LS7A_2WAY_CONNECT
option		HT64=0				 # set to 1 only if defined LOONGSON_3D5000 and TOT_NODE_NUM=8
option		REBOOT2LINK=0		 # set to 1 will cause reboot when the pcie link status is incorrect
option		LS7A			     # for 7A
option		LS7A2000		     # for 7A2000
option		PCIE_CONF_BASE="0xefe00000000"

#option		LS7A_CAN
#option		USE_I2S_FUNCTION 
#option		LS132_CORE
#option		LS3A_GPIO
option		BEEP_NEW=1
option		AVS_SUPPORT
select		ls_rtc

#####################################################################
#                        Board                                      #
#####################################################################
option		QR_3C5000_SINGL

#option     QUICK_START

#option     EFI_DEBUG_LOG	# This debugging macro must be used to debug EFI at the firmware stage.
							# Opening it may cause the system to fail to start normally
#select		efi_support
#option		EFI_BOOT=1

option		SMBIOS_SUPPORT
option		ACPI_SUPPORT
select		acpi_support

#
# VGA option
#
#option		USE_BMC
option		VGA_NO_ROM
option		VGA_BASE=0x1e000000
option		VRAM_SIZE=128
#option		DEBUG_EMU_VGA
#option		X800x600
option		LS_FB_XSIZE=640
option		LS_FB_YSIZE=480
option		CONFIG_VIDEO_16BPP

#
# GPU driver selection. Selects for video
# Disable all options below to disable gpu driver 
# Enable all options below to enble gpu driver 
#
select		mod_x86emu_int10
select		mod_framebuffer
select		mod_vesa
select		mod_vgacon
#option		VESAFB

#
#  Now the Machine specification
#
mainbus0	at root
localbus0	at mainbus0
#loopdev0	at mainbus0
#fd0		at mainbus0
pcibr0		at mainbus0
pcibr1		at mainbus0
pci*		at pcibr?
ppb*		at pci? dev ? function ?	# PCI-PCI bridges
pci*		at ppb? bus ?

ahci*		at pci? dev ? function ?
ahci_sd*	at ahci?
ahci_cdrom*	at ahci?

#
# USB
#
#uhci*		at pci? dev ? function ?
ohci*		at pci? dev ? function ?	# OHCI
usb*		at ohci?
pcixhci*    at pci? dev ? function ?
xhci*       at pcixhci?
usb*        at xhci?
#usbnet*    at xhci?
select      mod_usb_xhci

#
# Pseudo devices
#
pseudo-device	loop	1	# network loopback

#
# Networking Devices
#
#gt0		at localbus? base 4
#gt1		at localbus? base 5
#gt2		at localbus? base 6
# fxp normally only used for debugging (enable/disable both)
#fxp0		at pci? dev ? function ?	# Intel 82559 Device
#inphy*		at mii? phy ?			    # Intel 82555 PHYs
#brgphy*	at mii? phy ?		        # Broadcom PHYs
#rtl*		at pci? dev ? function ?
#rtk*		at pci? dev ? function ?
igb*		at pci? dev ? function ?	# Intel 82576
select		igb1
bnx*		at pci? dev ? function ?	# BCM5709S
#rte*		at pci? dev ? function ?
#option		RTL8111
pcisyn0		at pci? dev ? function ?
pcisyn1		at pci? dev ? function ?
select		gmac
option		USE_GMAC_NUM=2
option		GMAC_USE_FLASH_MAC
#select		e100

#
# IDE controllers
#
option		IDE_DMA
pciide*		at pci ? dev ? function ? flags 0x0000

#
# IDE hard drives
#
wd*		    at pciide? channel ? drive ? flags 0x0000
#wd0		at pciide? channel ? drive ? flags 0x0000
#cmdide*	at pci ? dev ? function ? flags 0x0000
#wd*		at cmdide? channel ? drive ? flags 0x0000

pcinvme*	at pci? dev ? function ?
nvme*		at pcinvme?

#
# LSI MegaRAID SAS RAID controllers
#
mfi*		at pci?
scsibus*	at mfi?
sd*		    at scsibus? target ? lun ?
# SCSI		RAID disk drive support
select		scsi_sd
select		raw_ether
# SCSI support
#siop*		at pci? dev ? function ?	# Symbios/NCR 53c...
#scsibus*	at siop?
#sd*		at scsibus? target ? lun ?
#cd*		at scsibus? target ? lun ?
#option		CONFIG_LSI_9260			    # for LSI_9260-8i(2108) RAID card support

ide_cd*		at pciide? channel ? drive ? flags 0x0001

#option		USE_ENVMAC
#option		FLOATINGPT
option		WDC_NORESET
