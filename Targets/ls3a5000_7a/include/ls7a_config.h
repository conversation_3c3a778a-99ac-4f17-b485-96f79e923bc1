#ifndef _LS7A_CONFIG_H_
#define _LS7A_CONFIG_H_
#include <platform.h>
//#define DEBUG_HT1
#ifdef  DEBUG_HT1
//#define PRINT_HT1_REG
//#define DEBUG_HT1_PARAM
#endif
//#define XP_BOARD
//#define QR_BOARD
#define CHECK_HT_PLL_MODE
#define CHECK_HT_PLL_LOCK
#define CHECK_7A_HT_PLL_LOCK

#define HT1_RECONNECT   1
//HT GEN1.0/3.0 cfg
#define HT1_GEN_CFG     3
//HT1 width cfg
#if defined(LS7A_2WAY_CONNECT) || (HT1_GEN_CFG == 1)
#define HT1_WIDTH_CFG   HT_WIDTH_CTRL_8BIT  //only support 8 bit
#else
#if	(TOT_NODE_NUM >= 8)
#define HT1_WIDTH_CFG   HT_WIDTH_CTRL_8BIT
#else
#define HT1_WIDTH_CFG   HT_WIDTH_CTRL_16BIT
#endif
#endif
#define LS3A5000_INDUSTRIAL_GRADE          ((chip_ver() == 0x30) && ((chip_grade() == 0x8) || (chip_grade() == 0x9) || (chip_grade() == 0xa) || (chip_grade() == 0xb)))
//HT1 freq cfg
#if (HT1_GEN_CFG == 3)
#ifdef LS7A2000
#define LS7A_HT1_HARD_FREQ_CFG  HT_GEN3_FREQ_CTRL_3200M
#if defined(LOONGSON_3C5000) || defined(LOONGSON_3A6000)
#define LS3A_HT1_HARD_FREQ_CFG  HT_GEN3_FREQ_CTRL_3200M
#else
#define LS3A_HT1_HARD_FREQ_CFG  (((chip_ver() == 0x41) || (chip_ver() == 0x42)) ? HT_GEN3_FREQ_CTRL_800M : HT_GEN3_FREQ_CTRL_3200M) //3200
#endif
#define LS7A_HT1_SOFT_FREQ_CFG  ((LS3A5000_INDUSTRIAL_GRADE ? LS7A2000_HT_PLL_2400M : LS7A2000_HT_PLL_3200M) | (0x1 << 1))
#define LS3A_HT1_SOFT_FREQ_CFG  ((LS3A5000_INDUSTRIAL_GRADE ? LS3A5000DA_HT_PLL_2400M : LS3A5000_HT_PLL_3200M) | (0x1 << 1))
#else
#define LS7A_HT1_HARD_FREQ_CFG  HT_GEN3_FREQ_CTRL_1600M
#if defined(LOONGSON_3C5000) || defined(LOONGSON_3A6000)
#define LS3A_HT1_HARD_FREQ_CFG  HT_GEN3_FREQ_CTRL_1600M
#else
#define LS3A_HT1_HARD_FREQ_CFG  (((chip_ver() == 0x41) || (chip_ver() == 0x42)) ? HT_GEN3_FREQ_CTRL_400M : HT_GEN3_FREQ_CTRL_1600M)
#endif
#define LS7A_HT1_SOFT_FREQ_CFG  (LS7A_HT_PLL_1600M | (0x1 << 1))
#define LS7A_HT1_SOFT_FREQ_CFG_C  (LS7A_C_HT_PLL_1600M | (0x1 << 1))
#define LS3A_HT1_SOFT_FREQ_CFG  ((LS3A5000_INDUSTRIAL_GRADE ? LS3A5000DA_HT_PLL_1600M : LS3A5000_HT_PLL_1600M) | (0x1 << 1))
#endif
#else
#define LS7A_HT1_HARD_FREQ_CFG  HT_GEN1_FREQ_CTRL_800M
#define LS3A_HT1_HARD_FREQ_CFG  HT_GEN1_FREQ_CTRL_200M
//in HT GEN1 mode, define PLL freq to request freq x 2, for example, if you want to use HT1 800M, define HT_PLL_1600M
#define LS7A_HT1_SOFT_FREQ_CFG  (LS7A_HT_PLL_1600M | (0x1 << 1))
#define LS7A_HT1_SOFT_FREQ_CFG_C  (LS7A_C_HT_PLL_1600M | (0x1 << 1))
#define LS3A_HT1_SOFT_FREQ_CFG  ((LS3A5000_INDUSTRIAL_GRADE ? LS3A5000DA_HT_PLL_1600M : LS3A5000_HT_PLL_1600M) | (0x1 << 1))
#endif

#if (TOT_NODE_NUM > 1)
#define LS7A_GRAPHICS_DISABLE   1
#else
#define LS7A_GRAPHICS_DISABLE   0
#endif

/* Functions require SUPPORT_HIGH_PERFORMANCE=1 */
#define LS7A_VPU_ENABLE 1
#define LS7A_RIO_ENABLE 1

//staticly disable some PCIE Ports, no matter whether there is device
#define LS7A_PCIE_F0_DISABLE    0
#define LS7A_PCIE_F1_DISABLE    0
#define LS7A_PCIE_H_DISABLE     0
#define LS7A_PCIE_G0_DISABLE    0
#define LS7A_PCIE_G1_DISABLE    0

#if (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1)
#define LS7A_ANOTHER_PCIE_F0_DISABLE    0
#define LS7A_ANOTHER_PCIE_F1_DISABLE    0
#define LS7A_ANOTHER_PCIE_H_DISABLE     0
#define LS7A_ANOTHER_PCIE_G0_DISABLE    0
#define LS7A_ANOTHER_PCIE_G1_DISABLE    0
#endif


#define LS7A_GMAC0_DISABLE  0
#ifdef LS7A2000
#define LS7A_SATA_DISABLE   0
#define LS7A_SATA_PORT0_DISABLE		0
#define LS7A_SATA_PORT1_DISABLE		0
#define LS7A_SATA_PORT2_DISABLE		0
#define LS7A_SATA_PORT3_DISABLE		0
#define LS7A_XHCI_DISABLE   0
#define LS7A_IOMMU_DISABLE  1
#ifdef USE_I2S_FUNCTION
#define	LS7A_HDA_I2S_SEL		2 //0:GPIO 1:HDA0 2:I2S 3:HDA1
#else
#define	LS7A_HDA_I2S_SEL		3 //0:GPIO 1:HDA0 2:I2S 3:HDA1
#endif
#define LS7A_SATA1_DISABLE  1
#define LS7A_XHCI1_DISABLE  1
#define LS7A_GMAC1_DISABLE  1
#define LS7A_IOMMU1_DISABLE 1
#else
#define LS7A_SATA0_DISABLE  0
#define LS7A_SATA1_DISABLE  (LS7A_SATA0_DISABLE | 0)
#define LS7A_SATA2_DISABLE  (LS7A_SATA1_DISABLE | 0)
#define LS7A_GMAC1_DISABLE  (LS7A_GMAC0_DISABLE | 0)
#endif
#define LS7A_USB0_DISABLE   0
#define LS7A_USB1_DISABLE   0
#define LS7A_LPC_DISABLE    0

//#define USE_PCIE_PAD_REFCLK
//#define USE_SATA_PAD_REFCLK
#define USE_USB_SYS_REFCLK

#if (!LS7A_GRAPHICS_DISABLE) && ((TOT_NODE_NUM < 8) || !defined(TY_MULTI_BOARD))
#ifdef LS7A2000
#define LS7A_GMEM_DISABLE	0
#define LS7A_GPU_DISABLE	0
#define DEBUG_GMEM		0
#else
#define LS7A_GMEM_CFG	1
#define DEBUG_GMEM	0
#endif
#else
#ifdef LS7A2000
#define LS7A_GMEM_DISABLE	1
#define LS7A_GPU_DISABLE	1
#else
#define LS7A_GMEM_CFG	0
#endif
#endif

#ifndef LS7A_CODE_CLOSURE
#if     (TOT_NODE_NUM <= 4)
#if	(defined(LS7A_2WAY_CONNECT) || (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1))
#ifdef LOONGSON_3D5000
	char ls7a_link_id_buf[2] = {0, 2};
#else
	char ls7a_link_id_buf[2] = {0, 1};
#endif
#else
	char ls7a_link_id_buf[1] = {0}; //3C6000
#endif
#elif (TOT_NODE_NUM == 8)
#if (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1)
#ifdef LOONGSON_3D5000
	char ls7a_link_id_buf[2] = {0, 2};
#else
	char ls7a_link_id_buf[2] = {0, 5};
#endif
#else
#ifdef LS7A_2WAY_CONNECT
	char ls7a_link_id_buf[2] = {0, 5};
#else
	char ls7a_link_id_buf[1] = {0};
#endif
#endif
#elif (TOT_NODE_NUM == 16)
#if (TOT_7A_NUM == 2) || (CHIPSET_MODE == 1)
#ifdef LS7A_2WAY_CONNECT
	char ls7a_link_id_buf[4] = {0, 5, 0xa, 0xf};
#else
	char ls7a_link_id_buf[2] = {0, 5};
#endif
#else
#ifdef LS7A_2WAY_CONNECT
	char ls7a_link_id_buf[2] = {0, 0xa};
#else
	char ls7a_link_id_buf[1] = {0};
#endif
#endif
#endif


//3C6000 don't use this macro
#if	(TOT_NODE_NUM == 1)
uint64_t DMA_NODE_ID_OFFSET = 44;
#elif	(TOT_NODE_NUM <= 4)
uint64_t DMA_NODE_ID_OFFSET = 38;
#elif	(TOT_NODE_NUM == 8)
uint64_t DMA_NODE_ID_OFFSET = 37;
#elif	(TOT_NODE_NUM == 16)
uint64_t DMA_NODE_ID_OFFSET = 36;
#endif
#endif /* LS7A_CODE_CLOSURE */

#ifdef LS7A_2WAY_CONNECT
#if     (TOT_NODE_NUM <= 4)
#define	DMA_DEST_HT		(0xa << 16)
#elif (TOT_NODE_NUM == 8)
#define	DMA_DEST_HT		(0xf0 << 16)
#elif (TOT_NODE_NUM == 16)
#define	DMA_DEST_HT		(0xff00 << 16)
#endif
#else
#define	DMA_DEST_HT		0
#endif

/*
 * LS7A RESOURCE CONFIG
 */
#define USE_OUTSIDE_REFCLK	0
#define USE_INSIDE_REFCLK	1

/* HT */
typedef struct {
	uint8_t ls3a_node_num;
	uint8_t ls7a_node_num; /* HT link 7A number */
	uint8_t ls3a_chip_type;
	uint8_t ls7a_chip_type;
	char    *ls3a_chip_name;
	char    *ls7a_chip_name;
	uint8_t ls7a_2way_connect;
	uint8_t ls7a_con_type; /* another 7A2000 pcie connections type |0 HT |1 F|2 H pcie| */
	uint64_t pci_conf_base;
	uint8_t ls7a_ht64;
	uint8_t speedup;
} ls3a7a_config_table;

enum {
	LS7A_PLL0 = 0,
	LS7A_PLL1,
	LS7A_PLL2,
	LS7A_PLL3,
	LS7A_PLL4,
	LS7A1_PLL0,
	LS7A1_PLL1,
	LS7A1_PLL2,
	LS7A1_PLL3,
	LS7A1_PLL4,
};
/* PLL */
typedef struct {
	uint32_t pll_val;
	uint32_t div;
} ls7a_pll_table;

typedef struct {
	uint32_t val_0;
	uint32_t val_4;
	uint32_t val_8;
} pcie_phy_param_t;
#define LS7A_PCIE_X1_MODE	0x1
#define LS7A_PCIE_X4_MODE	0x4
#define LS7A_PCIE_X8_MODE	0x8
#define LS7A_PCIE_X16_MODE	0x10
#define LS7A_PCIE_ADAPT_MODE	0xe
#define LS7A_PCIE_FLIP_EN_P0	0x100
#define LS7A_PCIE_FLIP_EN_P1	0x200
#ifdef  LS7A1000
#define LS7A_PCIE_FLIP_EN	0x10
#else
#define LS7A_PCIE_FLIP_EN	0x100
#endif
#define LS7A_PCIE_NO_POWERDOWN	0x20
#define LS7A_PCIE_FORCE_X1_MODE	(LS7A_PCIE_X1_MODE | LS7A_PCIE_NO_POWERDOWN)
#define LS7A_PCIE_FORCE_X4_MODE	(LS7A_PCIE_X4_MODE | LS7A_PCIE_NO_POWERDOWN)
#define LS7A_PCIE_FORCE_X8_MODE	(LS7A_PCIE_X8_MODE | LS7A_PCIE_NO_POWERDOWN)
#define LS7A_PCIE_GEN1_MODE	0x1
#define LS7A_PCIE_GEN2_MODE	0x2
#define LS7A_PCIE_GEN3_MODE	0x3
#define LS7A_PCIE_EQ_MODE0	0x0
#define LS7A_PCIE_EQ_MODE1	0x1
#define LS7A_PCIE_EQ_MODE2	0x2
#define LS7A_PCIE_RESET_DELAY 0x1000000
/* PCIE */
#ifdef LS7A2000
typedef struct {
	const char* name;
	int port_clk_disable;
	int ctrl_disable;
	int preset;
	int gen;
	int lane_mode;
	int eq_mode;
} pcie_desc;
#else
typedef struct {
	uint64_t bus_base;
	int first_port_num;
	const char* name;
	int port_bits;
	int disable;
	pcie_phy_param_t *phy_param;
	int mode;
} pcie_desc;
#endif

#define ENABLE_PCIEX8_CAL	0x10
#define PCIE_MAX_SIZE		12
typedef struct {
	uint8_t ref_clk;
	uint8_t x8_cal_en;
	uint64_t pcie_cfg_buffer[PCIE_MAX_SIZE];
	uint32_t num;
#ifdef LS7A2000
	uint8_t reboot_to_link;
	uint32_t reset_delay;
#endif
	pcie_desc* controller;
} ls7a_pcie_table;

/* SATA */
#ifdef LS7A2000
typedef struct {
	uint8_t disable;
	union {
		struct {
			uint8_t port0 : 1;
			uint8_t port1 : 1;
			uint8_t port2 : 1;
			uint8_t port3 : 1;
		};
		uint8_t disable_port;
	};
	uint8_t swing_gen1;
	uint8_t preemph_gen1;
	uint8_t swing_gen2;
	uint8_t preemph_gen2;
	uint8_t swing_gen3;
	uint8_t preemph_gen3;
} sata_desc;
#else
typedef struct {
	uint8_t disable;
	uint8_t ovrd_en;	//if OVRD_SATA_PHY
	uint32_t ovrd_val;
} sata_desc;
#endif

typedef struct {
	uint8_t ref_clk;
	sata_desc* controller;
} ls7a_sata_table;

/* USB */
#ifdef LS7A2000
typedef struct {
	uint8_t disable;
	union {
		uint64_t param_val;
		struct {
			uint32_t afetrim   : 32;
			uint8_t ihstx_port : 4;
			uint8_t zhsdrv_port : 2;
		} param_t;
	} usb2_param_port[6];
} usb_desc;
#else
typedef struct {
	uint8_t disable;
	uint8_t port0_ovrd_en;
	uint32_t port0_ovrd_val;
	uint8_t port1_ovrd_en;
	uint32_t port1_ovrd_val;
	uint8_t port2_ovrd_en;
	uint32_t port2_ovrd_val;
	uint8_t clk_frescale;
} usb_desc;
#endif

typedef struct {
	uint8_t ref_clk;
	usb_desc* controller;
} ls7a_usb_table;

/* GMAC */
typedef struct {
	uint8_t disable;
} gmac_desc;

/* IOMMU */
typedef struct {
    uint8_t disable;
} iommu_desc;
/* IOMMU */
typedef struct {
    iommu_desc* controller;
} ls7a_iommu_table;

typedef struct {
	uint8_t ref_clk;
	gmac_desc* controller;
} ls7a_gmac_table;

/* DISPLAY */
#ifdef LS7A2000
typedef struct {
	uint8_t graphics_disable;
	uint8_t gmem_disable;
	uint8_t gpu_disable;
	uint8_t vpu_enable;
} ls7a_dc_table;
#else
typedef struct {
	uint8_t graphics_disable;
	uint8_t gmem_cfg;
} ls7a_dc_table;
#endif

typedef struct {
	uint32_t min_rpm;
	uint32_t max_rpm;
} ls7a_fan_desc;

/* RAPID IO */
#define LSRIO_PORT_F1	0
#define LSRIO_PORT_G0	1

#define LSRIO_CLK_OUTSIDE      (1 << 0)
enum {
	LSRIO_SPEED_1_25 = 0,
	LSRIO_SPEED_2_5,
	LSRIO_SPEED_3_125,
	LSRIO_SPEED_5,
	LSRIO_SPEED_6_25,
};

/* MISC */
typedef struct {
	uint8_t	lpc_disable;
	ls7a_fan_desc fan;
} ls7a_misc_table;

/* Multiuse */
typedef struct {
	uint8_t	hda_i2s_sel;
} ls7a_multiuse_table;

/* Chip Info */
typedef struct ls7a_resource_table {
	ls3a7a_config_table	config;
	ls7a_pll_table		pll[10];
	ls7a_pcie_table		pcie;
	ls7a_sata_table		sata;
	ls7a_usb_table		usb;
	ls7a_gmac_table		gmac;
	ls7a_dc_table		dc;
	uint8_t			rio_enable;
	ls7a_misc_table		misc;
	ls7a_iommu_table	iommu;
	ls7a_multiuse_table	multiuse;
} ls7a_resource_table_t;

extern ls7a_resource_table_t ls7a_cfg_t;
extern char * ls7a_bin_version;

#define LS3A_NODE_NUM		(ls7a_cfg_t.config.ls3a_node_num)
//3C6000
#define LS3C6000_LCL_DEFAULT_SPEED    0x0
#define LS3C6000_LCL_GEN1             0x1
#define LS3C6000_LCL_GEN2             0x2
#define LS3C6000_LCL_GEN3             0x3
#define LS3C6000_LCL_GEN4             0x4

#define LS3C6000_PHY_MODE_1X16        0x0
#define LS3C6000_PHY_MODE_2X8         0x1
#define LS3C6000_PHY_MODE_4X4         0x2
#define LS3C6000_PHY_MODE_1X8_2X4     0x3
#define LS3C6000_PHY_DEFAULT          0x5

#define LS3C6000_PHY0_ADDR            0x20
#define LS3C6000_PHY1_ADDR            0x50
#define LS3C6000_PHY2_ADDR            0x28
#define LS3C6000_PHY3_ADDR            0x58

#define LS3C6000_PCIE_G0P0_DISABLE    1
#define LS3C6000_PCIE_G0P1_DISABLE    1
#define LS3C6000_PCIE_G0P2_DISABLE    1
#define LS3C6000_PCIE_G0P3_DISABLE    1

#define LS3C6000_PCIE_G1P0_DISABLE    1
#define LS3C6000_PCIE_G1P1_DISABLE    1
#define LS3C6000_PCIE_G1P2_DISABLE    1
#define LS3C6000_PCIE_G1P3_DISABLE    1

#define LS3C6000_PHY_PCIE_MODE        1
#define LS3C6000_PHY_LCL_MODE         0

#define LS3C6000_PCIE_RESETN_CH0      0
#define LS3C6000_PCIE_RESETN_CH1      0
#define LS3C6000_PCIE_RESETN_CH2      0
#define LS3C6000_PCIE_RESETN_CH3      0

#define LS3C6000_PCIE_PRESETN_CH0     0
#define LS3C6000_PCIE_PRESETN_CH1     0
#define LS3C6000_PCIE_PRESETN_CH2     0
#define LS3C6000_PCIE_PRESETN_CH3     0
#define LS3C6000_PCIE_PRESETN_CH4     0
#define LS3C6000_PCIE_PRESETN_CH5     0

#define LS3C6000_PCIE_MODE            0
#define LS3C6000_CHIPSET_MODE         1
#define LS3C6000_PHY_DISABLE          2

#define LS3C6000_PCIE_GEN1_MODE	      0x1
#define LS3C6000_PCIE_GEN2_MODE	      0x2
#define LS3C6000_PCIE_GEN3_MODE	      0x3
#define LS3C6000_PCIE_GEN4_MODE	      0x4

#define LS3C6000_PCIE_EQ_MODE0	      0x0
#define LS3C6000_PCIE_EQ_MODE1	      0x1
#define LS3C6000_PCIE_EQ_MODE2	      0x2

#define LS3C6000_PCIE_DISABLE_EQ23    0x1
#define LS3C6000_PCIE_ENABLE_EQ23     0x0

#define LS3C6000_PCIE_X0_MODE	      0x0
#define LS3C6000_PCIE_X1_MODE	      0x1
#define LS3C6000_PCIE_X4_MODE	      0x4
#define LS3C6000_PCIE_X8_MODE	      0x8
#define LS3C6000_PCIE_X16_MODE	      0x10

#define LS3C6000_PCIE_FLIP_DISABLE    0x0
#define LS3C6000_PCIE_FLIP_RX_EN      0x1
#define LS3C6000_PCIE_FLIP_TX_EN      0x2
#define LS3C6000_PCIE_FLIP_ENABLE	  0x3

#define PHY_INDEX(node_index, phy_index) (node_index * 4 + phy_index)

#define CTRL_INDEX(node_index, ctrl_index) (node_index * 8 + ctrl_index)

#define PCIE_G0P0 0
#define PCIE_G0P2 1
#define PCIE_G0P4 2
#define PCIE_G0P6 3

#define PCIE_G1P1 4
#define PCIE_G1P3 5
#define PCIE_G1P5 6
#define PCIE_G1P7 7

#define GPIO0     0
#define GPIO1     1
#define GPIO2     2
#define GPIO3     3

typedef struct {
    char name[10];
    uint8_t node_id;
    uint8_t phy_id;
    uint8_t phy_width;
    uint8_t phy_mode;
} ls3c6000_phy_desc;

typedef struct {
    uint8_t node_id;
    uint8_t phy_id;
    uint8_t input_bridge_en_man_pm; //[5:3]input_bridge_en_man_pm1_q;[2:0]input_bridge_en_man_pm0_q
    uint8_t load_hff_enn_q;
    uint8_t comp_tx_man;            //[7:4]comp_tx_man_q;[0]comp_tx_man_en_q
    uint8_t comp_rx_man;            //[7:1]comp_rx_man_q;[0]comp_rx_man_en_q
    uint8_t rxbuff_ctrl_man_q;      //[3] EQU_BOOST_EN;[0] DC_GAIN_EN
    uint8_t idx_man;                //[7:4]EQU_BOOST_IDX_MAN_Q;[3:0] DC_GAIN_IDX_MAN_Q
    uint8_t wait_sigdet            : 3;  // 0-4
    uint8_t reset_used_gpio        : 2;  // 0-3
} ls3c6000_phy_param_desc;

typedef struct {
	const char* name;
	int ctrl_mode;
	int ctrl_disable;
	int eq_mode;
	//int preset_search;
	int tx_preset;
	int rx_phint;
	int ltx_preset_g4;
	int rtx_preset_g4;
	int disable_eq23;
	int gen;
	int lane_flip_en;
	int lane_num;
	int lane_base;
	int lane_end;
	int phy_offset;
	int highperformance;
} ls3c6000_pcie_desc;

typedef struct {
	ls3a7a_config_table *config;
	int               use_new_irq;
	ls3c6000_phy_param_desc *phy_param;
	ls3c6000_phy_desc *phy_cfg;
	ls3c6000_pcie_desc *pcie_ctrl;
} ls3c_resource_table_t;

ls3c6000_phy_desc ls3c6000_phy_cfg[32] = {
#if (TOT_NODE_NUM == 1)
#ifdef LOONGSON_3B6000
#ifdef QR_BOARD
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
	{"QR_BOARD", node_id, phy_id, phy_width, phy_mode}
	PHY_DESC(0, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_DISABLE),
	PHY_DESC(0, 2, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_DISABLE),
#elif defined(ATKWA_3C7A_BOARD)
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
   {"qianru", node_id, phy_id, phy_width, phy_mode}

    PHY_DESC(0, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 1, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 2, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 3, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
#else
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
	{"XP_BOARD", node_id, phy_id, phy_width, phy_mode}
	PHY_DESC(0, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_DISABLE),
	PHY_DESC(0, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_DISABLE),
#endif
#else
#ifdef XP_BOARD
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
    {"XP_BOARD", node_id, phy_id, phy_width, phy_mode}

    PHY_DESC(0, 0, LS3C6000_PHY_MODE_1X8_2X4, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
#else
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
    {"AQ_BOARD", node_id, phy_id, phy_width, phy_mode}

    PHY_DESC(0, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X8_2X4, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
#endif
#endif
#elif defined(LOONGSON_3E6000) && (TOT_NODE_NUM == 8)
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
	{"tongyong", node_id, phy_id, phy_width, phy_mode}
	PHY_DESC(0, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(0, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(1, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(1, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(1, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(1, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(2, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(2, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(2, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(2, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(3, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(3, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(3, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(3, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(4, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(4, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(4, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(4, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(5, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(5, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(5, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(5, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(6, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(6, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(6, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(6, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(7, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
	PHY_DESC(7, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(7, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
	PHY_DESC(7, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
#elif defined(LOONGSON_3E6000) && (TOT_NODE_NUM == 4)
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
    {"TY_BOARD", node_id, phy_id, phy_width, phy_mode}

    PHY_DESC(0, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),

    PHY_DESC(1, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(1, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(1, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(1, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),

    PHY_DESC(2, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(2, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(2, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(2, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),

    PHY_DESC(3, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(3, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(3, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(3, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
#elif !defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 4)
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
    {"QR_BOARD", node_id, phy_id, phy_width, phy_mode}

    PHY_DESC(0, 0, LS3C6000_PHY_MODE_1X8_2X4, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),

    PHY_DESC(2, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(2, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(2, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(2, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
#elif defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 8)
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
    {"TY_BOARD", node_id, phy_id, phy_width, phy_mode}

    PHY_DESC(0, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),

    PHY_DESC(1, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(1, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(1, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(1, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),

    PHY_DESC(2, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(2, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(2, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(2, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),

    PHY_DESC(3, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(3, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(3, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(3, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),

    PHY_DESC(4, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(4, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(4, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(4, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),

    PHY_DESC(5, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(5, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(5, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(5, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),

    PHY_DESC(6, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(6, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(6, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(6, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),

    PHY_DESC(7, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(7, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(7, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(7, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
#elif defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 4)
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
    {"TY_BOARD", node_id, phy_id, phy_width, phy_mode}

    PHY_DESC(0, 0, LS3C6000_PHY_MODE_1X8_2X4, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),

    PHY_DESC(1, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(1, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(1, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(1, 3, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),

    PHY_DESC(2, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(2, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(2, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(2, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),

    PHY_DESC(3, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(3, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(3, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(3, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
#elif defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 2)
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
    {"XP_BOARD", node_id, phy_id, phy_width, phy_mode}

    PHY_DESC(0, 0, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 2, LS3C6000_PHY_MODE_2X8, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),

    PHY_DESC(1, 0, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(1, 1, LS3C6000_PHY_MODE_4X4, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(1, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(1, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
#else
#define PHY_DESC(node_id, phy_id, phy_width, phy_mode) \
    {"TY_BOARD", node_id, phy_id, phy_width, phy_mode}

    PHY_DESC(0, 0, LS3C6000_PHY_MODE_2X8,  LS3C6000_PHY_PCIE_MODE),
    PHY_DESC(0, 1, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 2, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
    PHY_DESC(0, 3, LS3C6000_PHY_MODE_1X16, LS3C6000_PHY_LCL_MODE),
#endif
};

ls3c6000_pcie_desc ls3c6000_pcie_cfg[64] = {
#if (TOT_NODE_NUM == 1)
#ifdef LOONGSON_3B6000
#ifdef QR_BOARD
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY2_ADDR, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  0, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, 0, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  8,  11, 0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  12, 15, 0, 1},
#elif defined(ATKWA_3C7A_BOARD)
    {"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
    {"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY2_ADDR, 1},
    {"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
    {"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY2_ADDR, 1},
    {"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY1_ADDR, 1},
    {"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY3_ADDR, 1},
    {"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY1_ADDR, 1},
    {"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY3_ADDR, 1},
#else   
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  0, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, 0, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  8,  11, 0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  12, 15, 0, 1},
#endif
#else
#if defined(XP_BOARD)
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  8,  11, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X4_MODE,  12, 15, LS3C6000_PHY0_ADDR, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
#elif defined(QR_BOARD)
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X8_MODE, 0,  7, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY2_ADDR, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE, 0,  7, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8, 15, LS3C6000_PHY3_ADDR, 1},
#else
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  8,  11, LS3C6000_PHY1_ADDR, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  12, 15, LS3C6000_PHY1_ADDR, 1},
#endif
#endif
#elif !defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 4)
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  8,  11, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X4_MODE,  12, 15, LS3C6000_PHY0_ADDR, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	{"G0P0", LS3C6000_PCIE_MODE, 	LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X4_MODE,  12, 15, LS3C6000_PHY0_ADDR, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
#elif defined(LOONGSON_3E6000) && (TOT_NODE_NUM == 8)
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  0, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE, LS3C6000_PCIE_X8_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},
	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
#elif defined(LOONGSON_3E6000) && (TOT_NODE_NUM == 4)
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  0, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  00, 00, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

#elif defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 8)
	/* node 0 */
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  8,  11, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X4_MODE,  12, 15, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	/* node 1 */
	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, 0, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X4_MODE,  12, 15, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  0,  0, 1},

	/* node 2 */
	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  0, 0, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	/* node 3 */
	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0, 0, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  0, 0, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	/* node 4 */
	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0, 0, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  0, 0, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	/* node 5 */
	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0, 0, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  0, 0, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	/* node 6 */
	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0, 0, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  0, 0, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	/* node 7 */
	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0, 0, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  0, 0, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

#elif defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 4)
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  8,  11, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X4_MODE,  12, 15, LS3C6000_PHY0_ADDR, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X4_MODE,  12, 15, LS3C6000_PHY0_ADDR, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE, 0,  7, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY3_ADDR, 1},

	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0, 0, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  0, 0, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	{"G0P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0, 0, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X0_MODE,  0, 0, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
#elif defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 2)
	{"G0P0", LS3C6000_CHIPSET_MODE, LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE0, 6, 6, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE, LS3C6000_PCIE_X8_MODE,  0,  7,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X8_MODE, 0,  7, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  8,  15, LS3C6000_PHY0_ADDR, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X8_MODE,  8, 15, LS3C6000_PHY2_ADDR, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  0, 0, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY3_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X0_MODE,  0,  0,  0, 1},

	{"G0P0", LS3C6000_PCIE_MODE, 	LS3C6000_PCIE_G0P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE, LS3C6000_PCIE_X16_MODE,  0,  15,  LS3C6000_PHY0_ADDR, 1},
	{"G0P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_ENABLE,  LS3C6000_PCIE_X16_MODE, 0,  15, LS3C6000_PHY2_ADDR, 1},
	{"G0P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X8_MODE,  0,  0, 0, 1},
	{"G0P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G0P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE,  LS3C6000_PCIE_X4_MODE,  0, 0, 0, 1},

	{"G1P0", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P0_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE, 0,  3, LS3C6000_PHY1_ADDR, 1},
	{"G1P1", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P1_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE, 4,  7, LS3C6000_PHY1_ADDR, 1},
	{"G1P2", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P2_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  8,  11, LS3C6000_PHY1_ADDR, 1},
	{"G1P3", LS3C6000_PCIE_MODE,    LS3C6000_PCIE_G1P3_DISABLE, LS3C6000_PCIE_EQ_MODE1, 5, 5, 6, 6, LS3C6000_PCIE_ENABLE_EQ23, LS3C6000_PCIE_GEN3_MODE, LS3C6000_PCIE_FLIP_DISABLE, LS3C6000_PCIE_X4_MODE,  12,  15, LS3C6000_PHY1_ADDR, 1},
#endif
};

ls3c6000_phy_param_desc ls3c6000_phy_param[32] = {
	/* node 0 */
	{0, 0, 0x1b, 0x7c, 0xa1, 0xff, 0x0, 0xf, 4, GPIO2},
	{0, 1, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	{0, 2, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO2},
	{0, 3, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	/* node 1 or 3C6000 dual */
	{1, 0, 0x1b, 0x7c, 0xa1, 0xff, 0x0, 0xf, 4, GPIO2},
	{1, 1, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	{1, 2, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO2},
	{1, 3, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	/* node 2 */
	{2, 0, 0x1b, 0x7c, 0xa1, 0xff, 0x0, 0xf, 4, GPIO2},
	{2, 1, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	{2, 2, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO2},
	{2, 3, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	/* node 3 */
	{3, 0, 0x1b, 0x7c, 0xa1, 0xff, 0x0, 0xf, 4, GPIO2},
	{3, 1, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	{3, 2, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO2},
	{3, 3, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	/* node 4 */
	{4, 0, 0x1b, 0x7c, 0xa1, 0xff, 0x0, 0xf, 4, GPIO2},
	{4, 1, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	{4, 2, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO2},
	{4, 3, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	/* node 5 */
	{5, 0, 0x1b, 0x7c, 0xa1, 0xff, 0x0, 0xf, 4, GPIO2},
	{5, 1, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	{5, 2, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO2},
	{5, 3, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	/* node 6 */
	{6, 0, 0x1b, 0x7c, 0xa1, 0xff, 0x0, 0xf, 4, GPIO2},
	{6, 1, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	{6, 2, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO2},
	{6, 3, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	/* node 7 */
	{7, 0, 0x1b, 0x7c, 0xa1, 0xff, 0x0, 0xf, 4, GPIO2},
	{7, 1, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
	{7, 2, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO2},
	{7, 3, 0x1b, 0x7c, 0x00, 0x00, 0x0, 0xf, 4, GPIO3},
};

ls3c_resource_table_t ls3c_cfg_t = {
	0,
	NEW_IRQ,
	ls3c6000_phy_param,
	ls3c6000_phy_cfg,
	ls3c6000_pcie_cfg
};
#endif
