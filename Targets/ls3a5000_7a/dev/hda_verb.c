#include <pmon.h>
#include <sys/malloc.h>
#include "hda.h"

#define CXT11880
#ifdef CXT11880
#define CXT11880_ID 0x14f11f87
unsigned int HdaVerbTableCxt11880[] = {
// Device ID：SN6140  DID:1F87，VID:14F1
// Node : SVID/SSID => 14F1/0101   Default
// Turn On SnM Mode
0x01C49100,
0x000F0000,
0x000F0000,
0x000F0000,
// Turn Off SnM Mode
0x000F0000,
0x01C49000,
0x000F0000,
0x000F0000,
// Softreset AFG
0x0017FF00,
0x0017FF00,
0x000F0000,
0x000F0000,
// Special note: needs to be re-do full Verb table when S3/S4 resume!
0x00172001,
0x00172101,
0x001722F1,
0x00172314,
// Node 16(Port A): 03211040(HP out)-Enable
0x01671C40,
0x01671D10,
0x01671E21,
0x01671F03,
// Node 17(port G): 91170110(SPK/Class_D)-Disable
0x01771CF0,
0x01771D01,
0x01771EF0,
0x01771F40,
// Node 18(Port B): 40F001F0(Mic/Line In)-Disable
0x01871CF0,
0x01871D01,
0x01871EF0,
0x01871F40,
// Node 19(Port D): 03A11030(Headset Mic)-Enable
0x01971C30,
0x01971D10,
0x01971EA1,
0x01971F03,
// Node 1A(port C): 95A60120(1st Dig-Mic I/F)-Enable
0x01A71C3E,
0x01A71D00,
0x01A71EA7,
0x01A71F95,
// Node 21(port I): 40F001F0(SPDI/F)-Disable
0x02171CF0,
0x02171D01,
0x02171EF0,
0x02171F40,
// Misc settings(GSMark by BIOS and Int-Mic On)
0x01B31201,       // Set Spk_Mute as Mute  0x01B31200
0x01C4C084,       // Set jack connector as normal open 
0x01C29006,       // Set HPF to 180Hz
0x01C4F0D3,       // Jack detection monitor window delay set to 2s, Board version V1.2
//0x01C4F0F2,       // Jack detection monitor window delay set to 0s, Board version V1.1
0x01C31005,       // Class D gain 
0x01C23540,       // SET MICBIAS
0x01C40408,       // Set Port-C to Analog

0xFFFFFFFF		  // Verb table ending
//===================================================================
//  20231030  First Version For Loongson Test Board
};
#else
//#define HDA_DEBUG
struct hda_pintbl {
	unsigned short nid;
	unsigned int val;
};
struct snd_hda_pin {
	unsigned int codec;             /* Codec vendor/device ID */
	const struct hda_pintbl *pins;  /* list of matching pins */
};

#define SND_HDA_PIN(_codec, _pins...) \
	{ .codec = _codec,\
	  .pins = (const struct hda_pintbl[]) { _pins, {0, 0}} \
	}

static const struct snd_hda_pin alc_pin_fixup_tbl[] = {
	SND_HDA_PIN(0x10ec0269,
		{0x1b, 0x02214c40},
		{0x15, 0x01014030}),
	SND_HDA_PIN(0x10ec0662,
		{}),
	SND_HDA_PIN(0x10ec0882,
		{0x1b, 0x02214c40},
		{0x15, 0x01014030}),
	SND_HDA_PIN(0x10ec0897,
		{0x11, 0x40130000},
		{0x12, 0x90A60140},
		{0x14, 0x01014010},
		{0x15, 0x411111F0},
		{0x16, 0x411111F0},
		{0x17, 0x411111F0},
		{0x18, 0x01A19030},
		{0x19, 0x02A19050},
		{0x1A, 0x0181303F},
		{0x1B, 0x02214020},
		{0x1C, 0x411111F0},
		{0x1D, 0x4034C601},
		{0x1E, 0x411111F0},
		{0x1F, 0x411111F0}),
	{}
};

int snd_hda_pick_pin_fixup(unsigned int codec_id,
                            const struct snd_hda_pin *pin_quirk)
{
	const struct snd_hda_pin *pq;
	int i = 0;
	for (pq = pin_quirk; pq->codec; pq++, i++) {
		if (codec_id != pq->codec) {
			continue;
		}
		return i;
	}
	return -1;
}
#endif

void hda_codec_set(void)
{
	int *corb_p, *corb_p0;
	corb_p = corb_p0 = (unsigned int*)pmalloc(sizeof(int) * 0x1000);
	if(corb_p == 0) {
		tgt_printf("HDA: can't alloc memory for corb rirb\n");
		return;
	}
	corb_p = (unsigned long)corb_p + (0x80 - ((unsigned long)corb_p & 0x7f)); //used to align adress
	int *rirb_p = (unsigned long)corb_p + 0x400;
	unsigned int corb_buf[80];
	struct snd_hda_pin *pq;
	struct hda_pintbl *ptr;
	int times = 10000;
	int i, j;
	unsigned int val;

	readl(PHYS_TO_UNCACHED(0xe0010010430)) |= (1 << 20);
	val = readl(PHYS_TO_UNCACHED(0xe0010010440));
	val = val & ~(3 << 11) | (1 << 11);
	readl(PHYS_TO_UNCACHED(0xe0010010440)) = val;
#ifdef HDA_DEBUG
	tgt_printf("corb_p = 0x%lx\n",corb_p);
	tgt_printf("rirb_p = 0x%lx\n",rirb_p);
#endif
	/*7A HDA device 7 function 0*/
	unsigned long base = ls_readl(((PHYS_TO_UNCACHED(0xefdfe000000)) | (0 << 16) | (7 << 11) | (0 << 9)) + 0x10) & ~0xf;
	base = PHYS_TO_UNCACHED(base);
#ifdef HDA_DEBUG
	tgt_printf("base = 0x%x\n",base);
#endif
	/*init hda controller*/
	hda_writel(base, GCTL, hda_readb(base, GCTL) & ~ICH6_GCTL_RESET);
	while ((hda_readw(base, GCTL) & 0x1) && times--);
	if (times < 0) {
		tgt_printf("HDA reset time out!\n");
		goto hda_out;
	}
	hda_writel(base, GCTL, hda_readb(base, GCTL) | ICH6_GCTL_RESET);
	hda_writew(base, STATESTS,0x1);
	hda_writew(base, GSTS,0x2);
	hda_writel(base, INTCTL,0xc0000010);
	hda_writel(base, CORBLBASE,(unsigned long)corb_p & 0xfffffff);
	hda_writeb(base, CORBCTL,ICH6_CORBCTL_RUN);
	hda_writeb(base, CORBSIZE,0x1);
	hda_writel(base, RIRBLBASE,(unsigned long)rirb_p & 0xfffffff);
	hda_writeb(base, RIRBCTL,ICH6_RBCTL_DMA_EN);
	hda_writeb(base, RIRBSTS,ICH6_RBSTS_OVERRUN);
	hda_writeb(base, RIRBSIZE,2);

	/*this code to get code ID*/
	ls_readl(corb_p + 1) = 0xf0000;
	delay(0x1000),
	hda_writew(base, CORBWP,1);
	times = 1000;
	while(!(hda_readb(base, RIRBSTS) & 0x1) && times--);
	if (times < 0) {
		tgt_printf("HDA rirb time out!\n");
		goto hda_out;
	}
	val = ls_readl(rirb_p + 2);
	hda_writeb(base, RIRBSTS, val | 0x1);
#ifdef CXT11880
	tgt_printf("Codec Vendor Id =  0x%x\n",val);
    if (CXT11880_ID == val) {
        unsigned int HdaVerbTableSize = sizeof (HdaVerbTableCxt11880) / sizeof (unsigned int);
	    tgt_printf("Write verb table to CORB!\n");
    	for(i = 2; i < HdaVerbTableSize+2; i++) {
    		ls_readl(corb_p + i) = HdaVerbTableCxt11880[i-2];
    		delay(0x1000),
    		hda_writew(base, CORBWP, i);
#ifdef HDA_DEBUG
    		tgt_printf("corb 0x%x\n", HdaVerbTableCxt11880[i-2]);
    		tgt_printf("corb wp %x\n",hda_readw(base, CORBWP));
#endif
    		times = 1000;
    		while(!(hda_readb(base, RIRBSTS) & 0x1) && times--);
    		if (times < 0) {
    			tgt_printf("HDA rirb time out!\n");
    			goto hda_out;
    		}
    		hda_writeb(base, RIRBSTS, 0x1);
    	}
    }
#else
	/*get alc_pin_fixup_tbl value to set codec*/
	i = snd_hda_pick_pin_fixup(val,alc_pin_fixup_tbl);

	tgt_printf("codec ID: 0x%x\n",val);
	if (i < 0) {
		tgt_printf("HDA:fixup_tlb not have this codec!\n");
		goto hda_out;
	}
	pq = alc_pin_fixup_tbl;
	pq += i;
	ptr = pq->pins;

	/*get id operation was used 1*/
	j = 2;
	/*prepare corb buf*/
	while(pq->pins->nid) {
		for(i = 0; i < 4; i++) {
			corb_buf[j++] = (pq->pins->nid << 20) | (0x71c + i) << 8 | ((pq->pins->val >> (i * 8)) & 0xff);
		}
		corb_buf[j++] = (pq->pins->nid << 20) | 0xf1c00;
		pq->pins++;
	}
	for(i = 2; i < j; i++) {
		ls_readl(corb_p + i) = corb_buf[i];
		delay(0x1000),
		hda_writew(base, CORBWP, i);
#ifdef HDA_DEBUG
		tgt_printf("corb 0x%x\n", corb_buf[i]);
		tgt_printf("corb wp %x\n",hda_readw(base, CORBWP));
#endif
		times = 1000;
		while(!(hda_readb(base, RIRBSTS) & 0x1) && times--);
		if (times < 0) {
			tgt_printf("HDA rirb time out!\n");
			goto hda_out;
		}
		if ((corb_buf[i] & 0xf0000) == 0xf0000) {
			if (ptr->val != ls_readl(rirb_p + (i*2))) {
				tgt_printf("HDA codec set error!\n");
				goto hda_out;
			}
#ifdef HDA_DEBUG
			tgt_printf("val -> 0x%x\n", ptr->val);
			tgt_printf("corb 0x%x -> rirb 0x%x\n", corb_buf[i], val = ls_readl(rirb_p + (i*2)));
#endif
			ptr++;
		}
		hda_writeb(base, RIRBSTS, 0x1);
	}
#endif
	tgt_printf("HDA codec cofigure done!\n");
hda_out:
	pfree(corb_p0);
}
static const Cmd Cmds[] = {
	{"ls hda"},
	{"hda_codec", "", 0, "test the hda function", hda_codec_set, 1, 99, 0},
	{0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));
static void init_cmd()
{
	cmdlist_expand(Cmds, 1);
}
