#ifdef EFI_BOOT
#include <cpu.h>
#include <efi.h>
#include <efi_api.h>
#include <platform.h>

#define EFI_MMAP_NR_SLACK_SLOTS 8

efi_status_t install_efimmap(void)
{
	efi_status_t status;
	int memtype = EFI_LOADER_DATA;
	efi_guid_t boot_memmap_guid = EFI_MEMMAP_GUID;
	struct efi_boot_memmap *m, tmp;
	unsigned long size;

	tmp.map_size = 0;
	status = efi_get_memory_map(&tmp.map_size, NULL, &tmp.key_ptr,
			&tmp.desc_size, &tmp.desc_ver);

	size = tmp.map_size + tmp.desc_size * EFI_MMAP_NR_SLACK_SLOTS;
	status = efi_allocate_pool(memtype, sizeof(*m) + size, (void **)&m);
	if (status != EFI_SUCCESS) {
		printf("alloc mmap memory failed\n");
		return status;
	}
	/* And expose them to our EFI payload */
	status = efi_install_configuration_table(&boot_memmap_guid, (void *)VA_TO_PHYS(m));
	if (status != EFI_SUCCESS)
		goto free_map;
	m->buff_size = m->map_size = size;
	status = efi_get_memory_map(&m->map_size, m->map, &m->key_ptr,
			&m->desc_size, &m->desc_ver);
	if (status != EFI_SUCCESS)
		goto uninstall_table;
	return EFI_SUCCESS;
uninstall_table:
	efi_install_configuration_table(&boot_memmap_guid, NULL);
free_map:
	efi_free_pool(m);

	return status;
}
#else
#include <libfdt.h>
#include <libfdt_env.h>
#include <sys/malloc.h>
#include "../../../pmon/common/smbios/smbios.h"
#include "../../../pmon/cmds/bootparam.h"
#include "target/ls7a.h"

#define PAGE_SHIFT 12
extern unsigned long long memorysize_high_n[];
void efimap_init_entry(struct efi_boot_memmap *m, int type, u64 start, u64 size)
{
	static int entry = 0;
	efi_memory_desc_t *map;

	map = (void *)m + sizeof(*m);
	map[entry].type = type;
	map[entry].phys_addr = start;
	map[entry].num_pages = size >> PAGE_SHIFT;
	entry++;
	m->map_size += sizeof(efi_memory_desc_t);
}

struct efi_boot_memmap *init_efi_mmap(void)
{
	int ret = 0;
	unsigned long long i;
	struct efi_boot_memmap *map;
	efi_guid_t boot_memmap_guid = EFI_MEMMAP_GUID;

	map = malloc(sizeof(struct efi_boot_memmap) + sizeof(efi_memory_desc_t) * 32);
	if (!map) {
		return NULL;
		printf("Alloc mammp memory failed!\n");
	}
	map->desc_size = sizeof(efi_memory_desc_t);
	map->desc_ver = 1;
	map->map_size = 0;

#ifdef LS132_CORE
	efimap_init_entry(map, SYSTEM_RAM, 0x200000, 0xe000000 - 0x200000);  // 0x200000~0xf000000
	/* 3. Reserved low memory highest 16M. */
	efimap_init_entry(map, MEM_RESERVED, 0xe000000, 0x1000000);  // 0xf000000~0x10000000
#else
	efimap_init_entry(map, SYSTEM_RAM, 0x200000, 0xf000000 - 0x200000);  // 0x200000~0xf000000
	/* 3. Reserved low memory highest 16M. */
	efimap_init_entry(map, MEM_RESERVED, 0xf000000, 0x1000000);  // 0xf000000~0x10000000
#endif

	/* 4. Available SYSTEM_RAM area */
	efimap_init_entry(map, SYSTEM_RAM, HIGH_MEM_WIN_BASE_ADDR + 0x10000000, memorysize_high_n[0] - 0x10000000); // (HIGH_MEM_WIN_BASE_ADDR + 0x10000000) ~ MAX

	for (i = 1; i < TOT_NODE_NUM; i++) {
		if (memorysize_high_n[i])
			efimap_init_entry(map, SYSTEM_RAM, HIGH_MEM_WIN_BASE_ADDR | (i << 44), memorysize_high_n[i]);
	}
	/* And expose them to our EFI payload */
	return efi_install_configuration_table(&boot_memmap_guid,
			(void *)VA_TO_PHYS(map));

}
#endif
