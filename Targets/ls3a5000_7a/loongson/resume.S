#include <platform.h>
#ifdef LS_STR
/*
 * 3A7A STR config start
 */
	.org 0x500

#ifdef LOONGSON_3C6000

	bl flush_cache

/* add 3c6000 selfrefresh code */
	/* Set interleve_en is 0 */
	li.d	t0, PHYS_TO_UNCACHED(0x1fe00000)
	ld.w	t1, t0, 0x404
	li.w	t2, 0xffffff3f
	and		t1, t1, t2
	st.w	t1, t0, 0x404
	dbar    0

	li.w	t1, 0
	li.w	t2, 4

1:
	bgeu	t1, t2, loop_end

	li.w	t3, 0
2:
	bgeu	t3, t2, middle_loop_end

	li.w	t4, 0
3:
	bgeu	t4, t2, inner_loop_end

	li.w	t5, 0x2418
	or		t5, t5, t0
	slli.w	t6, t4, 16
	or		t5, t5, t6
	slli.w	t6, t3, 8
	or		t5, t5, t6
	li.d	t6, 0x0ff00000
	st.d	t6, t5, 0
	dbar    0

	li.w	t5, 0x2458
	or		t5, t5, t0
	slli.w	t6, t4, 16
	or		t5, t5, t6
	slli.w	t6, t3, 8
	or		t5, t5, t6
	li.d	t6, 0xfffffffffff00000
	st.d	t6, t5, 0
	dbar    0

	li.w	t5, 0x2498
	or		t5, t5, t0
	slli.w	t6, t4, 16
	or		t5, t5, t6
	slli.w	t6, t3, 8
	or		t5, t5, t6
	slli.w	t6, t1, 8
	li.d	t7, 0xff00084
	or		t6, t6, t7
	st.d	t6, t5, 0
	dbar    0


	li.w	t5, 1
	add.w	t4, t4, t5
	b		3b

inner_loop_end:

	li.w	t5, 1
	add.w	t3, t3, t5
	b		2b

middle_loop_end:

	/* ENABLE_MC_REG_SPACE */
	li.w	t3, 0x180
	slli.w	t4, t1, 16
	or		t3, t3, t4
	or		t3, t3, t0
	ld.w	t4, t3, 0
	li.w	t5, (1 << 5)
	or		t4, t4, t5
	st.w	t4, t3, 0
	dbar    0

	/* ENABLE_MC_CONF_SPACE */
	li.w	t3, 0x180
	slli.w	t4, t1, 16
	or		t3, t3, t4
	or		t3, t3, t0
	ld.w	t4, t3, 0
	li.w	t5, ~(1 << 4)
	and		t4, t4, t5
	st.w	t4, t3, 0
	dbar    0

    /* Set MC refresh */
	li.d	t3, PHYS_TO_UNCACHED(0x0000ff00000)
	li.d	t4, 0x1308
	ldx.d   t4, t3,t4
	li.d	t5, 0x00000000000000ff
	or      t4, t4, t5
	li.d	t5, 0x1308
	stx.d   t4, t3,t5
	dbar    0

	li.w	t3, 1
	add.w	t1, t1, t3
	b		1b

loop_end:

	li.w	t1, 0
	li.w	t2, 4

4:
	bgeu	t1, t2, out_loop_end

	li.w	t3, 0
5:
	bgeu	t3, t2, in_loop_end

	li.w	t5, 0x2498
	or		t5, t5, t0
	slli.w	t6, t3, 16
	or		t5, t5, t6
	slli.w	t6, t1, 8
	or		t5, t5, t6
	li.d	t6, 0
	st.d	t6, t5, 0
	dbar    0


	li.w	t4, 1
	add.w	t3, t3, t4
	b		5b

in_loop_end:

	li.w	t4, 1
	add.w	t1, t1, t4
	b		4b

out_loop_end:



#else
	/*set memory controller selfrefresh*/
	/* Enable DDR control register */
	li.d	t0, PHYS_TO_UNCACHED(0x1fe00000)
	ld.w    t1, t0,0x180
	li.w	t2, 0xfffffdef
	and     t1, t1, t2
	li.w	t3, 0x00000420;
	or      t1, t1, t3
	st.w    t1, t0,0x180
	dbar    0 //sync

	/* Set interleave_en is 0 */
	li.d	t0, PHYS_TO_UNCACHED(0x1fe00400)
	ld.w    t1, t0,0x4
	li.w	t2, (1 << 7)
	andn	t1, t1, t2
	st.w    t1, t0,0x4
	dbar    0 //sync

	li.d	t0, PHYS_TO_UNCACHED(0x4000ff00000)
	li.d	t1, 0x1308
	ldx.d   t1, t0,t1
	li.d	t2, 0x00000000000000ff
	or      t1, t1, t2
	li.d	t2, 0x1308
	stx.d   t1, t0,t2
	dbar    0 //sync

	li.d	t0, PHYS_TO_UNCACHED(0x5000ff00000)
	li.d	t1, 0x1308
	ldx.d   t1, t0,t1
	li.d	t2, 0x00000000000000ff
	or      t1, t1, t2
	li.d	t2, 0x1308
	stx.d   t1, t0,t2
	dbar    0 //sync
#endif

	/* delay */
	li.w	t0, 0x4000
1:
	addi.w	t0, t0, -1
	bnez	t0, 1b

	li.d	t0, PHYS_TO_UNCACHED(0xe00100d0000)
	/* set key,usb wakeup of reg GPE0_EN */
	ld.w    t1, t0,0x2c
	li.w	t3, (0x1 << 8)|(0x3f<<10)
	or      t1, t1, t3
	st.w    t1, t0,0x2c

	/* clear 0-15 of reg GPE0_STS */
	ld.w    t1, t0,0x28
	li.w	t3, 0x0000ffff
	st.w    t3, t0,0x28

	/* clear 0-15 of reg PM1_STS */
	ld.w    t1, t0,0x0c
	li.w	t3, 0x0000ffff
	st.w    t3, t0,0x0c

	/* set wake on line */
	ld.w    t1, t0,0x4
	li.w	t3, 0x80
	or      t1, t1, t3
	st.w    t1, t0,0x4

	/* set vsb_gat_delay */
	ld.w    t1, t0,0x4
	li.w	t3, 0x5 << 11
	or      t1, t1, t3
	li.d	t3, 0xefff
	and     t1, t1, t3
	st.w    t1, t0,0x4

	li.w	a0,'S'
	bl tgt_putchar
	li.w	a0,'3'
	bl tgt_putchar

	/* set reg PM1_CNT to get into S3*/
	li.w	t3, 0x00003400
	st.w    t3, t0, 0x14

	/* delay */
	li.w	t0, 0x40000
2:
	addi.w  t0, t0, -1
	bnez    t0, 2b

1:
	b  1b
#endif

LEAF(flush_cache)
	move    a5, ra

	// flush L3 cache
	li.d    t0, 0x14  //L3
	cpucfg  t1, t0    //save cpucfg

	li.d    t0, 0xffff
	and     t6, t1, t0   //ways
	addi.d  t6, t6, 1

	li.d    t0, 0xff0000
	and     t4, t1, t0   //sets
	srli.d  t4, t4, 16
	li.d    t0, 1
	sll.d   t4, t0, t4

	li.d    t0, 0x7f000000
	and     t8, t1, t0   //linesz
	srli.d  t8, t8, 24
	li.d    t0, 1
	sll.d   t8, t0, t8

	li.d    t0, CACHED_MEMORY_ADDR //line
	li.d    t1, 0                  //nr_node
	li.d    t2, 1                  //node_num
	li.d    t3, 0                  //i
	li.d    t5, 0                  //j

6:
	bge     t1, t2, 4f
5:
	bge     t3, t4, 3f
1:
	bge     t5, t6, 2f
	cacop   0xb, t0, 0
	addi.d  t0, t0, 1
	addi.d  t5, t5, 1
	b       1b
2:
	li.d    t5, 0   //reback j to initval
	sub.d   t0, t0, t6
	add.d   t0, t0, t8
	addi.d  t3, t3, 1
	b       5b
3:
	li.d    t3, 0   //reback i to initval
	li.d    t7, 0x100000000000
	add.d   t0, t0, t7
	addi.d  t1, t1, 1
	b       6b
4:

	move    ra,a5

	dbar    0
	jirl    zero, ra, 0
END(flush_cache)

