/*
/*
 * Copyright (c) 2020 <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>@loongson.cn)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */

#ifndef _KERNEL
#define _KERNEL
#endif

#include <platform.h>
#include <asm.h>
#include <regnum.h>
#include <cpu.h>
#include <pte.h>

#include "pmon/dev/ns16550.h"
#include "target/bonito.h"

#include "loongson3_def.h"

#define TOT_CORE_NUM TOT_NODE_NUM * CORES_PER_NODE
#if (TOT_CORE_NUM >= 64)
#define RESERVED_COREMASK 0x0000000000000000
#else
#define RESERVED_COREMASK (0xFFFFFFFFFFFFFFFF << TOT_CORE_NUM)
#endif

/*
 *	Register usage:
 *
 *	s0	link versus load offset, used to relocate absolute adresses.
 */

	.globl	_start
	.globl	start
	.globl	__main
_start:
start:
	.globl	stack
stack = start + LOCK_CACHE_SIZE		/* Place PMON stack in the end of RAM */
	/* enable perf counter as cp0 counter */
	li.w	t0, (0x1 << 16)
	csrxchg t0, t0, 0x200

#if defined(LOONGSON_3A6000) || defined(LOONGSON_3C6000)
#if defined(LOONGSON_3A6000)
	/* disable PTW feature */
	csrrd   t0, 0xc1
	li.d    t1, ~(1 << 24)
	and     t0, t0, t1
	csrwr   t0, 0xc1

	li.d    t0, (0x1 << 9) | 0x30000000
	li.d    t1, (0x1 << 9) | 0x3 | 0x30000000
	csrxchg t0, t1, 0x80 //300 343c0

	li.d    t0, 0x37106f
	csrwr   t0, 0x83

	/* low power setting */
	li.d	t0, 0xa
	csrxchg t0, t0, 0xf0
#endif
#if defined(LOONGSON_3C6000)
	li.d    t0, (0x1 << 9) | 0x3 | 0x30000000
	li.d    t1, (0x1 << 9) | 0x3 | 0x30000000
	csrxchg t0, t1, 0x80 //300 343c3

	li.d    t0, 0x370000
	csrwr   t0, 0x83

	li.d	t0, 0x2002e
	csrxchg t0, t0, 0xf0
#endif
	/* scheduling strategy for SMT */
	li.d    t0, 0x09084474476bc3ff
	csrwr   t0, 0xf1
#else
	/* open auto flush SFB */
	li.w	t0, (0x1 << 9)
	csrxchg t0, t0, 0x80

	/* fast_ldq_dis close */
	li.w	t0, (0x1 << 12)
	csrxchg zero, t0, 0x80

	/* low power setting */
	li.d	t0, 0xe
	csrxchg t0, t0, 0xf0
#endif

#if (defined(LOONGSON_3C5000) || defined(LOONGSON_3C6000)) && defined(FLAT_MODE)
	li.w t0, (0x1 << 16)
	csrxchg t0, t0, 0xca
#endif

	li.d    t0, UNCACHED_MEMORY_ADDR | 0x1
	csrwr   t0, 0x180
	li.d    t0, CACHED_MEMORY_ADDR | 0x11
	csrwr   t0, 0x181

	/* clear Vint cofigure */
	li.d	t0, (0x7 << 16)
	csrxchg	zero, t0, 0x4
	/* set ebase address */
	li.d	t0, 0x1c001000
	csrwr	t0, 0xc
	/* set TLB excption address */
	li.d	t0, 0x000000001c001000
	csrwr	t0, 0x88

/*
 * should before execution jr shutdown slave core
 * otherwise speculative execution cause error
 */
	/* SHUT_SLAVES */
	/* get core id */
	csrrd   t0, 0x20
	andi	t1, t0, (CORES_PER_NODE - 1)
	li.d	t2, ~(CORES_PER_NODE - 1)
	andi    t2, t2, 0x1ff
	and     t2, t2, t0
	bnez    t2, 3f

	/* node 0 */
	li.d    a1, BOOTCORE_ID
	bne	a1, t1, 3f

#if defined(LOONGSON_3C6000)
	/* I2C: Check configuration of memory interleave */
	bl      set_mc_interleave
	li.d    t0, 0x800000001fe03800
	li.d    t1, 0x40000
	st.d    t1, t0, 0x0
#endif
	// MCC must be behind the set_mc_interleave
	/* MCC configure. */
	li.d	t0, PHYS_TO_UNCACHED(0x1fe00000)
	li.d	t1, TOT_NODE_NUM << NODE_OFFSET
	or	t3, t0, t1
1:
#if	defined(MCC)
	move	a0, zero

	/* MCC MODE */
	ld.d	t2, t0, 0x400
#if defined(LOONGSON_3C5000) || defined(LOONGSON_3C6000)
	li.d    t1, 0x3000
#else
	li.d	t1, 0x1000
#endif
	or	t2, t2, t1
	st.d	t2, t0, 0x400
22:
	/* MCA configure */
	li.d    t2, 0x379
	stptr.d t2, t0, 0x1510
	/* MCC BI MODE */
	ldptr.d	t2, t0, 0x1514
	li.d	t1, 0x2
	or	t2, t2, t1
	stptr.d	t2, t0, 0x1514
#if defined(LOONGSON_3C5000)
	/* Scache store fill */
	ld.d    t2, t0, 0x280
	li.d    t1, 0x1c80000000
	or      t2, t2, t1
	st.d    t2, t0, 0x280
#elif !defined(LOONGSON_3C6000)
	bl	chip_ver
	li.d	a1, 0x43
	li.d    t1, 0x1800000000
	blt	v0, a1, 2f
	li.d    t1, 0x1880000000
2:
	or      t2, t2, t1
	st.d    t2, t0, 0x280
#endif

#if	(defined(LOONGSON_3C5000) && defined(FLAT_MODE)) || defined(LOONGSON_3C6000)
	li.w	t1, 0x10000
	add.w	a0, a0, t1
	add.d	t0, t0, t1
	li.w	t1, 0x40000
	bne	a0, t1, 22b

	li.d	t1, 0xfffffffffff00000
	and	t0, t0, t1
#endif
#else
#ifndef LOONGSON_3A6000
	/* mca clock */
	ldptr.w t1, t0, 0x180
#if defined(LOONGSON_3C5000) || defined(LOONGSON_3C6000)
	li.w    t2, ~((1 << 6) | (1 << 11) | (1 << 16) | (1 << 21))
#else
	li.w	t2, ~((1 << 6) | (1 << 11))
#endif
	and     t1, t1, t2
	stptr.w t1, t0, 0x180
#endif
#endif
	/*enable Scache RAM auto clock gating*/
	ldptr.d t2, t0, 0x280
#if defined(LOONGSON_3A6000)
	li.d    t1, 0x9c3 //0x900 SCache low power
#else
	li.d    t1, 0x900 //0x900 SCache low power
#endif
	or      t2, t2, t1
	stptr.d t2, t0, 0x280
#ifdef LOONGSON_3C6000
	li.d    t1, PHYS_TO_UNCACHED(0x1fe10000)
	stptr.d t2, t1, 0x280
	li.d    t1, PHYS_TO_UNCACHED(0x1fe20000)
	stptr.d t2, t1, 0x280
	li.d    t1, PHYS_TO_UNCACHED(0x1fe30000)
	stptr.d t2, t1, 0x280
#endif

	li.d	t1, (1 << NODE_OFFSET)
	add.d	t0, t0, t1
#if defined(LOONGSON_3C6000) && (TOT_NODE_NUM == 4) && !defined(LOONGSON_3D6000)
	add.d	t0, t0, t1
#endif
	blt	t0, t3, 1b

/********add 0x90xxxxxxxxxxxxxx window for core 0****/
	li.d   t1, CACHED_MEMORY_ADDR
	bl     1f
1:
	addi.d  t0, ra, 12
	or      t0, t1, t0
	jirl    zero, t0, 0
	/* now pc run to 0x90xxxxxxxxxxxxxx */

	/* DA disable for 0x90xxxxxxxxxxxxxx and 0x80xxxxxxxxxxxx address can be used */
	li.w    t0, 0xb0
	csrwr   t0, 0x0

	WATCHDOG_CLOSE

	/* spi speedup */
	li.d    t0, PHYS_TO_UNCACHED(0x1fe001f0)
#ifdef	BONITO_100M
	li.w    t1, 0x27
	st.b    t1, t0, 0x4
#elif   BONITO_25M
	li.w    t1, 0x7
	st.b    t1, t0, 0x4
#endif

//#define SPI_QUAD_IO
#ifdef	SPI_QUAD_IO
	/* spi quad_io */
	li.w    t1, 0xb
	st.b    t1, t0, 0x6
1:
	ld.bu   t2, t0, 0x6
	bne     t2, t1, 1b
#endif
	/* Core 0 don't need idle */
	bl      2f
3:
	/* wait ipi wakeup */
	/* enable ipi interrupt */
	li.d	t0, 0x1004
	li.d	t1, 0xffffffff
	iocsrwr.w t1, t0

	/* interrupt enable */
	li.d	t0, 1 << 2
	csrxchg t0, t0, 0

	/* enable ipi interrupt for wakeup */
	li.d	t0, 1 << 12
	csrxchg t0, t0, 4

	idle	0
#ifdef	MCC
	/* other core wait MCC enable */
	/* get node id */
	csrrd	t0, 0x20
	li.d	t1, ~(CORES_PER_NODE - 1)
	andi	t1, t1, 0x1ff
	and     t1, t1, t0

	/* get node id shift offset */
	li.d	t2, CORES_PER_NODE
	ctz.w	t2, t2
	li.d	t3, NODE_OFFSET
	sub.d	t2, t3, t2

	sll.d   t1, t1, t2
	li.d	t0, 0x1fe00400
	or      t0, t0, t1
	li.d    t2, 0x1000
1:
	ld.w	t1, t0, 0
	and     t1, t1, t2
	beqz	t1, 1b
#endif
2:

	/* disable interrupt */
	li.d    t0, (1 << 2)
	csrxchg zero, t0, 0x0

	la	sp, stack
	la	gp, _gp

	/* don't change this code,jumping to cached address */
	li.d   t1, CACHED_MEMORY_ADDR
	bl     1f
1:
	addi.d  t0, ra, 12
	or      t0, t1, t0
	jirl    zero, t0, 0
	/* now pc run to 0x90xxxxxxxxxxxxxx */

	/* DA disable for 0x90xxxxxxxxxxxxxx and 0x80xxxxxxxxxxxx address can be used */
	li.w    t0, 0xb0
	csrwr   t0, 0x0

	/* calculate ASM stage print function s0 address */
	la      s0, start
	li.d    a0, PHYS_TO_UNCACHED(0x1c000000)
	/* if change locked cache address may need change the following code */
	sub.d   s0, s0, a0
	li.d	a0, 0x00000000ffff0000
	and     s0, s0, a0

	la	sp, stack
	la	gp, _gp

	/* slave core run to slave_main */
	/* 3C5000 default boot core must small then 4 */
	csrrd   t0, 0x20
	andi    t0, t0, 0x1ff
	andi    t1, t0, 0x3             /* core id */
	andi    t2, t0, 0x1fc           /* node id << 2 */

	/* get current core position in reserved_core_mask */
	add.w	t2, t2, t1
	li.w	t1, 1
	sll.d	t1, t1, t2

	li.d    t3, RESERVED_COREMASK
	and     t3, t3, t1
	bnez    t3, wait_to_be_killed

	li.d    t2, BOOTCORE_ID
	bne     t0, t2, slave_main
	b       1f

wait_to_be_killed:
	idle	0 // Avoid the killed cores awakened by unexpected interrupts.
	b	wait_to_be_killed
1:

#if defined(LOONGSON_3C6000) && (TOT_NODE_NUM > 1)
	/* 3C6000 AA SE device mode need to reset slave cores */
	li.d	a0,	TOT_NODE_NUM
	/* one reg control four logic cores */
	li.d	a1,	CORES_PER_NODE / 4
	/* start with node 1 */
#if !defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 4)
	li.d	a2,	2
#else
	li.d	a2,	1
#endif

reset_slave_per_node:
	slli.d	t2,	a2,	44
	li.d	t0,	PHYS_TO_UNCACHED(0x1fe001d8)
	or		a4,	t2,	t0
	/* start with cpu 0 */
	li.d	a3,	0

reset_slave_per_core:
	slli.d	t2,	a3,	16
	or		t0,	t2,	a4

	li.w	t1,	0x55
	st.b	t1,	t0,	0

	li.w	t1,	0x00
	st.b	t1,	t0,	0

	/* wait few time seems like safer */
	li.w	t2,	100
1:
	addi.w	t2,	t2,	-1
	bnez	t2,	1b

	li.w	t1,	0x55
	st.b	t1,	t0,	0

	li.w	t1,	0xff
	st.b	t1,	t0,	0

	addi.w	a3,	a3,	1
	bne		a3,	a1,	reset_slave_per_core

#if !defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 4)
	addi.w	a2,	a2,	2
#else
	addi.w	a2,	a2,	1
#endif
	bne		a2,	a0,	reset_slave_per_node
	/* end of se fixup */
#endif

	li.d	a0, GS3_UART_BASE
	bl	initserial

	PRINTSTR("Shut down slave cores done!\r\n")

bsp_start:
#ifdef QUICK_START
	QUICKDBG("\r\nPMON LoongArch Initializing\r\n")
#else
	PRINTSTR("\r\nPMON LoongArch Initializing. Standby...\r\n")
#endif

#if (TOT_NODE_NUM == 1)
	/* enable rd interleave */
	li.d    t0, (UNCACHED_MEMORY_ADDR |0x1fe00000)
	ld.d    t2, t0, 0x400
	li.d    t1, (0x1 << 13)
	or      t2, t2, t1
	st.d    t2, t0, 0x400
#endif

	bl	locate

	/* this code start address is 0x500 */
#include "resume.S"

	/* all exception entry */
	.org 0x1000

	csrwr   t0, 0x30
	csrwr   t1, 0x31

	/* IPI */
	csrrd	t0, 0x4
	li.d	t1, (1 << 12)
	and	t0, t0, t1
	beqz	t0, 1f
	csrrd	t1, 0x5
	and	t0, t0, t1
	beqz	t0, 1f

	/* clear ipi int status */
	li.d	t0, 0x1000 /* IOCSR_IPI_STATUS */
	iocsrrd.w t1, t0
	li.d	t0, 0x100c /* IOCSR_IPI_CLEAR */
	iocsrwr.w t1, t0

	csrrd   t0, 0x30
	csrrd   t1, 0x31

	ertn
1:
	/* s0 in different stage should fixup */
	la      a0, start
	li.d    a1, PHYS_TO_UNCACHED(0x1c000000)
	sub.d   a0, a0, a1
	li.d	a1, 0x00000000ffff0000
	and     a0, a0, a1
	beq	a0, s0, 1f
	move	s0, zero
1:
	and     s0, s0, a0

	csrrd	t0, 0x8a
	andi	t0, t0, 0x1
	bnez	t0, 2f

	PRINTSTR("\r\nCPU Trigger exception!\r\n")
	PRINT_CSR(0x4);
	PRINT_CSR(0x5);
	PRINT_CSR(0x6);
	PRINT_CSR(0x7);
	PRINT_CSR(0x8);
1:
	b	1b
2:
	li.d   t1, CACHED_MEMORY_ADDR
	bl     1f
1:
	addi.d  t0, ra, 12
	or      t0, t1, t0
	jirl    zero, t0, 0

	li.d	t0, 0xb0
	csrwr	t0, 0
	PRINTSTR("\r\nTLB exception!\r\n");
	PRINT_CSR(0x89);
	PRINT_CSR(0x8a);
1:
	b	1b

locate:

	/*sram ctrl*/
#ifdef LOONGSON_3C5000
	LS3C5000_SRAM_CTRL
#elif defined(LOONGSON_3C6000)
#if !defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 4)
	LS3C6000_SRAM_CTRL(2)
#else
	LS3C6000_SRAM_CTRL(1)
#endif
#elif defined(LOONGSON_3A6000)
	LS3A6000_SRAM_CTRL
#else
	LS3A5000_SRAM_CTRL
#endif
#ifdef	VOLTAGE_CTRL
	li.d	t0, TOT_NODE_NUM
	move	t1, zero
1:
	TTYDBG("\r\Node ")
	move	a0, t1
	bl	hexserial
#if !defined(LOONGSON_3D6000) || defined(LOONGSON_3E6000)
	TTYDBG("\r\nN Voltage  write :\r\n")
#if defined(LOONGSON_3C5000)
	li.w	a0, VOL_mV(1150)
#elif defined(LOONGDON_3A6000)
	li.w	a0, VOL_mV(1150)
#elif defined(LOONGDON_3E6000) || (defined(LOONGSON_3C6000) && (TOT_NODE_NUM == 4))
	li.w	a0, VOL_mV(1050)
#elif defined(LOONGSON_3B6000)
	li.w	a0, VOL_mV(1150)
#elif defined(LOONGSON_3C6000)
	li.w	a0, VOL_mV(1100)
#else
	li.w	a0, VOL_mV(1250)
#endif
#ifdef VDDN_VALUE
	li.w	a0, VOL_mV(VDDN_VALUE)
#endif
	move	a1, t1
	bl	v_n_ctrl
	TTYDBG("\r\nN Voltage  read :\r\n")
	move	a0, t1
	bl	v_n_ctrl_read

	TTYDBG("\r\nP Voltage write :\r\n")
#ifdef LOONGSON_3C5000
	li.w	a0, VOL_mV(1200)
#elif defined(LOONGDON_3A6000)
	li.w	a0, VOL_mV(1200)
#else
#if	(CORE_FREQ > 2400)
	li.w	a0, VOL_mV(1050)
#else
	li.w	a0, VOL_mV(1150)
#endif
#endif
	move	a1, t1
	bl	v_p_ctrl
#endif
#ifdef AVS_SUPPORT
	move    s1, t1
	slli.d	s1, s1, NODE_OFFSET
	bl      avs_adjust_vol
#endif
#if defined(LOONGSON_3D6000) || defined(LOONGSON_3D5000) || (defined(LOONGSON_3C6000) && (TOT_NODE_NUM == 4) && !defined(LOONGSON_3D6000))
	addi.w	t1, t1, 2
#elif	(defined(LOONGSON_3C5000) && (!defined(FLAT_MODE)) || TOT_NODE_NUM >= 8) /* compatible mode */
	addi.w	t1, t1, 4
#else
	addi.w	t1, t1, 1
#endif
	blt	t1, t0, 1b
#endif

	/* Read sys_clk_sel */
	TTYDBG ("\r\nPHYS_TO_UNCACHED(0x1fe00190)  : ")
	li.d    t2,PHYS_TO_UNCACHED(0x1fe00190)
	ld.d    t1, t2, 0x0
	bl      hexserial64
	PRINTSTR ("\r\n")

	li.d	s2, TOT_NODE_NUM << NODE_OFFSET
	li.d	s3, 0x0000100000000000
	/* Using s1 to passing the node id */
	li.d	s1, 0x0000000000000000
clk_set_loop:
#ifdef LOONGSON_3C6000
#include "ls3c6000_clksetting.S"
#else
#include "loongson3_clksetting.S"
#endif
	add.d	s1, s1, s3

#if defined(LOONGSON_3C6000) && (TOT_NODE_NUM == 4) && !defined(LOONGSON_3D6000)
	add.d	s1, s1, s3
#endif
	blt	s1, s2, clk_set_loop

##########################################

#ifndef LOONGSON_3C6000
	/*disable 0x3ff0_0000 that routing of configuration register space */
	li.d    t0, PHYS_TO_UNCACHED(0x1fe00400)
	ld.w    t1, t0, 0x0
	li.w    t2, (1 << 9)
	or      t1, t1, t2
	st.w    t1, t0, 0x0
#endif

	li.d	s1, TOT_NODE_NUM
	move    s2, zero
1:
	STABLE_COUNTER_CLK_EN
#if	(TOT_NODE_NUM >=  2)
	SET_GPIO_FUNC_EN(1 << 13)
#endif
#ifdef LOONGSON_3C5000
	SET_NODEMASK(3)
#elif !defined(LOONGSON_3C6000)
	SET_NODEMASK(TOT_NODE_NUM - 0x1)
#endif

#if defined(LOONGSON_3C6000) || defined(LOONGSON_3C5000)
	//chip_mask
#if	defined(LOONGSON_3C6000) && (TOT_NODE_NUM == 4) && !defined(LOONGSON_3D6000)
	SET_CHIPMASK(2)
	addi.w  s2, s2, 0x2
#elif defined(FLAT_MODE)
	SET_CHIPMASK(TOT_NODE_NUM - 1)
	addi.w  s2, s2, 0x1
#else
	SET_CHIPMASK(TOT_NODE_NUM / 4 - 1)
	addi.w  s2, s2, 0x4
#endif
#else
	addi.w  s2, s2, 0x1
#endif
	blt     s2, s1, 1b

#if defined(LOONGSON_3C6000) && (TOT_NODE_NUM == 1)
	SET_GPIO_FUNC_EN(1 << 9)
	GPIO_CLEAR_OUTPUT(1 << 9)
#endif
#if	(TOT_NODE_NUM >=  2)
	/*sync stable counter*/
	GPIO_CLEAR_OUTPUT(1 << 12)
	GPIO_SET_OUTPUT(1 << 12)
#else
	/*stable_reset*/
	li.d    t0, PHYS_TO_UNCACHED(0x1fe00420)
	ld.w    t1, t0, 0x0
	li.w    t2, (1 << 21)
	or      t1, t1, t2
	st.w    t1, t0, 0x0
	ld.w    t1, t0, 0x0
	li.w    t2, (1 << 21)
	xor     t1, t1, t2
	st.w    t1, t0, 0x0
#endif

#ifndef LOONGSON_3C6000
#include "loongson3_ht1_32addr_trans.S"
	PRINTSTR("loongson3_ht1_32addr_trans.S End\r\n")
#endif

/*
 *  Reset and initialize l1 caches to a known state.
 */
	PRINTSTR("\r\nlock scache ")
	li.d	a0, LOCK_CACHE_BASE
	bl	hexserial
	PRINTSTR(" - ")
	li.d	a0, LOCK_CACHE_BASE + LOCK_CACHE_SIZE
	bl	hexserial

	li.d	t0, PHYS_TO_UNCACHED(0x1fe00000)
	li.d	t1, ~(LOCK_CACHE_SIZE - 1)
	li.d	t2, (LOCK_CACHE_BASE & 0xffffffffffff) | (1 << 63)
	st.d	t1, t0, 0x248
	st.d	t2, t0, 0x208
#if (defined(LOONGSON_3C6000) || defined(LOONGSON_3D6000)) && defined(FLAT_MODE)
	li.d	t0, PHYS_TO_UNCACHED(0x1fe10000)
	st.d	t1, t0, 0x248
	st.d	t2, t0, 0x208
	li.d	t0, PHYS_TO_UNCACHED(0x1fe20000)
	st.d	t1, t0, 0x248
	st.d	t2, t0, 0x208
	li.d	t0, PHYS_TO_UNCACHED(0x1fe30000)
	st.d	t1, t0, 0x248
	st.d	t2, t0, 0x208
#endif

	PRINTSTR("\r\nLock Scache Done.\r\n")
	/* copy flash code to scache */
	li.d	a1, PHYS_TO_CACHED(0x1c000000)
	la	a0, start
	la	a2, edata
1:
	ld.d	a3, a1, 0
	st.d	a3, a0, 0
	addi.d	a0, a0, 8
	addi.d	a1, a1, 8
	bne	a2, a0, 1b

	PRINTSTR("copy text section to lock cache done.\r\n")
	/*clear cache mem BSS in this space*/
	la	a0, _edata
	la	a1, _end
1:
	st.d	zero, a0, 0
	addi.d	a0, a0, 8
	blt	a0, a1, 1b

	ibar    0
	dbar    0

	li.d	a0, LOCK_CACHE_BASE
	li.d	a1, LOCK_CACHE_BASE + LOCK_CACHE_SIZE
1:
	/* ensure locked cache address accessible so this code can`t remove*/
	ld.d	zero, a0, 0
	addi.d	a0, a0, 0x40
	bne	a1, a0, 1b

	/* jump to locked cache address */
	/* ra addr is 0xffffffff9fcxxxxx */
	li.d	t0, PHYS_TO_CACHED(0x9000000c)
	li.d	t1, 0xfffff
	bl	1f
1:
	and	t1, ra, t1
	add.d	t1, t1, t0
	jirl	zero, t1, 0

	li.d	t1, TOT_NODE_NUM
	move	t2, zero
1:
	slli.d	t3, t2, 44
	/* clken_percore enable */
	li.d	a0, PHYS_TO_UNCACHED(0x1fe00420)
	or	a0, a0, t3
	/* close INT_encode */
	ld.w	a1, a0, 0x4
	li.w	t0, ~((1 << 17) | (1 << 16))
	and	a1, t0, a1
	st.w	a1, a0, 0x4
	/* SE */
	li.d	a0, PHYS_TO_UNCACHED(0x1fe00404)
	or	a0, a0, t3
	ld.w	a1, a0, 0
	li.w	a2, 0xf00000
	or	a1, a1, a2
	st.w	a1, a0, 0

	addi.w	t2, t2, 1
#if defined(LOONGSON_3C6000) && (TOT_NODE_NUM == 4) && !defined(LOONGSON_3D6000)
	addi.w  t2, t2, 1
#endif
	blt	t1, t2, 1b

	PRINTSTR("run in cache.\r\n")
	move	s0, zero

#ifdef QUICK_START
	QUICKDBG("Mem init ...")
#endif
	la	ra, cache_stage
	jirl	zero, ra, 0

LEAF(watchdog_close)
	WATCHDOG_CLOSE
	jirl zero, ra, 0
END(watchdog_close)

#include "memdebug.S"
#include "cpulib.S"
#include "serial.S"
#include "ls3a5000_vctrl.S"
#include "set_mc_interleave.S"
