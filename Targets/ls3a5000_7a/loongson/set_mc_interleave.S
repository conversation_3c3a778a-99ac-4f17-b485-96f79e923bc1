#include <platform.h>
#define MC_INTERLEAVE_DEBUG 0

LEAF(mc_i2cinit)
	//LPB clock_a,SCL clock_s,prescale = clock_a / (4 * clock_s);
	ld.b	a0, a3, CTR_REG
	andi	a0, a0, 0x7f /* ~(1 << 7) */
	st.b	a0, a3, CTR_REG

	li.d	a0, 0x53
	st.b	a0, a3, PRER_LO_REG

#ifdef BONITO_100M
	li.d	a0, 0x2
#else
	li.d	a0, 0x0 //25M
#endif
	st.b	a0, a3, PRER_HI_REG

	ld.b	a0, a3, CTR_REG
	ori	a0, a0, (1 << 7)
	st.b	a0, a3, CTR_REG

	jirl	zero, ra, 0
END(mc_i2cinit)

LEAF(mc_i2cread)
	/*
	 * use register:
	 *  a0, a1, a2, a3
	 *  input: a0,a1
	 *  a0: device ID -> change to return value
	 *  a1: register offset
	 */
	/****change page*****/
	li.d	a2, 0xff00
	and	a2, a1, a2
	bnez	a2, 1f
	li.d	a2, 0x6c
	b	2f
1:
	li.d	a2, 0x6e
2:
	st.b	a2, a3, TXR_REG
	li.d	a2, CR_START | CR_WRITE
	st.b	a2, a3, CR_REG

1:
	ld.bu	a2, a3, SR_REG
	andi	a2, a2, SR_TIP
	bnez	a2, 1b

	li.d	a2, CR_STOP
	st.b	a2, a3, CR_REG

1:
	ld.bu	a2, a3, SR_REG
	andi	a2, a2, SR_BUSY
	bnez	a2, 1b

	/* i2c_send_addr */
	/* send device addr */
	/* load device address */
	andi	a2, a0, 0xfe
	st.b	a2, a3, TXR_REG

	/* send start frame */
	li.d	a2, CR_START | CR_WRITE
	st.b	a2, a3, CR_REG

	/* wait send finished */
1:
	ld.bu	a2, a3, SR_REG
	andi	a2, a2, SR_TIP
	bnez	a2, 1b

	/* send device addr */
	/* load data(offset/command) to be send */
	ori	a2, a1, 0
	st.b	a2, a3, TXR_REG
	/* send data frame */
	li.d	a2, CR_WRITE
	st.b	a2, a3, CR_REG

	/* wait send finished */
1:
	ld.bu	a2, a3, SR_REG
	andi	a2, a2, SR_TIP
	bnez	a2, 1b

	/* i2c_send_addr */
	/* i2c_read_word */
	/* send device addr */
	/* load device address (dev_addr | 0x1/READ) */
	ori	a2, a0, 0x1
	st.b	a2, a3, TXR_REG

	/* send start frame */
	li.d	a2, CR_START | CR_WRITE
	st.b	a2, a3, CR_REG

	/* wait send finished */
1:
	ld.bu	a2, a3, SR_REG
	andi	a2, a2, SR_TIP
	bnez	a2, 1b

	/* receive data from fifo */
	li.d	a2, CR_READ | CR_ACK
	st.b	a2, a3, CR_REG

1:
	ld.bu	a2, a3, SR_REG
	andi	a2, a2, SR_TIP
	bnez	a2, 1b

	/* read data from fifo */
	ld.bu	a1, a3, RXR_REG

	/* i2c_stop */
	li.d	a2, CR_STOP
	st.b	a2, a3, CR_REG
1:
	ld.bu	a2, a3, SR_REG
	andi	a2, a2, SR_BUSY
	bnez	a2, 1b

	ori	a0, a1, 0

	jirl	zero, ra, 0
END(mc_i2cread)

LEAF(set_mc_interleave)
	// t0 : node id
	// t4 : mc bit map
	move	a6, ra
#if MC_INTERLEAVE_DEBUG
	li.d	a0, GS3_UART_BASE
	bl	initserial
	PRINTSTR("mc interleave debug\r\n")
#endif
	li.d	t0, 0
node_id_loop:
	li.d	t4, 0

	// Only support DDR4/DDR5 right now
	// Config the base address of I2C
	// t1 : mc_num
	// t3 : deice id
	move	t1, zero
mc_read_loop:
#if defined(LOONGSON_3E6000)
	andi    t5, t0, 0x3
	li.d	t6, 2
	blt	t5, t6, 130f
	li.d	t2, 0x800000001fe00138
	b	138f
130:
	li.d	t2, 0x800000001fe00130
138:
	andi    t5, t0, 0x4
	slli.d  t5, t5, 44
#elif defined(LOONGSON_3D6000)
	li.d	t6, 2
	div.d	t6, t0, t6
	li.d	t7, 2
	mul.d	t6, t6, t7
	slli.d	t5, t6 , NODE_OFFSET
	li.d	t6, 2
	mod.d	t6, t0, t6
	li.d	t7, 2
	div.d	t7, t1, t7
	add.d	t6, t6, t7
	li.d	t7, 2
	mod.d	t6, t6, t7
	li.d	t7, 8
	mul.d	t6, t6, t7
	li.d	t2, 0x800000001fe00130
	add.d	t2, t2, t6
#elif defined(LOONGSON_3C6000)
	slli.d	t5, t0 , NODE_OFFSET
	li.d	t2, 0x800000001fe00130
#endif
	or	a3, t5, t2
	bl	mc_i2cinit

	// Slot0
	// Config the base address of I2C
#ifdef LOONGSON_3E6000
	li.d	t3, 1
	and	t3, t0, t3
	beqz	t3, node0_2
	b	node1_3
node0_2:
	li.d	t3, 2
	blt	t1, t3, 3f
	b	node_out
node1_3:
	li.d	t3, 2
	bge	t1, t3, 3f
	b	node_out
node_out:
#endif
	addi.d	t3, t1, 0
	slli.d	t3, t3, 1
	ori	t3, t3, 0xa1
	move	a0, t3
	li.d	a1, 0x2		//DRAM_TYPE_OFFSET
#if MC_INTERLEAVE_DEBUG
	move	t2, a0
	move	t3, a1
	move	t5, a3
	PRINTSTR("\r\ndev addr\r\n")
	move	a0, t2
	bl	hexserial64
	PRINTSTR("\r\ni2c base addr\r\n")
	move	a0, t5
	bl	hexserial64
	move	a0, t2
	move	a1, t3
	move	a3, t5
#endif
	bl	mc_i2cread
	li.d	t2, 0xc		//DDR4
	beq	a0, t2, 1f
	li.d	t2, 0x12	//DDR5
	bne	a0, t2, 2f
1:
	li.d	t2, 0x1
	sll.d	t2, t2, t1
	or	t4, t4, t2
2:
	// Slot1
	// Config the base address of I2C
#ifdef LOONGSON_3E6000
	li.d	t3, 1
	and	t3, t0, t3
	beqz	t3, node0_2s1
	b	node1_3s1
node0_2s1:
	li.d	t3, 2
	blt	t1, t3, 3f
	b	node_outs1
node1_3s1:
	li.d	t3, 2
	bge	t1, t3, 3f
	b	node_outs1
node_outs1:
#endif
	addi.d	t3, t1, 4
	slli.d	t3, t3, 1
	ori	t3, t3, 0xa1
	move	a0, t3
	li.d	a1, 0x2		//DRAM_TYPE_OFFSET
	bl	mc_i2cread
	li.d	t2, 0xc		//DDR4
	beq	a0, t2, 1f
	li.d	t2, 0x12	//DDR5
	bne	a0, t2, 3f
1:
	li.d	t2, 0x1
	sll.d	t2, t2, t1
	or	t4, t4, t2
3:
	addi.d	t1, t1, 1
	li.d	t2, MC_PER_NODE
	blt	t1, t2, mc_read_loop

	//calculate the mc count
	//t2 : MC num
	//t3 : MC count
	//a0 : first MC
	//a1 : Second MC
	move	a0, zero
	move	a1, zero
	move	t2, zero
	move	t3, zero
mc_count_loop:
	srl.d	t1, t4, t2
	andi	t1, t1, 0x1
	beqz	t1, 3f
	bnez	t3, 1f
	move	a0, t2
	b	2f
1:
	li.d	a1, 1
	bne	t3, a1, 2f
	move	a1, t2
2:
	add.d	t3, t3, t1
3:
	li.d	t1, MC_PER_NODE
	addi.d	t2, t2, 1
	blt	t2, t1, mc_count_loop
test_mc_1:
	//set interleave
	li.d	t2, 0x800000001fe00400
	slli.d	t1, t0, NODE_OFFSET
	or	t2, t1, t2
	li.d	t1, 0x4
	bne	t3, t1, 1f
	li.d	t1, 0xc0
	ori	t1, t1, INTERLEAVE_OFFSET
	st.d	t1, t2, 0x4
	b	2f
1:
	li.d	t1, 0x2
	bne	t3, t1, 2f
	li.d	t1, 0x40
	ori	t1, t1, INTERLEAVE_OFFSET
	st.d	t1, t2, 0x4
2:
	li.d	t2, 0x800000001fe00410
	slli.d	t1, t0, NODE_OFFSET
	or	t2, t1, t2

	beqz	t3, 2f
	// 1 Channel
	li.d	t1, 1
	bne	t3, t1, 1f
	slli.d	t4, a0, 2
	or	a0, a0, t4
	slli.d	t4, a0, 4
	or	a0, a0, t4
	b	2f
1:
	// 2 Channels
	li.d	t1, 2
	bne	t3, t1, 1f
	slli.d	t4, a1, 2
	or	a0, a0, t4
	slli.d	t4, a0, 4
	or	a0, a0, t4
	b	2f
1:
	// 4 Channels
	li.d	a0, 0xe4
2:
	st.b	a0, t2, 0x1

	// loop node id
	addi.d	t0, t0, 0x1
#if defined(LOONGSON_3C6000) && (TOT_NODE_NUM == 4) && !defined(LOONGSON_3D6000)
	slli.d	t0, t0 , 1
#endif
	li.d	t1, TOT_NODE_NUM
	blt	t0, t1, node_id_loop

#if MC_INTERLEAVE_DEBUG
	PRINTSTR("mc interleave set end\r\n")
#endif
	move	ra, a6
	jirl	zero, ra, 0
END(set_mc_interleave)
