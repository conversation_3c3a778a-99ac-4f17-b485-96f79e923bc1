/*
 * loongson3_clksetting.S
 * change the PLL settings of each core
 * NOTE:
 * Using S1 for passing the NODE ID
 */
#ifdef SOFT_CLKSEL

#define CORE_STEP       25

#define USE_LS_PLL      1

#ifdef BONITO_100M
#define SYS_CLOCK       100     //MUST BE 100 or 25, depend on the osillator
#elif BONITO_25M
#define SYS_CLOCK       25      //MUST BE 100 or 25, depend on the osillator
#SYS_CLOCK IS NOT GOOD FOR HIGH FREQUENCY
#endif

// L1_* define core frequency
#define LS_PLL		USE_LS_PLL
#define ST_PLL		(1 - USE_LS_PLL)

//PLL Constraint
//Refc = 25/50/100MHz, 50MHz is more prefered
//VCO = 4.8GHz - 6.4GHz
//LOOPC < 255
//DIV = 1/2/4/6/>6, 3/5 is NOT prefered

#define L1_REFC		2 //SYS_CLOCK/50
//#define L1_REFC		SYS_CLOCK/50

#define PCIE_L1_REFC	2 //SYS_CLOCK/50
#define PCIE_VDDA_CTRL  0x7
#define PCIE_VDDD_CTRL  0x7
#define PCIE_PLL_L1_LOCKED 		((1 << 58) | (1 << 6))
#define PCIE_PLL_L1_ENA		(0x1 << 1)
#define PCIE_FREQ       1000
#define PCIE_USE_NODE

#define VDDA_CTRL_3     0x3
#define VDDD_CTRL_3     0x3
#define VDDA_CTRL_7     0x7
#define VDDD_CTRL_7     0x7
#define VDDA_EN         0x1
#define VDDD_EN         0x1

#define SYS_PD			(((1 - ST_PLL) << 1) | (1 - LS_PLL))
#define PLL_L1_LOCKED 		((ST_PLL << 17) | (LS_PLL << 16))
#define PLL_CHANG_COMMIT 	0x1

#define PLL_L1_ENA		(0x1 << 2)

    li.d    t0, PHYS_TO_UNCACHED(0x1fe001b0)
    or      t0, t0, s1
    ld.w    t1, t0, 0x0
    li.w    t2, (0x3 << 8)
    or      t1, t1, t2
    st.w    t1, t0, 0x0

    /* use gpio to reset pcie clk */
    li.d    t0, PHYS_TO_UNCACHED(0x1fe00500)
    or      t0, t0, s1
    li.w    t2, ~(0x1 << 11)
    /* gpio oen */
    ld.w    t1, t0, 0x0
    and     t1, t1, t2
    st.w    t1, t0, 0x0
    /* gpio func en */
    ld.w    t1, t0, 0x4
    and     t1, t1, t2
    st.w    t1, t0, 0x4
    /* gpio output val */
    ld.w    t1, t0, 0x8
    and     t1, t1, t2
    st.w    t1, t0, 0x8

#if 0 //PRG
    TTYDBG ("PRG:")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe00440)
    or      t0, t0, s1
    ld.d    t1, t0, 0
    li.d    t2, (0x1 << 2) // enable all CLKout_en
    or      t1, t1, t2
    st.d    t1, t0, 0x0

    ld.d    t1, t0, 0
    li.d    t2, (0x1 << 10) // enable all CLKout_en
    or      t1, t1, t2
    st.d    t1, t0, 0x0
#endif

    /* 3C6000 only need config PCIE scacle of sub-node 0. */
    TTYDBG ("Change the scale of PCIE clock\r\n")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe00180)
    or      t0, t0, s1
    ld.w    a0, t0, 0x0
    li.w    a1, 0x88ffffff
    and     a0, a0, a1
    li.w    a1, 0x77000000 // 4/8 of node clock
    or      a0, a0, a1
    st.w    a0, t0, 0x0

    TTYDBG ("Change the scale of LS132 clock\r\n")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe00420)
    or      t0, t0, s1
    ld.w    a0, t0, 0x0
    li.w    a1, 0xfff0ffff
    and     a0, a0, a1
    li.w    a1, 0x00080000 // 1/8 of node clock
    or      a0, a0, a1
    st.w    a0, t0, 0x0

    TTYDBG ("Soft CLK SEL adjust begin\r\n")

//soft_pcie_pll:
    TTYDBG ("PCIE:")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe001a8)
    or      t0, t0, s1
#ifdef PCIE_USE_NODE
    li.d    t1, 0x1 | PCIE_PLL_L1_ENA | (0x4 << 8) |(0x1 << 30) | (VDDD_EN << 41) | (VDDA_EN << 40)
    st.d    t1, t0 , 0x0
    ld.d    a0, t0, 0x0
#else
    li.w    t1, (0x1 << 7) //power down pll L1 first
    st.w    t1, t0, 0x0

    li.d    t1, (PCIE_VDDD_CTRL << 36) | (PCIE_VDDA_CTRL << 32)
2:
    /*L1_LOOPC = L1_REFC*CORE_FREQ*L1_DIV/SYS_CLOCK */
    li.w    t2, PCIE_FREQ
    li.w    t3, PCIE_L1_REFC
    li.w    t4, SYS_CLOCK

    li.d    t5, 6 //div
    mul.d   t3, t2, t3
    mul.d   t3, t5, t3
    div.d   t3, t3, t4

    slli.d  t2, t3, 14
    slli.d  t3, t3, 42
    or      t2, t2, t3 /*pcie 0 1 LOOPC*/

    li.d    t5, (6 << 52) | (6 << 24) //div
    or      t1, t1, t5 /* pcie DIV  VDD_CTRL*/
    or	    t1, t1, t2 /* L1_DIV  VDD_CTRL  LOOPC */
    st.d    t1, t0, 0x0

    li.d    t3, (VDDD_EN << 41) | (VDDA_EN << 40)
    or	    t1, t1, t3 /* L1_DIV  VDD_CTRL  LOOPC PD_PLL VDD_EN */
    li.d    t3, (PCIE_L1_REFC << 8) | (0 << 30)	/* PCIE_PLL */
    or	    t1, t1, t3 /* L1_DIV  VDD_CTRL  LOOPC PD_PLL VDD_EN L1_REFC */
    ori     t1, t1, PCIE_PLL_L1_ENA
    st.d    t1, t0, 0x0

    li.d    a1, PCIE_PLL_L1_LOCKED
11: //wait_locked_sys:
    ld.d    a0, t0, 0x0
    and     a0, a1, a0
    bne     a0, a1, 11b //wait_locked_sys
    //beqz    a0, 11b //wait_locked_sys

    ld.d    a0, t0, 0x0
    ori     a0, a0, PLL_CHANG_COMMIT
    st.d    a0, t0, 0x0
#endif

    bl     hexserial

//soft_sys:
    TTYDBG ("CORE & NODE:")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe001b0)
    or      t0, t0, s1
    li.d    t1, (0x3 << 19) | (0x3 << 8) //power down pll L1 first
    st.d    t1, t0, 0x0

    li.d    t1, (VDDD_CTRL_7 << 60) | (VDDA_CTRL_7 << 56)
    li.w    t5, 2
2:
    /*L1_LOOPC = L1_REFC*CORE_FREQ*L1_DIV/SYS_CLOCK */
    li.w    t2, CORE_FREQ
    li.w    t3, L1_REFC
    li.w    t4, SYS_CLOCK

    mul.d   t3, t2, t3
    mul.d   t3, t5, t3
    div.d   t3, t3, t4

    slli.d  t2, t3, 32
    slli.d  t3, t3, 54
    or      t2, t2, t3 /*L1_LOOPC*/

    or      t1, t1, t5 /* | L1_DIV */
    st.d    t1, t0, 0x8
    li.d    t1, (0x3 << 19) | (0x3 << 8) //power down pll L1 first
    st.d    t1, t0, 0x0
    li.d    t1, (L1_REFC << 48) | (L1_REFC << 26) | \
                (SYS_PD << 19) | (0x3 << 10) | (VDDD_EN << 9) | (VDDA_EN << 8) | (0x1 << 7)
    or      t1, t1, t2 /* | L1_LOOPC */
    slli.d  t5, t5, 42 /* L1_DIV << 42*/
    or      t1, t1, t5 /* | L1_DIV */

    ori     t1, t1, PLL_L1_ENA
    st.d    t1, t0, 0x0

    li.d    a1, PLL_L1_LOCKED
11: //wait_locked_sys:
    ld.d    a0, t0, 0x0
    and     a0, a1, a0
    beqz    a0, 11b //wait_locked_sys

    ld.d    a0, t0, 0x0
    ori     a0, a0, PLL_CHANG_COMMIT
    st.d    a0, t0, 0x0

    bl     hexserial

    TTYDBG ("\r\n clock setting done!\r\n")
#endif /* SOFT_CLKSEL */
