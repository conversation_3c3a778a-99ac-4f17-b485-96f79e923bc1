/*
/*
 * Copyright (c) 2020 <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>@loongson.cn)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */

#ifndef _KERNEL
#define _KERNEL
#endif

#include <platform.h>
#include <asm.h>
#include <regnum.h>
#include <cpu.h>
#include <pte.h>

#include "pmon/dev/ns16550.h"
#include "target/bonito.h"

#include "loongson3_def.h"

LEAF(clear_mailbox)
	csrrd   t0, 0x20
	andi    t1, t0, 0x3    /* core id */
	slli.d  t1, t1, 8

	li.d    t2, (CORES_PER_NODE - 1) & (~0x3)
	and     t2, t0, t2     /* internal node id */
	slli.d  t2, t2, (16 - 2)
	or      t1, t2, t1

	/* get node id shift offset */
	li.d    t2, CORES_PER_NODE
	ctz.w   t2, t2
	li.d    t3, NODE_OFFSET
	sub.d   t2, t3, t2

	li.d    t3, ~(CORES_PER_NODE - 1)
	andi    t3, t3, 0x1ff
	and     t3, t0, t3    /* node id */
	sll.d   t2, t3, t2

	or      t1, t2, t1
	li.d    t2, NODE0_CORE0_BUF0
	or      t1, t1, t2
	st.d    zero, t1, FN_OFF
	st.d    zero, t1, SP_OFF
	st.d    zero, t1, GP_OFF
	li.d    t3, 0x1234
	st.d    t3, t1, A1_OFF

	jirl zero, ra, 0x0
END(clear_mailbox)

LEAF(prepare_scache)
	move	t5, ra

	csrrd   t0, 0x20
	andi    t1, t0, 0x1ff
	slli.d  t4, t1, 39

	andi    t1, t0, 0x1f
	bnez    t1, dont_need_prepare

	li.d	t0, PHYS_TO_UNCACHED(0x1fe00000)
	or      t0, t0, t4
	li.d	t1, ~(LOCK_CACHE_SIZE - 1)
	li.d	t2, (LOCK_CACHE_BASE & 0xffffffffffff) | (1 << 63)
	//or      t2, t2, t4 //lock self-cache
	st.d	t1, t0, 0x248
	st.d	t2, t0, 0x208
#if (defined(LOONGSON_3C6000) || defined(LOONGSON_3D6000)) && defined(FLAT_MODE)
	li.d	t0, PHYS_TO_UNCACHED(0x1fe10000)
	or      t0, t0, t4
	st.d	t1, t0, 0x248
	st.d	t2, t0, 0x208
	li.d	t0, PHYS_TO_UNCACHED(0x1fe20000)
	or      t0, t0, t4
	st.d	t1, t0, 0x248
	st.d	t2, t0, 0x208
	li.d	t0, PHYS_TO_UNCACHED(0x1fe30000)
	or      t0, t0, t4
	st.d	t1, t0, 0x248
	st.d	t2, t0, 0x208
#endif
#if 0 // code for lock ap scache their own.
	/* copy flash code to scache */
	li.d	t1, PHYS_TO_CACHED(0x1c000000)
	la	t0, start
	or      t0, t0, t4
	la	t2, edata
	or      t2, t2, t4
1:
	ld.d	t3, t1, 0
	st.d	t3, t0, 0
	addi.d	t0, t0, 8
	addi.d	t1, t1, 8
	bne	t2, t0, 1b

	ibar    0
	dbar    0

	/*clear cache mem BSS in this space*/
	la	t0, _edata
	or      t0, t0, t4
	la	t1, _end
	or      t1, t1, t4
1:
	st.d	zero, t0, 0
	addi.d	t0, t0, 8
	blt	t0, t1, 1b
#endif

	li.d	t0, LOCK_CACHE_BASE
	li.d	t1, LOCK_CACHE_BASE + LOCK_CACHE_SIZE
#if 0 // code for lock ap scache their own.
	or      t0, t0, t4
	or      t1, t1, t4
#endif
1:
	/* ensure locked cache address accessible so this code can`t remove*/
	ld.d	zero, t0, 0
	addi.d	t0, t0, 0x40
	bne	t1, t0, 1b

dont_need_prepare:
	move	ra, t5
	jirl	zero, ra, 0x0
END(prepare_scache)

	.globl slave_main_entry
slave_main:
	bl	prepare_scache
slave_main_entry:
#ifdef ACPI_SUPPORT
	bl clear_mailbox

	bl get_core_id	//get a0
	/*
	 * don't changing the following register
	 * a0, ap own cpu number set by get_core_id
	 * t2, node 0 mail box address set by clear_mailbox
	 */
1:
	li.w    t0, 0x1000

2:
	addi.w  t0, t0, -1
	bnez    t0, 2b

	ld.w    t0, t1, FN_OFF  //mailbox0
	beqz    t0, 1b

	ld.d    t0, t1, FN_OFF
	move    ra, t0

	ld.d    a1, t1, A1_OFF  //mailbox3

	li.d    t3, PHYS_TO_CACHED(0x90300000)
	slli.d  a2, a0, 0x9
	or      t0, a2, t3
	move    sp, t0

	addi.d  sp, sp, -8

	jirl    zero, ra, 0x0        # jump to initlialize AP info function with "a0=ap cpu number"

	.globl asm_wait_for_kernel
asm_wait_for_kernel:
#endif
	bl      clear_mailbox
	/*
	 * don't changing the following register
	 * t1, each node mail box address
	 */

waitforinit:
	li.w    t0, 0x10

idle1000:
	addi.w  t0, t0, -1
	bnez    t0, idle1000
	/*csr finally filled the low 32 bits*/
	ld.w    t0, t1, FN_OFF
	beqz    t0, waitforinit

	ld.d    t0, t1, FN_OFF
	li.d    t2, CACHED_MEMORY_ADDR
	or      t0, t0, t2
	move    ra, t0

	li.d    t3, CACHED_MEMORY_ADDR

	ld.d    t0, t1, SP_OFF
	or      t0, t0, t3
	move    sp, t0

	ld.d    t0, t1, GP_OFF
	or      t0, t0, t3
	move    gp, t0

#ifdef	ACPI_SUPPORT
	ld.d    a1, t1, A1_OFF
	or      a1, a1, t3
#endif
	/* slave core jump to kernel, byebye */
	jirl    zero, ra, 0x0
	/* end slave_main */
LEAF(get_core_id)
	csrrd   a0, 0x20
	andi    a0, a0, 0x1ff
	jirl    zero, ra, 0x0
END(get_core_id)

LEAF(get_cpuprid)
	cpucfg  a0, zero
	jirl    zero, ra, 0
END(get_cpuprid)

/*
3A5000HV	2.5GHz@1.25V
3A5000		2.5GHz@1.20V
3A5000LL	2.3GHz@1.15V
3A5000M		2.0GHz@1.05V

3A5000K		1.5GHz@0.90V
3A5000K-HV	2.0GHz@1.10V
3A5000i		1.5GHz@0.90V
3A5000i-HV	2.0GHz@1.10V

3B5000		2.3GHz@1.15V

3C5000L		2.2GHz@1.15V
3C5000L-LL	2.0GHz@1.05V
*/

LEAF(chip_ver)
	li.d    a0, PHYS_TO_UNCACHED(0x1fe00460)
#ifdef LOONGSON_3C5000
	ld.w    v0, a0, 0x8
	li.d    a1, 29
	srl.w   v0, v0, a1
	andi    v0, v0, 0xf  //0:3C5000  1:3C5000-LL
#else
#ifdef LOONGSON_3A6000
	li.d    v0, 0x30
	csrwr   v0, 0x32
#else
	ld.w    a2, a0, 0x4
	li.d    a1, 29
	srl.w   a4, a2, a1
	li.d    a1, 13
	srl.w   v0, a2, a1
	andi    v0, v0, 0xff

	li.d    a1, 0x44
	bne     v0, a1, 1f
	li.d    v0, 0x43
1:
	li.d    a1, 0x30
	beq     v0, a1, 5f
	li.d    a1, 0x43
	beq     v0, a1, 3f
	li.d    a1, 0x42
	beq     v0, a1, 3f
	li.d    a1, 0x41
	beq     v0, a1, 3f

	bnez    v0, 2f
	li.d    a1, 7
	srl.w   v0, a2, a1
	andi    a3, v0, 0x7
	li.d    a1, 2
	li.d    v0, 0x42
	beq     a3, a1, 3f
2:
	li.d    v0, 0x43  //default BA version
3:
	li.d    a1, 21
	srl.w   a2, a2, a1
	andi    a2, a2, 0xf

	li.d    a3, 0x5
	li.d    v1, 0x1
	beq     a2, a3, 5f

	li.d    v1, 0x1
	bne     a2, v1, 4f
	beqz    a4, 5f
4:
	li.d    v1, 0xf
5:
	csrwr   v0, 0x32
	csrrd   v0, 0x32
#endif
#endif
	jirl    zero, ra, 0
END(chip_ver)
LEAF(chip_grade)
	li.d    a0, PHYS_TO_UNCACHED(0x1fe00460)
	ld.w    a2, a0, 0x4
	li.d    a1, 21
	srl.w   v0, a2, a1
	andi    v0, v0, 0xf
	jirl    zero, ra, 0
END(chip_grade)

LEAF(ls7a_version)
	li.d    a0, PHYS_TO_UNCACHED(0xefdfe000100)
	ld.bu   a0, a0, 0x8
	jirl    zero, ra, 0
END(ls7a_version)

LEAF(ls7a_perf_enh)
	li.d    a0, PHYS_TO_UNCACHED(0xe0010013ff8)
	ld.w    a1, a0, 0x0
	li.d    a0, 0
	li.d    a2, 0x7A120001
	bne     a1, a2, 1f
	li.d    a0, LS7A_PERF_ENH
1:
	jirl    zero, ra, 0
END(ls7a_perf_enh)
