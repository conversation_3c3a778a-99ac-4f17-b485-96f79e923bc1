#if 1
#include <sys/param.h>
#include <sys/syslog.h>
#include <machine/endian.h>
#include <sys/device.h>
#include <machine/cpu.h>
#include <machine/pio.h>
#include <machine/intr.h>
#include <dev/pci/pcivar.h>
#endif
#include <sys/types.h>
#include <termio.h>
#include <string.h>
#include <time.h>
#include <stdlib.h>

#include <dev/ic/mc146818reg.h>
#include <linux/io.h>

#include <autoconf.h>

#include "pflash.h"
#include "dev/pflash_tgt.h"

#include "target/bonito.h"
#include "target/ls2k1000.h"
#include "target/board.h"
#include <pmon/dev/ns16550.h>

#include <pmon.h>

#include "mod_x86emu_int10.h"
#include "mod_x86emu.h"
#include "mod_vgacon.h"
#include "mod_framebuffer.h"

#define _uart_debug_
#define   DDR_EN                 0

u8     I2CADR = 0x33; //0x5a

void i2c_init(int speed,  int slaveaddr);
int i2c_read(u8 chip, uint addr, int alen, u8 *buffer, int len);
int i2c_write(u8 chip, uint addr, int alen, u8 *buffer, int len);

typedef struct LT9721_PARA
{
	//LT9721 video parameter	
	u16 H_Total ;			  
	u16 H_Start ;  
	u16 H_Width;
	u16 H_Active;	
	u16 V_Total ;			  
	u16 V_Start ;  
	u16 V_Width;
	u16 V_Active;
	
	u8 resolution;
	Lane_Num lane_num;
	Lane_Rate link_rate;
	Color_Depth color_depth;	
};
struct LT9721_PARA g_Lt9721_info;
u32 cnt=0,backset=0,backset1,backset2,backset3;

#define  gpioi2c_write(chip, reg, data)  i2c_write(chip, reg, 1, data, 1);
#define  gpioi2c_read(chip, reg, data) i2c_read(chip, reg, 1, data, 1);
typedef enum
{ 
  Bit_RESET = 0,
  Bit_SET = !Bit_RESET
}bool;


typedef enum LT9211_OUTPUTMODE_ENUM
{
    OUTPUT_RGB888=0,
    OUTPUT_BT656_8BIT=1,
    OUTPUT_BT1120_16BIT=2,
    OUTPUT_LVDS_2_PORT=3,
    OUTPUT_LVDS_1_PORT=4,
    OUTPUT_YCbCr444=5,
    OUTPUT_YCbCr422_16BIT
};
#define LT9211_OutPutModde  OUTPUT_LVDS_1_PORT

typedef enum VIDEO_INPUTMODE_ENUM
{
    Input_RGB888,
    Input_YCbCr444,
    Input_YCbCr422_16BIT
}
_Video_Input_Mode;

#define Video_Input_Mode  Input_RGB888


//#define lvds_format_JEIDA

//#define lvds_sync_de_only


//////////option for debug///////////


typedef struct video_timing{
    u16 hfp;
    u16 hs;
    u16 hbp;
    u16 hact;
    u16 htotal;
    u16 vfp;
    u16 vs;
    u16 vbp;
    u16 vact;
    u16 vtotal;
    u32 pclk_khz;
};

typedef enum VideoFormat
{
    video_384x292_60Hz_vic,
    video_640x480_60Hz_vic,
    video_1280x720_60Hz_vic,
    video_1366x768_60Hz_vic,
    video_1280x1024_60Hz_vic,
    video_1920x1080_60Hz_vic,
    video_1920x1200_60Hz_vic,
    video_1920x1080_25Hz_vic,
    video_none
};

typedef struct Lane_No{
u8	swing_high_byte;
u8	swing_low_byte;
u8	emph_high_byte;
u8	emph_low_byte;
};
u16 hact, vact;
u16 hs, vs;
u16 hbp, vbp;
u16 htotal, vtotal;
u16 hfp, vfp;
u8 VideoFormat=0;

enum VideoFormat Video_Format;
struct video_timing TimingStr;

#define print printf
//hfp, hs, hbp,hact,htotal,vfp, vs, vbp, vact,vtotal,
struct video_timing video_640x480_60Hz     ={ 8, 96,  40, 640,   800, 33,  2,  10, 480,   525,  25000};
struct video_timing video_720x480_60Hz     ={16, 62,  60, 720,   858,  9,  6,  30, 480,   525,  27000};
struct video_timing video_1280x720_60Hz    ={110,40, 220,1280,  1650,  5,  5,  20, 720,   750,  74250};
struct video_timing video_1280x720_30Hz    ={110,40, 220,1280,  1650,  5,  5,  20, 720,   750,  37125};
struct video_timing video_1366x768_60Hz    ={26, 110,110,1366,  1592,  13, 6,  13, 768,   800,  81000};
struct video_timing video_1920x1080_30Hz   ={88, 44, 148,1920,  2200,  4,  5,  36, 1080, 1125,  74250};
struct video_timing video_1920x1080_60Hz   ={88, 44, 148,1920,  2200,  4,  5,  36, 1080, 1125, 148500};
struct video_timing video_3840x1080_60Hz   ={176,88, 296,3840,  4400,  4,  5,  36, 1080, 1125, 297000};
struct video_timing video_1920x1200_60Hz   ={48, 32,  80,1920,  2080,  3,  6,  26, 1200, 1235, 154000};
struct video_timing video_3840x2160_30Hz   ={176,88, 296,3840,  4400,  8,  10, 72, 2160, 2250, 297000};
struct video_timing video_3840x2160_60Hz   ={176,88, 296,3840,  4400,  8,  10, 72, 2160, 2250, 594000};

struct video_timing video_1920x1080_25Hz   ={528, 44, 148,1920,  2640,  4,  5,  36, 1080, 1125,  74250};


void printByte(u8 UData)
{
  u8 reg_H,reg_L;
  reg_L=UData&0x0f;
  reg_H=(UData&0xf0)>>4;
 
  reg_L = (reg_L<0x0a) ? (reg_L+0x30):(reg_L+0x37);   //map reg_L to asicc 'a' to a (0x41),   a = 0x37 + 0x0a = 0x41
  reg_H = (reg_H<0x0a) ? (reg_H+0x30):(reg_H+0x37);	
  //Send_Data_To_UART0(reg_H);
  //Send_Data_To_UART0(reg_L);	
 // Send_Data_To_UART0(0x20); //space	

  printf ("%x",reg_H);
   printf ("%x ",reg_L);
}

void printnewline(void)
{
	//Send_Data_To_UART0(0x0a);   //huanhang
	  printf ("\n");
}


void Debug_printf(unsigned char *str)
{ 
//#ifdef _uart_debug_
//	printf(str);	
//#endif
printf ("%s \n",str);
}

void printWord(u16 U16Data)
{
	//printByte((U16Data&0xFF00)>>8);
	//printByte((U16Data&0x00FF));
	//Send_Data_To_UART0(0x0a);   //???
	printf ("%x \n",U16Data);
}


void printByteWithSpace(u8 U8Data)
{
	//Send_Data_To_UART0(U8Data);
	//Send_Data_To_UART0(0x20);   //space
     //0x3c |,  0x3e ~
    printf ("%x ",U8Data);
}
/*
³ö³§Ç°£¬Ã¿¿Å N76E003¶¼»áÔ¤ÉÕÒ»¸ö96Î»µÄÐòÁÐºÅ
£¬ÓÃÒÔÈ·±£¸ÃÐ¾Æ¬µÄÎ¨Ò»ÐÔ£¬Õâ¸öÎ¨Ò»´úÂë±»³ÆÎª
ÐòÁÐºÅUID (Unique Code)¡£ÓÃ»§»ñµÃÐòÁÐºÅµÄÎ¨
Ò»·½Ê½ÊÇÍ¨¹ý IAPÖ¸Áî¶ÁÈ¡£¬
*/
UINT8 UID_BYTE(UINT8 Addr)
{
#if 0
	UINT8 DATATEMP;
	set_IAPEN;
	IAPAL = Addr;
  	IAPAH = 0x00;
  	IAPCN = READ_UID;
  	set_IAPGO;
	DATATEMP = IAPFD;
	clr_IAPEN;
	return DATATEMP;
	#endif
	return 0x55;
}

void READ_MCU_UID(void)
{
    #if 0
	UINT8 READ1,READ2;

	READ1 = UID_BYTE(0x02);
	READ2 = UID_BYTE(0x05);

	printf ("\n UID1 = 0x%bx",READ1);
	printf ("\n UID2 = 0x%bx",READ2);
	#endif
}



void printch(char ch)
{
	 //Send_Data_To_UART0(ch);
	 printf("%c",ch);
}

void printdec(int dec)
{
    if((dec/10) < 10)
    {
        printch((char)((dec/10) + '0'));
    }
    else
    {
        printch((char)((dec/10) - 10 + 'a' ));
    }	 

    if((dec%10) < 10)
    {
        printch((char)(dec%10 + '0'));
    }
    else
    {
        printch((char)(dec%10 - 10 + 'a' ));
    }
}

void printhex(int hex)
{	  
    printf("0x");
	Timer0_Delay1ms(2);

    if((hex/16) < 10)
    {
        printch((char)((hex/16) + '0'));
    }
    else
    {
        printch((char)((hex/16) - 10 + 'a' ));
    }	 

    if((hex%16) < 10)
    {
        printch((char)(hex%16 + '0'));
    }
    else
    {
        printch((char)(hex%16 - 10 + 'a' ));
    }
}


/* max input 99999999*/
void printdec_u32(u32 u32_dec)
{
#ifdef _uart_debug_
    bool start_flag = 0;
	u32 dec = 0;
	dec = u32_dec;

    printch((char)(' '));  //print blank£»

	if(dec > 9999999)
	{
	   if((dec/10000000) < 10)
	    {
	        printch((char)((dec/10000000) + '0'));
	    }
	    else
	    {
	        printch((char)((dec/10000000) - 10 + 'a' ));
    	}
		dec %= 10000000;
		start_flag = 1;	
	}

	if(dec > 999999)
	{
	   if((dec/1000000) < 10)
	    {
	        printch((char)((dec/1000000) + '0'));
	    }
	    else
	    {
	        printch((char)((dec/1000000) - 10 + 'a' ));
    	}
		dec %= 1000000;
		start_flag = 1;		
	}else
	{
	   if(start_flag)
	   printch((char)('0'));
	}


   	if(dec > 99999)
	{
	   if((dec/100000) < 10)
	    {
	        printch((char)((dec/100000) + '0'));
	    }
	    else
	    {
	        printch((char)((dec/100000) - 10 + 'a' ));
    	}
		dec %= 100000;
		start_flag = 1;		
	}else
	{
	   if(start_flag)
	   printch((char)('0'));
	}

	if(dec > 9999)
	{
	   if((dec/10000) < 10)
	    {
	        printch((char)((dec/10000) + '0'));
	    }
	    else
	    {
	        printch((char)((dec/10000) - 10 + 'a' ));
    	}
		dec %= 10000;
		start_flag = 1;		
	}else
	{
	   if(start_flag)
	   printch((char)('0'));
	}

	if(dec > 999)
	{
	   if((dec/1000) < 10)
	    {
	        printch((char)((dec/1000) + '0'));
	    }
	    else
	    {
	        printch((char)((dec/1000) - 10 + 'a' ));
    	}
		dec %= 1000;
		start_flag = 1;		
	}else
	{
	   if(start_flag)
	   printch((char)('0'));
	}

	if(dec > 99)
	{
	   if((dec/100) < 10)
	    {
	        printch((char)((dec/100) + '0'));
	    }
	    else
	    {
	        printch((char)((dec/100) - 10 + 'a' ));
    	}
		dec %= 100;
		start_flag = 1;		
	}
	else
	{
	   if(start_flag)
	   printch((char)('0'));
	}

	if((dec/10) < 10)
    {
        printch((char)((dec/10) + '0'));
    }
    else
    {
        printch((char)((dec/10) - 10 + 'a' ));
    }	 

    if((dec%10) < 10)
    {
        printch((char)(dec%10 + '0'));
    }
    else
    {
        printch((char)(dec%10 - 10 + 'a' ));
    }
#endif
}

void Timer0_Delay1ms(UINT32 u32CNT)
{
        delay(1000*u32CNT);                      	
}
u8 HDMI_ReadI2C_Byte(u8 RegAddr)
{
	u8  p_data=0;
	gpioi2c_read(I2CADR,RegAddr,&p_data);
	return p_data;
}
bool HDMI_WriteI2C_Byte(u8 RegAddr, u8 d)
{
gpioi2c_write(I2CADR, RegAddr,&d);
return TRUE;
}

void ParameterInit(void)
{
	//MSA	1080P	H active: H blank: H offset: H width:	  V active: V blank: V offset: V width	
	//1080P 1920:160:46:30:	 1080:32:2:4	2_Lane	 2.7GBps   8Bit  CHIMEI_N133HSE-EA1
	//1366*768   1366:160:48:32	 768:22:3:6 	 1_Lane   2.7GBps	6Bit PANDA
	//1080P 1920:280:88:44:  1080:45:4:5	 2_Lane   2.7GBps	6Bit PANDA-LM156LF1L02
	//1080P               2_Lane	 2.7GBps   8Bit                       PANDA-LC133LF4L01
	//1080P   1920:160:48:32:  1080:31:3:5    edp1.2  2_Lane/2.7GBps/8Bit   PANDA-LC116LF1L01
	#if 1	
	LCD_info.H_Active =  1920 ;
	LCD_info.H_Black =  160; 
	LCD_info.H_Sync_Offset =  48;
	LCD_info.H_Sync_Pulse_Width =	32;
	LCD_info.V_Active = 1080; 
	LCD_info.V_Black =  31; 
	LCD_info.V_Sync_Offset = 3;
	LCD_info.V_Sync_Pulse_Width = 5;
	#else
	LCD_info.H_Active =  1366 ;
	LCD_info.H_Black =  160; 
	LCD_info.H_Sync_Offset =  48;
	LCD_info.H_Sync_Pulse_Width =	32;
	LCD_info.V_Active = 768; 
	LCD_info.V_Black =  22; 
	LCD_info.V_Sync_Offset = 3;
	LCD_info.V_Sync_Pulse_Width = 6;
	#endif
	g_Lt9721_info.resolution =  0x08;
	g_Lt9721_info.H_Total = LCD_info.H_Active + LCD_info.H_Black;
	g_Lt9721_info.H_Start = LCD_info.H_Black - LCD_info.H_Sync_Offset;
	g_Lt9721_info.H_Width = LCD_info.H_Sync_Pulse_Width;
	g_Lt9721_info.H_Active = LCD_info.H_Active; 
	g_Lt9721_info.V_Total = LCD_info.V_Active + LCD_info.V_Black;
	g_Lt9721_info.V_Start = LCD_info.V_Black - LCD_info.V_Sync_Offset;
	g_Lt9721_info.V_Width = LCD_info.V_Sync_Pulse_Width;
	g_Lt9721_info.V_Active = LCD_info.V_Active;

	//refer TFT_LCD spec pin assignment
	g_Lt9721_info.lane_num = _2_Lane_; //Two_Lane;//2;  // 1, 2, 4  
	g_Lt9721_info.link_rate = PerLane27GBps;	// 1:2:3= 1.62:2.7:5.4
	//refer display color to define color_depth, Display color  RGB666 = 26* 26* 26, RGB888 = 28 *28 *28
	g_Lt9721_info.color_depth = RGB_8Bit; //2;  // RGB666:RGB888 = 1:2
}

 
void ChipID(void)
{ u8 a,b;
	HDMI_WriteI2C_Byte(0xff,0x60);
	HDMI_ReadI2C_Byte(0x00); 
	HDMI_ReadI2C_Byte(0x01);
	Debug_printf("LT8718  CHIP ID:");
	a=HDMI_ReadI2C_Byte(0x00);
	b=HDMI_ReadI2C_Byte(0x01);
	printByteWithSpace(a);
	printByteWithSpace(b);	
	printnewline();
}

void BackLight(void)
{
	HDMI_WriteI2C_Byte(0xff,0x70);	
	HDMI_WriteI2C_Byte(0x44,0x0c);
	HDMI_WriteI2C_Byte(0xff,0x80);
	HDMI_WriteI2C_Byte(0xd1,0x0c);
	HDMI_WriteI2C_Byte(0xd3,0x00);	
		//µ÷ÕûÁÁ¶È³õÊ¼»¯¼Ä´æÆ÷
	HDMI_WriteI2C_Byte(0xd3,0x30);  //00	
	HDMI_WriteI2C_Byte(0xc8,0x20);  //00	
	//µ÷ÕûpwmµÄËÙÂÊ
	HDMI_WriteI2C_Byte(0xc9,0x00);  //00	
	HDMI_WriteI2C_Byte(0xca,0x27);  //00	
	HDMI_WriteI2C_Byte(0xcb,0x10);  //00
	//µ÷ÕûpwmµÄ¸ßµçÆ½Õ¼¿Õ±È
	HDMI_WriteI2C_Byte(0xcc,0x00);  //00	
	HDMI_WriteI2C_Byte(0xcd,0x27);  //00	
	HDMI_WriteI2C_Byte(0xce,0x10);  //00	
}

void RxPHY(void)
{
	HDMI_WriteI2C_Byte(0xff,0x70);
	HDMI_WriteI2C_Byte(0x13,0x03);
	HDMI_WriteI2C_Byte(0x14,0x24);//RGB mode Enable
	HDMI_WriteI2C_Byte(0xff,0x80);
	HDMI_WriteI2C_Byte(0x06,0x03);//Rx source from TTL
	HDMI_WriteI2C_Byte(0x53,0xc0);//sync pol adj---guoxianghao
}

void RxPLL(void)
{
	HDMI_WriteI2C_Byte(0xff,0x70);
	HDMI_WriteI2C_Byte(0x16,0x40);//Pll ref clk from Pixel clk
	HDMI_WriteI2C_Byte(0x18,0x13);//Loop mode sel TTL	
	HDMI_WriteI2C_Byte(0x19,0x1e);//Enable Pll Lock
	HDMI_WriteI2C_Byte(0x1a,0xa4);//RGB clk from external pixel clk
	
		#if DDR_EN	//   DDR MODE
	HDMI_WriteI2C_Byte(0x16,0x60);//Pll ref clk from Pixel cl
	HDMI_WriteI2C_Byte(0x18,0x13);//Loop mode sel TTL	
	HDMI_WriteI2C_Byte(0x1a,0x24);//RGB clk from external pixel clk
	HDMI_WriteI2C_Byte(0x19,0x1e);//Enable Pll Lock
  #endif

}

void TypecMode(void)
{
	HDMI_WriteI2C_Byte(0xff,0x70);
	HDMI_WriteI2C_Byte(0x48,0x00);//DP 4-lane only mode
}	


void TxPHY(void)
{
	HDMI_WriteI2C_Byte(0xff,0x70);
	HDMI_WriteI2C_Byte(0x21,0x0d);
	HDMI_WriteI2C_Byte(0x21,0x0f);//Enable DP TX
	//====Lane Manager========
	HDMI_WriteI2C_Byte(0x22,0x77);//Enable Lane0 and Lane1; Enable Tap0 and Tap1
	HDMI_WriteI2C_Byte(0x23,0x77);//Disable Lane2 and Lane3; Diable Tap0 and Tap1
	HDMI_WriteI2C_Byte(0x24,0x80);//Lane0 Swing: 16mA x 25ohm = 400 mV
	HDMI_WriteI2C_Byte(0x25,0x00);//Lane0 Pre-emphasis 0 mV
	HDMI_WriteI2C_Byte(0x26,0x80);//Lane1 Swing: 16mA x 25ohm = 400 mV
	HDMI_WriteI2C_Byte(0x27,0x00);//Lane1 Pre-emphasis 0 mV
	HDMI_WriteI2C_Byte(0x28,0x80);//Lane2 Swing: 16mA x 25ohm = 400 mV
	HDMI_WriteI2C_Byte(0x29,0x00);//Lane2 Pre-emphasis 0 mV
	HDMI_WriteI2C_Byte(0x2a,0x80);//Lane3 Swing: 16mA x 25ohm = 400 mV
	HDMI_WriteI2C_Byte(0x2b,0x00);//Lane3 Pre-emphasis 0 mV
	HDMI_WriteI2C_Byte(0x2c,0xb0);
	HDMI_WriteI2C_Byte(0x2c,0xf0);//Tx Rterm calibration reset
	//=====AUX PHY=============
	HDMI_WriteI2C_Byte(0x2f,0x70);//Enable AUX PHY 
	HDMI_WriteI2C_Byte(0x30,0x24);//Pull-up register sel 100K 
	HDMI_WriteI2C_Byte(0x31,0xFC);//AUX + CC
	HDMI_WriteI2C_Byte(0xff,0x80);
	HDMI_WriteI2C_Byte(0x30,0x0e);//Aux_clk divider 15; clk_frq: 30M/15 = 2M
}

void TxPLL(u8 LinkRate)
{
/***********2.7G: LinkRate 1, 1.62G: LinkRate 0,***************/
	HDMI_WriteI2C_Byte(0xff,0x80);
	HDMI_WriteI2C_Byte(0x40,0x22);
	if (PerLane162GBps== LinkRate)
	{
		HDMI_WriteI2C_Byte(0x41,0x20);
		HDMI_WriteI2C_Byte(0x42,0x99);
		HDMI_WriteI2C_Byte(0x43,0x99);
	}
	else if (PerLane27GBps== LinkRate)
	{
		HDMI_WriteI2C_Byte(0x41,0x1b);
		HDMI_WriteI2C_Byte(0x42,0x00);
		HDMI_WriteI2C_Byte(0x43,0x80);
	}
	//Analog
	HDMI_WriteI2C_Byte(0xff,0x70);
	HDMI_WriteI2C_Byte(0x1c,0x18);
	HDMI_WriteI2C_Byte(0x1d,0x42);
	HDMI_WriteI2C_Byte(0x1e,0x00);
	HDMI_WriteI2C_Byte(0x1e,0x01);
	if (PerLane27GBps== LinkRate)
	{
		HDMI_WriteI2C_Byte(0x1f,0x2b);
		HDMI_WriteI2C_Byte(0x1c,0x14);
	}
	
	//Digital
	HDMI_WriteI2C_Byte(0xff,0x60);
	HDMI_WriteI2C_Byte(0x1e,0xbf);	
	HDMI_WriteI2C_Byte(0x1e,0xff);
	//Analog
	HDMI_WriteI2C_Byte(0xff,0x70);
	if (PerLane27GBps== LinkRate)
	{
		HDMI_WriteI2C_Byte(0x1f,0x13);
	}
	else if (PerLane162GBps== LinkRate)
	{
		HDMI_WriteI2C_Byte(0x1f,0x19);
		HDMI_WriteI2C_Byte(0x1f,0x1b);
	}
		
	//Digital
	HDMI_WriteI2C_Byte(0xff,0x80);	
	HDMI_WriteI2C_Byte(0x44,0x41);
	if (PerLane162GBps== LinkRate)
	{
		HDMI_WriteI2C_Byte(0x45,0x83);
		HDMI_WriteI2C_Byte(0x46,0x06);
		HDMI_WriteI2C_Byte(0x48,0x06);
	}	
	else if(PerLane27GBps== LinkRate)
	{
		HDMI_WriteI2C_Byte(0x45,0x03);
		HDMI_WriteI2C_Byte(0x46,0x0a);
		HDMI_WriteI2C_Byte(0x48,0x0a);		
	}
	HDMI_WriteI2C_Byte(0x40,0x22);//0x62: ssc en; 0x22: ssc_dis
	
}


/*
Total=active+blanking
blanking=frontporch + syncwidth + backporch
Start= syncwidth + backporch = blanking - frontporch    (H active start point)
sync offset = frontporch
width = sync pulse width = Hor Sync time

 H_Sync ___________                   Hor active start                          Hor black start   Hor Sync start
_______|----------|-----------|---------------------------|-------------|
         Hor_Sync_time   H_back_porch           H_active                           H_front_porch 
           
point:                      width              start                                                               total
            |----------|-----------|-------total----------------|-------------|
            
*/

void VideoProcessor(struct LT9721_PARA g_Lt9721_info)
{
	HDMI_WriteI2C_Byte(0xff,0x88);
	HDMI_WriteI2C_Byte(0x00,0x4a);//Rerverse 12bit pixel input 
	HDMI_WriteI2C_Byte(0x01,0x02);
	HDMI_WriteI2C_Byte(0x02,0x10);
	HDMI_WriteI2C_Byte(0x03,0x01);	
	HDMI_WriteI2C_Byte(0x04,0xff);//Idle number

//=========MSA===============
	//1080p
	//h_totel 2200
	HDMI_WriteI2C_Byte(0x05,0x08);//Htotal[15:8]
	HDMI_WriteI2C_Byte(0x06,0x98);//Htotal[7:0]
	//hs_time+h_back_porch  44+148=192
	HDMI_WriteI2C_Byte(0x07,0x00);//Hstart[15:8]
	HDMI_WriteI2C_Byte(0x08,0xc0);//Hstart[7:0]
	//hs_time 44
	HDMI_WriteI2C_Byte(0x09,0x00);//Hsync polarity; HsyncWidth[14:8]
	HDMI_WriteI2C_Byte(0x0a,0x2c);//HsyncWidth[7:0]
	//hactive 1920
	HDMI_WriteI2C_Byte(0x0b,0x07);//Hactive[15:8]
	HDMI_WriteI2C_Byte(0x0c,0x80);//Hactive[7:0]
	//vtotal 1125
	HDMI_WriteI2C_Byte(0x0d,0x04);//Vtotal[15:8]
	HDMI_WriteI2C_Byte(0x0e,0x65);//Vtotal[7:0
	//i  
	HDMI_WriteI2C_Byte(0x0f,0x02);
	HDMI_WriteI2C_Byte(0x10,0x33);
	//Vs_time+V_back_porch  36+5=41
	HDMI_WriteI2C_Byte(0x11,0x00);//Vstart[15:8]
	HDMI_WriteI2C_Byte(0x12,0x29);//Vstart[7:0]
	//Vs_time 5
	HDMI_WriteI2C_Byte(0x13,0x00);//Vsync polarity; VsyncWidth[14:8]
	HDMI_WriteI2C_Byte(0x14,0x05);//VsyncWidth[7:0]
	//V_active 1080
	HDMI_WriteI2C_Byte(0x15,0x04);//Vactive[15:8]
	HDMI_WriteI2C_Byte(0x16,0x38);//Vactive[7:0]
	
    /*
    0X1A 0X1B 0X1C These three parameters need to be calculated based
    on the number of lane bars.
    The value of total_byte_line = (hwidth/lane_cnt)*BPP/8.
    for example:RGB888-1080P-2lanes:(1920/2)*24/8=2880.
	*/
	HDMI_WriteI2C_Byte(0x1a,0x00);//Sdp; Lane_Byte[19:16]
	HDMI_WriteI2C_Byte(0x1b,0x0b);//Lane_Byte[15:8]
	HDMI_WriteI2C_Byte(0x1c,0x40);//Lane_Byte[7:0]

	
	
	HDMI_WriteI2C_Byte(0x17,0x08);//Format: RGB; Depth: 8bit
	HDMI_WriteI2C_Byte(0x18,0x20);//MISC0 Value: 8bit
	HDMI_WriteI2C_Byte(0x19,0x00);//MISC1 value
	

	
	HDMI_WriteI2C_Byte(0x1d,0x36);//Pixels_Onetime; Pixels_Holdtime
	HDMI_WriteI2C_Byte(0x1e,0x30);//VIDEO_EDY control: Hardware; FIFO_Empty[4:3]
	HDMI_WriteI2C_Byte(0x1f,0x4e);//FIFO_Empty[2:0]; FIFO_Full[4:0]
	HDMI_WriteI2C_Byte(0x20,0x66);//Out_HighByte; Out_LowByte
	HDMI_WriteI2C_Byte(0x21,0x1b);//Tu_Size Value control: Hardware
	HDMI_WriteI2C_Byte(0x22,0x09);//VB_ID in Idle
	HDMI_WriteI2C_Byte(0x23,0xff);
//	HDMI_WriteI2C_Byte(0x25,0x47);//Pttern Image; Pattern Data;
//	HDMI_WriteI2C_Byte(0x26,0xff);//Pattern data value
//	HDMI_WriteI2C_Byte(0x27,0x01);//Video Pattern: Color ramps
	HDMI_WriteI2C_Byte(0x4b,0xf2);//Source sel from pre-pattern
	HDMI_WriteI2C_Byte(0x4c,0x00);
	HDMI_WriteI2C_Byte(0x4d,0x00);
}

void MainLink(void)
{
	//TPS control
	HDMI_WriteI2C_Byte(0xff,0x84);
	HDMI_WriteI2C_Byte(0x00,0x00);
	//=====Tx Lane Swap============
	HDMI_WriteI2C_Byte(0x15,0x8d);//ref to Demo EVB
	//=====Polarity Swap===========
	HDMI_WriteI2C_Byte(0x16,0xcf);	
}

void Audio_IIS(void)
{
	HDMI_WriteI2C_Byte(0xff,0x88);
	HDMI_WriteI2C_Byte(0x1a,0x00);//Sdp enable
	HDMI_WriteI2C_Byte(0xff,0x80);
	HDMI_WriteI2C_Byte(0x06,0x23);//Video from mipi; Audio from external
	HDMI_WriteI2C_Byte(0xff,0x90);
	//HDMI_WriteI2C_Byte(0x07,0x80);
	HDMI_WriteI2C_Byte(0x08,0x8f);	
	HDMI_WriteI2C_Byte(0xff,0x8c);
	HDMI_WriteI2C_Byte(0x42,0x08);
	HDMI_WriteI2C_Byte(0x43,0x10);
	HDMI_WriteI2C_Byte(0x44,0x00);
	HDMI_WriteI2C_Byte(0x45,0x00);
	HDMI_WriteI2C_Byte(0x0e,0x09);
	HDMI_WriteI2C_Byte(0x4c,0x00);	
	HDMI_WriteI2C_Byte(0x50,0x01);	
	HDMI_WriteI2C_Byte(0x51,0xf8);	
}

void TTL_RGB_MODE_INPUT_SETTING(void)
{
	HDMI_WriteI2C_Byte(0xff,0x88);                     
	HDMI_WriteI2C_Byte(0x1e,0x20);
	HDMI_WriteI2C_Byte(0xff,0x80);	
	HDMI_WriteI2C_Byte(0x76,0x20);	//clk ²ÉÑù¼«ÐÔ	
	HDMI_WriteI2C_Byte(0x52,0x00);
	HDMI_WriteI2C_Byte(0xff,0x88);	
	HDMI_WriteI2C_Byte(0x1a,0x30);
	HDMI_WriteI2C_Byte(0x4b,0x92); //0x90-->0x92---guoxianghao
	HDMI_WriteI2C_Byte(0xff,0x80);
	HDMI_WriteI2C_Byte(0x74,0x28); //dit
	#if DDR_EN
	HDMI_WriteI2C_Byte(0xff,0x70);
	HDMI_WriteI2C_Byte(0x1b,0x06); //dit
  #endif
	
    Timer0_Delay1ms(500);
}
	
void HDMI_Format(void)
{
	u8 Format = 0x00;
	u8 Polarity =0x00;
	u16 LaneByteH = 0x0000;
	u16 LaneByteL = 0x0000;
	u16 LaneByte = 0x0000;
	HDMI_WriteI2C_Byte(0xff,0x90);
	Format = HDMI_ReadI2C_Byte(0x71)&0x60;
	HDMI_WriteI2C_Byte(0xff,0x88);	
	Polarity = HDMI_ReadI2C_Byte(0x45)&0x03;
	LaneByteH = HDMI_ReadI2C_Byte(0x33);
	LaneByteL = HDMI_ReadI2C_Byte(0x34);
	Debug_printf(" video check rdy:");	
	printByte(HDMI_ReadI2C_Byte(0x24)); 

	Debug_printf(" video check rdy:");	
	printByte(HDMI_ReadI2C_Byte(0x1e)); 
	
	Debug_printf(" HDMI_Format ***Format, Polarity, LaneByte:");	
	printByteWithSpace(Format);
	printByteWithSpace(Polarity);
	printByteWithSpace(LaneByteH);
	printByte(LaneByteL);
	
	Debug_printf(" vs wid:");	
	printByte(HDMI_ReadI2C_Byte(0x2e)); 
	Debug_printf(" vs bp:");	
	printByte(HDMI_ReadI2C_Byte(0x2f)); 
	Debug_printf(" vs fp:");
	printByte(HDMI_ReadI2C_Byte(0x30)); 
	Debug_printf(" htatol:");
	printByte(HDMI_ReadI2C_Byte(0x31)); 
	printByte(HDMI_ReadI2C_Byte(0x32)); 	
	Debug_printf(" hact ");
	printByteWithSpace(HDMI_ReadI2C_Byte(0x33)); 
	printByteWithSpace(HDMI_ReadI2C_Byte(0x34));
	printnewline( );
	
	Debug_printf("VID_VS_ACT[11:8] bit7-4  VID_HS_WID[11:8]bit3-0");
	printByte(HDMI_ReadI2C_Byte(0x3c)); 
	Debug_printf("VID_HS_WID[7:0]");
	printByte(HDMI_ReadI2C_Byte(0x3d));
	Debug_printf("VID_VS_ACT[7:0]");
	printByte(HDMI_ReadI2C_Byte(0x3e));	
	Debug_printf("VID_HS_BP[11:8]");
	printByte(HDMI_ReadI2C_Byte(0x3f));	
	Debug_printf("VID_HS_BP[7:0]");
	printByte(HDMI_ReadI2C_Byte(0x40));	
	
	Debug_printf("VID_VS_TOTAL[11:8] bit7-4 VID_HS_FP[11:8] bit3-0");
	printByte(HDMI_ReadI2C_Byte(0x42));	
	Debug_printf("VID_VS_TOTAL[7:0]");
	printByte(HDMI_ReadI2C_Byte(0x44));	
	
	switch(Polarity)
	{
		case 0x00: 
								HDMI_WriteI2C_Byte(0x1a,0x30);
								HDMI_WriteI2C_Byte(0x4b,0xe0);
								break;
		case 0x01:
								HDMI_WriteI2C_Byte(0x1a,0x10);
								HDMI_WriteI2C_Byte(0x4b,0xe2);
								break;
		case 0x02:
								HDMI_WriteI2C_Byte(0x1a,0x20);
								HDMI_WriteI2C_Byte(0x4b,0xe0);
								break;
		case 0x03:
								HDMI_WriteI2C_Byte(0x1a,0x00);
								HDMI_WriteI2C_Byte(0x4b,0xe2);
								break;
	}
	LaneByte |= LaneByteH;
	LaneByte <<= 8;
	LaneByte |= LaneByteL;
//	LaneByte = LaneByte*3/2;
//	LaneByte = LaneByte*9/8;
	 if (g_Lt9721_info.color_depth == RGB_8Bit )
	 {
		 LaneByte = (LaneByte*24/8)/(g_Lt9721_info.lane_num);
	 }
	 else if ( g_Lt9721_info.color_depth == RGB_6Bit )
	 {
		LaneByte = (LaneByte*18/8)/(g_Lt9721_info.lane_num); /*The value of total_byte_line =(hwidth/lane_cnt)*BPP/8*/
	 }
	HDMI_WriteI2C_Byte(0x1b,LaneByte>>8);//Lane_Byte[15:8]
	HDMI_WriteI2C_Byte(0x1c,(LaneByte));//Lane_Byte[7:0]
	//HDMI_WriteI2C_Byte(0x1b,0x0b);//Lane_Byte[15:8]
	//HDMI_WriteI2C_Byte(0x1c,0x40);//Lane_Byte[7:0]

	if(Format == 0x00)
	{
	 if (g_Lt9721_info.color_depth == RGB_8Bit )
	 {
		HDMI_WriteI2C_Byte(0x17,0x08);//Format: RGB; Depth: 8bit
		HDMI_WriteI2C_Byte(0x18,0x20);//MISC0 Value: 8bit
		HDMI_WriteI2C_Byte(0x1d,0x36);//Pixels_Onetime; Pixels_Holdtime //before£º0x59
		HDMI_WriteI2C_Byte(0x1f,0x4e);//FIFO_Empty[2:0]; FIFO_Full[4:0]
		HDMI_WriteI2C_Byte(0x20,0x66);//Out_HighByte; Out_LowByte
	 }
	 else if ( g_Lt9721_info.color_depth == RGB_6Bit )
	 { 	 // Debug_printf("color_depth == RGB_6Bit \n");	
		HDMI_WriteI2C_Byte(0x17,0x00);	//Format(7~6): RGB; Depth (5~3) bit3: 8bit	 (8:1, 6:0)
		HDMI_WriteI2C_Byte(0x18,0x00);	//	Depth (7~5) bit5: 8bit	(8:1, 6:0)	 
	 }	
		return;
	}

	if(Format == 0x20)
	{
		HDMI_WriteI2C_Byte(0x17,0x48);//Format: YCbCr422; Depth: 8bit
		HDMI_WriteI2C_Byte(0x18,0x22);//MISC0 Value: 8bit; YCbCr422
		HDMI_WriteI2C_Byte(0x1d,0x34);//Pixels_Onetime; Pixels_Holdtime 
		HDMI_WriteI2C_Byte(0x1f,0x2e);//FIFO_Empty[2:0]; FIFO_Full[4:0]
		HDMI_WriteI2C_Byte(0x20,0x44);//Out_HighByte; Out_LowByte	
		return;
	}
	if(Format == 0x40)
	{
		HDMI_WriteI2C_Byte(0x17,0x88);//Format: YCbCr422; Depth: 8bit
		HDMI_WriteI2C_Byte(0x18,0x24);//MISC0 Value: 8bit; YCbCr444
		HDMI_WriteI2C_Byte(0x1d,0x36);//Pixels_Onetime; Pixels_Holdtime 
		HDMI_WriteI2C_Byte(0x1f,0x4e);//FIFO_Empty[2:0]; FIFO_Full[4:0]
		HDMI_WriteI2C_Byte(0x20,0x66);//Out_HighByte; Out_LowByte	
		return;
	}
	if(Format == 0x60)
	{
		HDMI_WriteI2C_Byte(0x17,0x88);//Format: YCbCr422; Depth: 8bit
		HDMI_WriteI2C_Byte(0x18,0x24);//MISC0 Value: 8bit; YCbCr444
		HDMI_WriteI2C_Byte(0x1d,0x36);//Pixels_Onetime; Pixels_Holdtime 
		HDMI_WriteI2C_Byte(0x1f,0x4e);//FIFO_Empty[2:0]; FIFO_Full[4:0]
		HDMI_WriteI2C_Byte(0x20,0x66);//Out_HighByte; Out_LowByte	
		return;
	}
	HDMI_WriteI2C_Byte(0xff,0x88);     //                
  HDMI_WriteI2C_Byte(0x1e,0x30);     //
}

void DP_OUT_VIDEO_OPEN(void)
{
	
	delay1ms(300); 
	HDMI_Format( );
	delay1ms(300); 
	HDMI_Format( );
	delay1ms(300); 
	HDMI_Format( );
	delay1ms(300); 
	HDMI_Format( );
	delay1ms(300); 
	HDMI_Format( );
	delay1ms(300); 
	HDMI_Format( );
	delay1ms(300); 
	HDMI_Format( );
				
	HDMI_WriteI2C_Byte(0xff,0x88);     //                
  HDMI_WriteI2C_Byte(0x1e,0x30);     //}
}

void LT8718_Config(void)
{

	ls2k_i2c_init(0, LS2K1000_I2C0_REG_BASE);
    Timer0_Delay1ms(100);
    ChipID();
    Debug_printf("check HPD");
	BackLight();
	RxPHY();
	RxPLL();
	Audio_IIS( );
	TypecMode();
	TxPHY();
	TxPLL(g_Lt9721_info.link_rate);
	VideoProcessor(g_Lt9721_info);
	MainLink();
	HDMI_WriteI2C_Byte(0xff,0x70);
	HDMI_WriteI2C_Byte(0x21,0x0d);
	HDMI_WriteI2C_Byte(0x21,0x0f);//Enable DP TX
	TTL_RGB_MODE_INPUT_SETTING();
 	if(DP_DET())
	{
		{
			Debug_printf(" DP hdp HIGHT! ");
			AuxTraining(g_Lt9721_info);
	        EdidRead();
		}
	}
	else
	{
	 	Debug_printf(" DP hdp LOW! ");
	}
   
    
    DP_OUT_VIDEO_OPEN( );
   
}

