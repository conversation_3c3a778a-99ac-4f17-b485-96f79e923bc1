/*
 * Copyright (c) 2021 Loongson Technology Corporation Limited (www.loongson.cn)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */

#ifndef _KERNEL
#define _KERNEL
#endif

#include <asm.h>
#include <regnum.h>
#include <cpu.h>
#include <pte.h>

#include "pmon/dev/ns16550.h"
#include "target/bonito.h"
#include "target/ls2k300.h"

#include "target/cacheops.h"

#define DEBUG_LOCORE
#ifdef DEBUG_LOCORE
#define TTYDBG(x) \
	.section .rodata;98: .asciz x; .text; la a0, 98b; bl stringserial
#define TTYDBG_COM1(x) \
	.section .rodata;98: .asciz x; .text; la a0, 98b; bl stringserial_COM1
#else
#define TTYDBG(x)
#define TTYDBG_COM1(x)
#endif
#define	PRINTSTR TTYDBG
#define PRINT_CSR(offset)	\
	PRINTSTR("\r\ncsr 0x");	\
	li.w	a0, offset;	\
	bl	hexserial;	\
	PRINTSTR(" ->0x");	\
	csrrd	a0, offset;	\
	bl	hexserial64;	\
	PRINTSTR("\r\n");

##define USEPCI

#define msize		s2

/*
 * Register usage:
 *
 * s0 link versus load offset, used to relocate absolute adresses.
 * s1 free
 * s2 memory size.
 * s3 st.dShape.
 * s4 Bonito base address.
 * s5 dbg.
 * s6 st.dCfg.
 * s7 rasave.
 * s8 L3 Cache size.
 */


	.globl	_start
	.globl	start
	.globl	__main
_start:
start:
	.globl	stack
stack = start + LOCK_CACHE_SIZE	 /* Place PMON stack in the end of 2M RAM */

	/* default mem config space: [5] default, [4] disable cfg space */
	li.w	t0, 0x16000100
	ld.w	t1, t0, 0x0
	li.w	t2, (1 << 5 | 0xf << 20)
	or	t1, t1, t2
	st.w	t1, t0, 0x0

	li.d	sp, SRAM_TOP - 12
	bl dev_fixup
	li.w	t0, 0x28
	csrwr	t0, 0x0

	/*close usb & ddr soft reset: [0] ddr, [7] usbm*/
	li.w  	t1, 0xffffff7e 
	st.w 	t1, t0, 0x1c

	/*unvalid ddr soft reset: [0] ddr*/
	ld.w	t1, t0, 0x1c
	ori     t1, t1, 0x1
	st.w 	t1, t0, 0x1c
	/* remove this code to C code */
	/*[103:96] en [111:104] coherent*/
	/*0:cpu    1:usb     2:gmac0    3:gmac1   4:dc   
		5:apb-dma(sdio spi can .etc)*/
	li.w	t0, 0x1600010c
	ld.w	t1, t0, 0x0
	li.w	t2, ((0x1f << 1) | (0x1f << 9))  //cache coherent for usb/gmac0/gmac1/dc
	or	t1, t1, t2
	st.w	t1, t0, 0x0

	li.d	t0, (1 << (24 + 32))	//set CPUCFG 0 bit24
	csrxchg	t0, t0, 0xc0

	li.d	t0, (0xa << (16 + 32))	//set CPUCFG 0x13 bit16-23
	li.d	t1, (0xff << (16 + 32))
	csrxchg	t0, t1, 0xc9

	/* enable perf counter as cp0 counter */
	li.w	t0, (0x1 << 16)
	csrxchg t0, t0, 0x200

	li.d	t0, UNCACHED_MEMORY_ADDR | 0xf
	csrwr	t0, 0x180
	li.d	t0, CACHED_MEMORY_ADDR | 0x1f
	csrwr	t0, 0x181

//	bl watchdog_close //zzz
/*
 * should before execution jr shutdown slave core
 * otherwise speculative execution cause error
 */
	/* clear Vint cofigure */
	li.d	t0, (0x7 << 16)
	csrxchg zero, t0, 0x4
	/* set ebase address */
	li.d	t0, PHYS_TO_CACHED(0x1c001000)
	csrwr	t0, 0xc
	/* set TLB excption address */
	li.d	t0, 0x000000001c001000
	csrwr	t0, 0x88

	/* disable interrupt */
	li.d	t0, (1 << 2)
	csrxchg zero, t0, 0x0
	

	/* don't change this code,jumping to cached address */
	li.d	t1, CACHED_MEMORY_ADDR
	pcaddi	t0, 3
	or	t0, t1, t0
	jirl	zero, t0, 0
	/* now pc run to 0x90xxxxxxxxxxxxxx */
	/* change to PG-mode from DA-mode */
	li.w	t0, 0xb0
	csrwr	t0, 0x0

	/* calculate ASM stage print function s0 address */
	la	s0, start
	li.d	a0, PHYS_TO_CACHED(0x1c000000)
	/* if change locked cache address may need change the following code */
	sub.d	s0, s0, a0

	la	sp, stack
	la	gp, _gp
#if 1
	/* spi speedup */
	li.d	t0, PHYS_TO_UNCACHED(0x16010000)
	li.w	t1, 0x47
	st.b	t1, t0, 0x4
#endif


	/* config uart, use gpio[40 41] */
#ifdef	USE_UART0_DEBUG
	li.d	t0, PHYS_TO_UNCACHED(0x16000498)
	ld.w	t1, t0, 0x0
	li.w	t2, 0xf0000
	or	t1, t1, t2
	st.w	t1, t0, 0x0
#endif
#ifdef	USE_UART6_DEBUG
	/* config uart4~6, use gpio[60 65] */
	li.d	t0, PHYS_TO_UNCACHED(0x1600049c)
	ld.w	t1, t0, 0x0
	li.w	t2, 0xffffff
	and	t1, t1, t2
	li.w	t2, 0xaa000000
	or	t1, t1, t2
	st.w	t1, t0, 0x0
#endif
#ifdef	USE_UART5_DEBUG
	li.d	t0, PHYS_TO_UNCACHED(0x160004a0)
	ld.w	t1, t0, 0x0
	li.w	t2, 0xfffffff0
	and	t1, t1, t2
	li.w	t2, 0xa
	or	t1, t1, t2
	st.w	t1, t0, 0x0
#endif


	li.d	a0, COM1_BASE_ADDR
	li.d	a1, (APB_FREQ_DEF * 1000000)
	bl	initserial

bsp_start:
	PRINTSTR("\r\nPMON2000 LOONGARCH Initializing. Standby...\r\n")
	dbar 0
	ibar 0

	bl	locate			/* Get current execute address */

	/* all exception entry */
	.org 0x1000
1:
	b	1b
	/* s0 in different stage should fixup */
	la	a0, start
	li.d	a1, PHYS_TO_UNCACHED(0x1c000000)
	sub.d	a0, a0, a1
	li.d	a1, 0x00000000ffff0000
	and	a0, a0, a1
	beq	a0, s0, 1f
	move	s0, zero
1:
	and	s0, s0, a0
	csrrd	t0, 0x8a
	andi	t0, t0, 0x1
	bnez	t0, 2f

	PRINTSTR("\r\nCPU Trigger exception!\r\n")
	PRINT_CSR(0x4);
	PRINT_CSR(0x5);
	PRINT_CSR(0x6);
	PRINT_CSR(0x7);
	PRINT_CSR(0x8);
1:
	b	1b
2:
	li.d	t1, CACHED_MEMORY_ADDR
	bl	1f
1:
	addi.d	t0, ra, 12
	or	t0, t1, t0
	jirl	zero, t0, 0

	li.d	t0, 0xb0
	csrwr	t0, 0
	PRINTSTR("\r\nTLB exception!\r\n");
	PRINT_CSR(0x89);
	PRINT_CSR(0x8a);
1:
	b	1b

locate:

#include "clksetting_2k300.S"
start_now:
	PRINTSTR("\r\nPMON2000 LOONGARCH Initializing. Standby...\r\n")
#ifdef	USE_120M
	/*pll setting*/
	li.d 	t0,0x0001000107270e00
	#csrwr	t0,LOONGARCH_CSR_MCR2
	csrwr	t0,0xc2
#endif

#if 0
	bnez	s0, 1f

	li.w	a0, 128
	la	ra, init_loongarch
	jirl	zero, ra, 0
1:
#endif

	PRINTSTR("\r\nlock scache ")
	li.d	a0, LOCK_CACHE_BASE
	bl	hexserial
	PRINTSTR(" - ")
	li.d	a0, LOCK_CACHE_BASE + LOCK_CACHE_SIZE
	bl	hexserial

	li.d	t0, PHYS_TO_UNCACHED(0x16000200)
	li.d	t1, ~(LOCK_CACHE_SIZE - 1)
	st.d	t1, t0, 0x40
	li.d	t1, (LOCK_CACHE_BASE & 0xffffffffffff) | (1 << 63)
	st.d	t1, t0, 0x0

	dbar 0x0

	PRINTSTR("\r\nLock Scache Done.\r\n")

	/* copy flash code to scache */
	li.d	a1, PHYS_TO_CACHED(0x1c000000)
	la	a0, start
	la	a2, edata
1:
	ld.d	a3, a1, 0
	st.d	a3, a0, 0
	addi.d	a0, a0, 8
	addi.d	a1, a1, 8
	bne	a2, a0, 1b

	PRINTSTR("copy text section to lock cache done.\r\n")
	/*clear cache mem BSS in this space*/
	la	a0, _edata
	la	a1, _end
1:
	st.d	zero, a0, 0
	addi.d	a0, a0, 8
	blt	a0, a1, 1b

	ibar 0

#if 0
	li.d	a0, LOCK_CACHE_BASE
	li.d	a1, LOCK_CACHE_BASE + LOCK_CACHE_SIZE
1:
	/* ensure locked cache address accessible so this code can`t remove*/
	ld.d	zero, a0, 0
	addi.d	a0, a0, 0x40
	bne	a1, a0, 1b
#endif

	/* jump to locked cache address */
	la.abs  t0, LC
	jirl    zero, t0, 0
LC:
	move	s0, zero
	PRINTSTR("run in cache.\r\n")

	la      ra, cache_main
	jirl	zero, ra, 0


#include "cpulib.S"
#include "serial.S"

LEAF(beep_off)
	/* enable gpio46 */
	li.d	t1, PHYS_TO_UNCACHED(0x1fe104a4)
	ld.w	t0, t1, 0x0
	li.w	t2, 0xf8ffffff
	and	t0, t0, t2
	st.w	t0, t1, 0x0

	/* enable gpio46 output */
	li.d	t1, PHYS_TO_UNCACHED(0x1fe10434)
	ld.w	t0, t1, 0x0
	li.w	t2, ~(0x1 << 14)
	and	t0, t0, t2
	st.w	t0, t1, 0x0

	/* set gpio46 high */
	li.d	t1, PHYS_TO_UNCACHED(0x1fe10444)
	ld.w	t0, t1, 0x0
	li.w	t2, (0x1 << 14)
	or	t0, t0, t2
	st.w	t0, t1, 0x0

	jirl	zero, ra, 0
	nop
END(beep_off)

/*
 * a0 [0:15] phy configure address, [16:31] phy configure data
 */
LEAF(ls2k_pcie_phy_write)
	li.d	a1, 0x100000000
	or	a0, a1, a0
	li.d	a1, PHYS_TO_UNCACHED(0x1fe10560)
	st.d	a0, a1, 0x0
1:
	ld.w	a0, a1, 0x4
	andi	a0, a0, (1 << 2)
	beqz	a0, 1b
	jirl	zero, ra, 0
END(ls2k_pcie_phy_write)

/*
 * a0 is device number
 */
LEAF(ls2k_pcie_port_conf)
	slli.w	a0, a0, 11
	li.d	a1, PHYS_TO_UNCACHED(0xfe0800000c)
	or	a1, a1, a0

	li.w	a2, 0xfff9ffff
	ld.w	a3, a1, 0x0
	and	a3, a3, a2
	li.w	a2, 0x20000
	or	a3, a3, a2
	st.w	a3, a1, 0x0

	li.d	a1, PHYS_TO_UNCACHED(0xfe0700001c)
	or	a1, a1, a0
	ld.w	a3, a1, 0x0
	li.w	a2, (0x1 << 26)
	or	a3, a3, a2
	st.w	a3, a1, 0x0

	li.d	a1, PHYS_TO_UNCACHED(0x16000000)
	slli.w	a0, a0, 1
	or	a1, a1, a0

	li.w	a2, ~((0x7 << 18) | (0x7 << 2))
	ld.w	a3, a1, 0x54
	and	a3, a3, a2
	st.w	a3, a1, 0x54

	ld.w	a3, a1, 0x58
	and	a3, a3, a2
	st.w	a3, a1, 0x58

	li.d	a2, 0xff204f
	st.w	a2, a1, 0x0

	jirl	zero, ra, 0
END(ls2k_pcie_port_conf)

LEAF(watchdog_open)
	//disable watch DOG.
#if 1	//use internal watch dog
	li.d	t1, PHYS_TO_UNCACHED(0x1ff6c000)
	li.w	t2, 0x2fffffff
	st.w	t2, t1, 0x38

	ld.w	t2, t1, 0x30
	li.w	t3, 0x2
	or	t2, t2, t3
	st.w	t2, t1, 0x30	//enable watchdog

	li.w	t2, 0x1
	st.w	t2, t1, 0x34	//set watchdog time
#else	//GPIO0:WDI, GPIO1=0:close, GPIO1=1:1~3s
	/* multi cfg */
	li.d	t1, LS2K500_GPIO_MULTI_CFG
	ld.w	t2, t1, 0x0
	li.w	t3, ~0xff
	and	t2, t2, t3
	st.w	t2, t1, 0x0

	/*gpio1 output high*/
	li.d	t1, LS2K500_GPIO_00_63_DIR
	ld.w	t2, t1, 0x0
	li.w	t3, ~0x2
	and	t2, t2, t3
	st.w	t2, t1, 0x0

	ld.w	t2, t1, 0x10
	ori	t2, t2, 2
	st.w	t2, t1, 0x10
	nop
#endif
	jirl	zero, ra, 0
END(watchdog_open)

LEAF(watchdog_close)
	//disable watch DOG.
#if 1	//use internal watch dog
	li.d	t1, PHYS_TO_UNCACHED(0x1ff6c030)
	ld.w	t2, t1, 0x0
	li.w	t3, ~0x2
	and	t2, t2, t3
	st.w	t2, t1, 0x0
#else	//GPIO0:WDI, GPIO1=0:close, GPIO1=1:1~3s
	/* multi cfg */
	li.d	t1, LS2K500_GPIO_MULTI_CFG
	ld.w	t2, t1, 0x0
	li.w	t3, ~0xff
	and	t2, t2, t3
	st.w	t2, t1, 0x0

	/*gpio1 output zero*/
	li.d	t1, LS2K500_GPIO_00_63_DIR
	ld.w	t2, t1, 0x0
	li.w	t3, ~0x2
	and	t2, t2, t3
	st.w	t2, t1, 0x0

	ld.w	t2, t1, 0x10
	and	t2, t2, t3
	st.w	t2, t1, 0x10
	nop
#endif
	jirl	zero, ra, 0
END(watchdog_close)

// todo ?????
#if 1
    .text
    .global  nvram_offs
    .align 12
nvram_offs:
    .dword 0x0
    .align 12
#endif
#######################################


//    .section .rdata
//    .global ddr2_reg_data
//    .global ddr3_reg_data
//    .align  5
//#include "ddr_dir/loongson_mc2_param.S"
