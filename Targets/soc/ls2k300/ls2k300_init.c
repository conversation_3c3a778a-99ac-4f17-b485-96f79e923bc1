#include <include/stdarg.h>
#include <include/stdio.h>
#include <include/file.h>
#include <linux/io.h>
#include <sys/ioccom.h>
#include <sys/types.h>
#include <frame.h>
#include <termio.h>
#include <string.h>
#include <stdlib.h>
#include <dev/pci/pcivar.h>

#include <autoconf.h>
#include <pmon.h>
#include <machine/cpu.h>
#include <machine/pio.h>

#include <pmon/dev/ns16550.h>

#include "pai_99.h"
#include "pai_99_plus.h"
#include "pai_99_plus_single_net.h"
#include "pai_2k300_dual_net.h"
#if 0
void
delay2(unsigned int loops)
{
       volatile unsigned int counts = loops;
       while (counts--);
}
#endif

void ls2k300_beep(void)
{
}

void ls2k300_init_before(void)
{
	unsigned int val;
#ifdef LCD_EN
	/*lcd mux*/
	readl(PHYS_TO_UNCACHED(0x16000490)) |= 0xffffffff;
	readl(PHYS_TO_UNCACHED(0x16000494)) |= 0xffffff;
#if (NPAI_99 >0)
	/*pwm0 mux*/
	readl(PHYS_TO_UNCACHED(0x160004a4)) &= ~(0x3 << 12);
	readl(PHYS_TO_UNCACHED(0x160004a4)) |= (0x2 << 12);
	
	/*GPIO52~GPIO54*/
	readl(PHYS_TO_UNCACHED(0x1600049c)) &= ~(0x3f << 8);

	/*gpio54 output 0x1*/
	writeb(0x0, PHYS_TO_UNCACHED(0x16104836));
	writeb(0x1, PHYS_TO_UNCACHED(0x16104936));

	readl(PHYS_TO_UNCACHED(0x1611b00c)) &= ~0x1;
	writel(150, PHYS_TO_UNCACHED(0x1611b004));
	writel(1500, PHYS_TO_UNCACHED(0x1611b008));
	readl(PHYS_TO_UNCACHED(0x1611b00c)) |= 0x1;

	//gmac0 phy reset
	/*GPIO87*/
	readl(PHYS_TO_UNCACHED(0x160004a4)) &= ~(0x3f << 14);
	writeb(0x0, PHYS_TO_UNCACHED(0x16104857));
	writeb(0x1, PHYS_TO_UNCACHED(0x16104957));
#elif (NPAI_99_PLUS > 0 || NPAI_99_PLUS_SINGLE_NET > 0)
	/*pwm0 mux*/
	readl(PHYS_TO_UNCACHED(0x160004a4)) &= ~(0x3 << 12);
	readl(PHYS_TO_UNCACHED(0x160004a4)) |= (0x2 << 12);

    /*GPIO88~GPIO89*/
    readl(PHYS_TO_UNCACHED(0x160004a4)) &= ~(0xf << 16);

	readl(PHYS_TO_UNCACHED(0x1611b00c)) &= ~0x1;
	writel(150, PHYS_TO_UNCACHED(0x1611b004));
	writel(1500, PHYS_TO_UNCACHED(0x1611b008));
	readl(PHYS_TO_UNCACHED(0x1611b00c)) |= 0x1;

	//gmac0 phy reset
	/*GPIO87*/
	readl(PHYS_TO_UNCACHED(0x160004a4)) &= ~(0x3f << 14);
	writeb(0x0, PHYS_TO_UNCACHED(0x16104857));
	writeb(0x1, PHYS_TO_UNCACHED(0x16104957));
#else
#ifdef BEEP_ON
	/*pwm0 mux*/
	readl(PHYS_TO_UNCACHED(0x160004a4)) &= ~(0x3 << 12);
	readl(PHYS_TO_UNCACHED(0x160004a4)) |= (0x2 << 12);

	/*pwm0 for beep*/
	readl(PHYS_TO_UNCACHED(0x1611b00c)) &= ~0x1;
	writel(0x5000, PHYS_TO_UNCACHED(0x1611b004));
	writel(0x10000, PHYS_TO_UNCACHED(0x1611b008));
	readl(PHYS_TO_UNCACHED(0x1611b00c)) |= 0x1;
	mdelay(500);
	readl(PHYS_TO_UNCACHED(0x1611b00c)) &= ~0x1;
#endif
	/*pwm1 mux*/
	readl(PHYS_TO_UNCACHED(0x160004a4)) &= ~(0x3 << 14);
	readl(PHYS_TO_UNCACHED(0x160004a4)) |= (0x2 << 14);
	
	/*gpio103~gpio105  mux*/
	readl(PHYS_TO_UNCACHED(0x160004a8)) &= ~(0x3f << 14);

	/*gpio105 output 0x1*/
	writeb(0x0, PHYS_TO_UNCACHED(0x16104869));
	writeb(0x1, PHYS_TO_UNCACHED(0x16104969));
	/*pwm1 output*/
	readl(PHYS_TO_UNCACHED(0x1611b01c)) &= ~0x1;
	writel(150, PHYS_TO_UNCACHED(0x1611b014));
	writel(1500, PHYS_TO_UNCACHED(0x1611b018));
	readl(PHYS_TO_UNCACHED(0x1611b01c)) |= 0x1;
#endif
#endif
/* Add SPI PAD default value */
	readl(PHYS_TO_UNCACHED(0x16000110)) |= (0x3 << 18);
}

void ls2k300_init_after(void)
{	
}

