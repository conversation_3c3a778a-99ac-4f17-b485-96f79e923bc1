/*
 * This file is for 2K300 pin and gpio ctrl
 */
#include "target/pincfgs.h"
#include <pmon.h>
#include <string.h>
pin_cfgs_t default_pin_cfgs[106] = {
	//0:GPIO, 1:first reuse, 2:second reuse, 3:main
	{0,   3}, //3:lcd_clk		1:-		            2:-	
	{1,   3}, //3:lcd_vsync		1:-					2:lioa[0]         
	{2,   3}, //3:lcd_hsync		1:-					2:lioa[1]            
	{3,   3}, //3:lcd_en		1:-					2:lioa[2]     
	{4,   2}, //3:lcd_dat_b[0]	1:-					2:lioa[3]     
	{5,   2}, //3:lcd_dat_b[1]	1:-					2:lioa[4]     
	{6,   2}, //3:lcd_dat_b[2]	1:-					2:lioa[5] 
	{7,   2}, //3:lcd_dat_b[3]	1:-					2:lioa[6] 
	{8,   3}, //3:lcd_dat_b[4]	1:-					2:lioa[7] 
	{9,   3}, //3:lcd_dat_b[5]	1:-					2:lioa[8] 
	{10,  3}, //3:lcd_dat_b[6]	1:-					2:lioa[9] 
	{11,  3}, //3:lcd_dat_b[7]	1:-					2:lioa[10]
#ifdef USE_CONSOLE_TCM2
	{12,3}, //3:main_spi1_clk	1:main_uart0_dcd	2:pm1io[4]	0:GPIO 	
	{13,3}, //3:main_spi1_miso	1:main_uart0_ri		2:pm1io[5]	0:GPIO:SGM706 input
	{14,3}, //3:main_spi1_mosi	1:main_uart1_rts	2:pm1io[6]	0:GPIO:GPIO_REBOOT
	{15,3}, //3:main_spi1_cs	1:main_uart1_cts	2:pm1io[7]	0:GPIO:SGM706 output
#else
	{12,  0}, //3:lcd_dat_g[0]	1:-					2:lioa[11]
	{13,  0}, //3:lcd_dat_g[1]	1:-					2:lioa[12]
	{14,  0}, //3:lcd_dat_g[2]	1:-					2:lioa[13]
	{15,  0}, //3:lcd_dat_g[3]	1:-					2:lioa[14]
#endif
	{16,  3}, //3:lcd_dat_g[4]	1:-					2:lioa[15]
	{17,  3}, //3:lcd_dat_g[5]	1:-					2:lioa[16]
	{18,  3}, //3:lcd_dat_g[6]	1:-					2:lioa[17] 
	{19,  3}, //3:lcd_dat_g[7]	1:-					2:lioa[18] 
	{20,  3}, //3:lcd_dat_r[0]	1:-					2:lioa[19] 
	{21,  3}, //3:lcd_dat_r[1]	1:-					2:lioa[20] 
	{22,  3}, //3:lcd_dat_r[2]	1:-					2:lioa[21] 
	{23,  3}, //3:lcd_dat_r[3]	1:-					2:lioa[22] 
	{24,  3}, //3:lcd_dat_r[4]	1:-					2:liocsn[0]
	{25,  3}, //3:lcd_dat_r[5]	1:-					2:liocsn[1]
	{26,  3}, //3:lcd_dat_r[6]	1:-					2:liowrn   
	{27,  3}, //3:lcd_dat_r[7]	1:-					2:liordn   
	{28,  3}, //3:gmac0_rx_ctl	1:-					2:tim1_ch1	
	{29,  3}, //3:gmac0_rx[0]  	1:-					2:tim1_ch2
	{30,  3}, //3:gmac0_rx[1]  	1:-					2:tim1_ch3
	{31,  3}, //3:gmac0_rx[2]  	1:-					2:tim1_ch1n
	{32,  3}, //3:gmac0_rx[3]  	1:-					2:tim1_ch2n
	{33,  3}, //3:gmac0_tx_ctl 	1:-					2:tim1_ch3n
	{34,  3}, //3:gmac0_tx[0]  	1:-					2:tim2_ch1
	{35,  3}, //3:gmac0_tx[1]  	1:-					2:tim2_ch2
	{36,  3}, //3:gmac0_tx[2]  	1:can_rx[0]			2:tim2_ch3
	{37,  3}, //3:gmac0_tx[3]  	1:can_tx[0]			2:-
	{38,  3}, //3:gmac0_mdck   	1:can_rx[1]			2:-
	{39,  3}, //3:gmac0_mdio   	1:can_tx[1]			2:-
	{40,  3}, //3:uart0_rx		1:gmac0_ptp_trig	2:lio_data[0]
	{41,  3}, //3:uart0_tx		1:gmac0_ptp_pps		2:lio_data[1]
	{42,  3}, //3:uart1_rx		1:gmac1_ptp_trig	2:lio_data[2]
	{43,  3}, //3:uart1_tx		1:gmac1_ptp_pps		2:lio_data[3]
	{44,  1}, //3:uart2_tx		1:gmac1_rx_ctl		2:lio_data[4]
	{45,  1}, //3:uart2_rx		1:gmac1_rx[0]		2:lio_data[5]
	{46,  1}, //3:uart3_tx		1:gmac1_rx[1]		2:lio_data[6]
	{47,  1}, //3:uart3_rx		1:gmac1_rx[2]		2:lio_data[7]
#ifdef USE_I2C_MASTER
	{48,  3}, //3:i2c_scl[0]	1:gmac1_rx[3]		2:lio_data[8]
	{49,  3}, //3:i2c_sda[0]	1:gmac1_tx_ctl		2:lio_data[9]
	{50,  3}, //3:i2c_scl[1]	1:gmac1_tx[0]		2:lio_data[10]
	{51,  3}, //3:i2c_sda[1]	1:gmac1_tx[1]		2:lio_data[11]
	{52,  3}, //3:i2c_scl[2]	1:gmac1_tx[2]		2:lio_data[12]
	{53,  3}, //3:i2c_sda[2]	1:gmac1_tx[3]		2:lio_data[13]
	{54,  3}, //3:i2c_scl[3]	1:gmac1_mdck		2:lio_data[14]
	{55,  3}, //3:i2c_sda[3]	1:gmac1_mdio		2:lio_data[15]
#else	
	{48,  1}, 	
	{49,  1},
	{50,  1}, 
	{51,  1}, 
	{52,  1}, 
	{53,  1}, 
	{54,  1}, 
	{55,  1},
#endif		
	{56,  3}, //3:spi0_clk   	1:-					2:can_rx[2]
	{57,  3}, //3:spi0_miso  	1:-					2:can_tx[2]
	{58,  3}, //3:spi0_mosi  	1:-					2:can_rx[3]
	{59,  3}, //3:spi0_cs[0] 	1:-					2:can_tx[3]
#ifndef USE_UART0_DEBUG 
	{60,  2}, //3:spi1_clk		1:i2c_scl[0]		2:uart0_rts
	{61,  2}, //3:spi1_miso		1:i2c_sda[0]		2:uart0_cts
	{62,  2}, //3:spi1_mosi		1:i2c_scl[1]		2:uart0_dsr
	{63,  2}, //3:spi1_cs[0]    1:i2c_sda[1]		2:uart0_dtr
	{64,  2}, //3:spi_sck[0]    1:pwm[0]			2:uart0_dcd
	{65,  2}, //3:spi_miso[0]   1:pwm[1]			2:uart0_ri
#else
	{60,  3},  	
	{61,  3}, 
	{62,  3},   	
	{63,  3}, 
	{64,  3}, 
	{65,  3}, 
#endif
	{66,  3}, //3:spi_mosi[0]	1:pwm[2]			2:uart1_rts
	{67,  3}, //3:spi_ss[0]		1:pwm[3]			2:uart1_cts
	{68,  3}, //3:can0_rx		1:spi0_cs[1]		2:uart1_dsr
	{69,  3}, //3:can0_tx		1:spi0_cs[2]		2:uart1_dtr
	{70,  3}, //3:can1_rx		1:spi0_cs[3]		2:uart1_dcd
	{71,  3}, //3:can1_tx		1:-					2:uart1_ri
	{72,  3}, //3:can2_rx		1:sdio1_d[4]		2:gmac0_col
	{73,  3}, //3:can2_tx		1:sdio1_d[5]		2:gmac0_crs
	{74,  3}, //3:can3_rx		1:sdio1_d[6]		2:gmac1_col
	{75,  3}, //3:can3_tx		1:sdio1_d[7]		2:gmac1_crs
	{76,  3}, //3:i2s_mclk		1:tim1_ch4			2:-
	{77,  3}, //3:i2s_bclk		1:tim2_ch4			2:-
	{78,  3}, //3:i2s_lr		1:atim_etr			2:spi1_cs[1]
	{79,  3}, //3:i2s_datai		1:gtim_etr			2:spi1_cs[2]
	{80,  3}, //3:i2s-datao		1:tim1_breakin		2:spi1_cs[3]
	{81,  0}, //3:tim1_ch1		1:-					2:-
	{82,  1}, //3:tim1_ch2		1:spi_sck[1]		2:i2c_scl[2]
	{83,  1}, //3:tim1_ch3		1:spi_miso[1]		2:i2c_sda[2]
	{84,  1}, //3:tim1_ch1n		1:spi_mosi[1]		2:i2c_scl[3]
	{85,  1}, //3:tim1_ch2n		1:spi_ss[1]			2:i2c_sda[3]
	{86,  1}, //3:tim1_ch3n		1:sdio0_d[4]		2:pwm[0]
	{87,  1}, //3:tim2_ch1		1:sdio0_d[5]		2:pwm[1]
	{88,  1}, //3:tim2_ch2		1:sdio0_d[6]		2:pwm[2]
	{89,  1}, //3:tim2_ch3		1:sdio0_d[7]		2:pwm[3]
	{90,  3}, //3:sdio0_clk  	1:-					2:-
	{91,  3}, //3:sdio0_cmd  	1:-					2:-
	{92,  3}, //3:sdio0_d[0] 	1:can_rx[0]			2:uart0_rx
	{93,  3}, //3:sdio0_d[1] 	1:can_tx[0]			2:uart0_tx
	{94,  3}, //3:sdio0_d[2] 	1:can_rx[1]			2:uart1_rx
	{95,  3}, //3:sdio0_d[3] 	1:can_tx[1]			2:uart1_tx
	{96,  3}, //3:sdio0_d[4]	1:can_rx[2]			2:uart2_tx
	{97,  3}, //3:sdio0_d[5]	1:can_tx[2]			2:uart2_rx	
	{98,  3}, //3:sdio0_d[6]	1:can_rx[3]			2:uart3_tx
	{99,  3}, //3:sdio0_d[7]	1:can_tx[3]			2:uart3_rx
	{100, 3}, //3:sdio1_clk		1:-					2:-
	{101, 3}, //3:sdio1_cmd		1:tim1_ch4			2:-
	{102, 3}, //3:sdio1_d[0]	1:tim2_ch4			2:pwm[0]
	{103, 3}, //3:sdio1_d[1]	1:atim_etr			2:pwm[1]
	{104, 3}, //3:sdio1_d[2]	1:gtim_etr			2:pwm[2]
	{105, 3}, //3:sdio1_d[3]	1:tim1_breakin		2:pwm[3]
};

void ls2k300_gpio_out(int gpio_num, int val)
{
	set_pin_mode(gpio_num, 0);
	uint64_t *addr_dir = LS2K300_GPIO_BIT_OEN;
	uint64_t *addr_out = LS2K300_GPIO_BIT_O;
	readq(addr_dir) &= ~(1ULL << gpio_num);
	readq(addr_out) = readq(addr_out) & ~(1ULL << gpio_num) | ((unsigned long long)!!val << gpio_num);
}

int ls2k300_gpio_in(int gpio_num)
{
	set_pin_mode(gpio_num, 0);
	uint64_t *addr_dir = LS2K300_GPIO_BIT_OEN;
	uint64_t *addr_in = LS2K300_GPIO_BIT_I;
	readq(addr_dir) |= (1ULL << gpio_num);
	return !!(readq(addr_in) & (1ULL << gpio_num));
}

int cmd_ls2k300_gpio_out(int ac, unsigned char *av[])
{
	if (ac != 3) {
		printf("gpio_out <gpio_num> <output_val>\n");
		return 0;
	}
	ls2k300_gpio_out(strtoul(av[1], NULL, 0), strtoul(av[2], NULL, 0));
	return 0;
}

int cmd_ls2k300_gpio_in(int ac, unsigned char *av[])
{
	if (ac != 2) {
		printf("gpio_in <gpio_num>\n");
		return 0;
	}
	printf("gpio read val: %d\n", ls2k300_gpio_in(strtoul(av[1], NULL, 0)));
	return 0;
}

static const Cmd Cmds[] =
{
	{"MyCmds"},
	{"gpio_out","",0,"set gpio out put <gpio num> <val>", cmd_ls2k300_gpio_out , 0, 3, CMD_REPEAT},
	{"gpio_in","",0,"set gpio in, and get val", cmd_ls2k300_gpio_in , 0, 2, CMD_REPEAT},
	{0,0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

static void
init_cmd()
{
	cmdlist_expand(Cmds,1);
}
