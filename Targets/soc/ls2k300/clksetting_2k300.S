#define SOFT_CLKSEL
#ifdef SOFT_CLKSEL

#if ((DDR_LOOPC > 255) | (NODE_LOOPC > 255) | (SOC_LOOPC > 255))
PLL LOOPC overflow
#endif

	TTYDBG ("Soft CLK SEL adjust begin\r\n")
	TTYDBG ("\r\nNODE	:")

	
	li.d	t0, PHYS_TO_UNCACHED(0x16000400)
	li.w	t1, (0x1 << 5)	//power down pll L1 first
	st.w	t1, t0, 0
	li.w	t4, (NODE_DIV << 24) | (NODE_LOOPC << 15) | (NODE_REFC << 8)
	st.w	t4, t0, 0

	ld.w	t1, t0, 4
	li.w	t2, ~((0x7f << 8) | 0x7f)
	and	t1, t1, t2
	li.w	t2, (I2S_DIV << 8) | (GMAC_DIV)
	or	t2, t2, t1
	st.w	t2, t0, 0x4
	
	
	ori	t4, t4, PLL_L1_ENA
	st.w	t4, t0, 0

11:
	ld.w	a0, t0, 0
	li.w	a1, PLL_L1_LOCKED
	and	a0, a1, a0
	beqz	a0, 11b //wait_locked_sys

	ld.w	a0, t0, 0
	ori	a0, a0, SEL_PLL0 | SEL_PLL1 | SEL_PLL2
	st.w	a0, t0, 0

	bl	hexserial

	TTYDBG ("\r\nDDR	:")

	li.d	t0, PHYS_TO_UNCACHED(0x16000408)
	li.w	t1, (0x1 << 5)	//power down pll first
	st.w	t1, t0, 0
	li.w	t4, (DDR_DIV << 24) | (DDR_LOOPC << 15) | (DDR_REFC << 8)
	st.w	t4, t0, 0

#if 0
	ld.w	t1, t0, 4
	li.w	t2, ~((0x3 << 18)| (0x7f << 8) | 0x7f | (1 << 16))
	and	t1, t1, t2
	li.w	t2,  (MEMDIV_MODE << 18) | (DEV_DIV << 8) | (NETWORK_DIV) |1<<16
	or	t2, t2, t1
	st.w	t2, t0, 0x4
#else
	ld.w	t1, t0, 4
	li.w	t2, ~((0x3 << 18)| (0x7f << 8) | 0x7f )
	and	t1, t1, t2
	li.w	t2,  (MEMDIV_MODE << 18) | (DEV_DIV << 8) | (NETWORK_DIV) 
	or	t2, t2, t1
	st.w	t2, t0, 0x4
#endif
	ori	t4, t4, PLL_L1_ENA
	st.w	t4, t0, 0

21:
	ld.w	a0, t0, 0
	li.w	a1, PLL_L1_LOCKED
	and	a0, a0, a1
	beqz	a0, 21b //wait_locked_ddr

#if 1
	ld.w	t1, t0, 4
	li.w	t2, ~(0x1 << 16)
	and	t1, t1, t2
	st.w	t1, t0, 0x4
	ld.w	t1, t0, 4
	li.w	t2, (0x1 << 16)
	or	t1, t1, t2
	st.w	t1, t0, 0x4
#endif

	/* Reset the serial port frequency division */
	li.d	a0, COM1_BASE_ADDR
	li.d	a1, (APB_FREQ * 1000000)
	bl	initserial

	ld.w	a0, t0, 0
	ori	a0, a0, SEL_PLL0 | SEL_PLL1 | SEL_PLL2
	st.w	a0, t0, 0

	bl	hexserial
#if 0
	TTYDBG ("\r\nPIX	:")

	li.d	t0, PHYS_TO_UNCACHED(0x16000410)
	li.w	t1, (0x1 << 5)	//power down pll first
	st.w	t1, t0, 0
	li.w	t1, (PIX_DIV << 24) | (PIX_LOOPC << 15) | (PIX_REFC << 8)
	st.w	t1, t0, 0

	ld.w	t1, t0, 4
	li.w	t2, ~((0x7f << 8) | 0x7f)
	and	t1, t1, t2
	li.w	t2, GMACBP_DIV
	or	t2, t2, t1
	st.w	t2, t0, 0x4

	ld.w	t1, t0, 0
	ori	t1, t1, PLL_L1_ENA
	st.w	t1, t0, 0

21:
	ld.w	a0, t0, 0
	li.w	a1, PLL_L1_LOCKED
	and	a0, a0, a1
	beqz	a0, 21b //wait_locked_ddr

	ld.w	a0, t0, 0
	ori	a0, a0, SEL_PLL0 | SEL_PLL1
	st.w	a0, t0, 0

	bl	hexserial
#endif
	TTYDBG ("\r\nPLL CONFIGURE DONE!\r\n")
#endif
