# config file for loongson pai
#
machine         soc        loongarch  ls2k1000  # CPU Architecture, Platform , CPU name

include "conf/LOONGSON_PUBILC"

option		LOONGSON_2K
option		LOONGARCH_2K1000
option		SOC_CPU
option      VRAM_SIZE=16
option      DEBUG_2KLA

#
# System str options
#
#option     LS_STR
option		CORES_PER_NODE=2

#
# Platform options
#
option		LS3_HT              # Enable the IO cache coherent for ls2k
option      loongson3A3
option      LSMC_2
#option     ARB_LEVEL
option      DDR3_DIMM
option		TOT_NODE_NUM=0

#option      QUICK_START

#<<<<<<<<<<<<<<
###these options can NOT defined at the same time
#option		DDR3_SODIMM #1Rx8 2GB
#option		DDR3_SMT    #for SMT x16 SDRAM 4 chip 2GB
#>>>>>>>>>>>>>>
option      SMBIOS_SUPPORT

option 		CORE_FREQ=1000   #CPU FREQ
option		DDR_FREQ=400     #DDR FREQ
option      BOOT_SPI_FREQ=1  #1  >>  25M
                             #2  >>  100/16M
                             #3  >>  100/32M
                             #4  >>  12.5M
                             #5  >>  100/64M

option		HIGH_MEM_WIN_BASE_ADDR=0x80000000
#option		SLT

select		mod_flash_st		# intel flash device programming
select		mod_txt

select		cmd_dtb
option		DTB

#
#
# Platform options
#
select		mod_display
option      DEBUG_CONSOLE=0
select		mod_x86emu_int10
option		MY61IO
option      VGA_BASE=0xb0000000
select		mod_vgacon
#select		sis315e
#option		SIS315E
option		VGA_NO_ROM       # display card has no rom
#select		mod_sisfb

#Display config
select		mod_framebuffer
option      FB_XSIZE=800
option      FB_YSIZE=480
select		mod_vesa
option 		CONFIG_VIDEO_16BPP
option      DC_FB0
option      DC_FB1
#option		X1280x1024		# if use dc, neep open. if use pcie Graphics, need close.
#option 	CONFIG_VIDEO_8BPP_INDEX

#
# HAVE options. What tgt level provide
#
option		INTERNAL_RTC		# chip internal RTC
#option		EXTERNAL_RTC		# external RTC
rmoption    CONFIG_VIDEO_LOGO
rmoption    PROGRESS_BAR
#option     VESAFB

#option     DDR_PARAM_018="0x3030303016100000"
option      DDR_S1=0xc1a18404
#option     DEBUG_DDR
#option     DEBUG_DDR_PARAM

#
#  Now the Machine specification
#
mainbus0    at root
localbus0	at mainbus0
#fd0		at mainbus0
pcibr0		at mainbus0
#pcibr1		at mainbus0
pci*		at pcibr?
ppb*		at pci? dev ? function ?	# PCI-PCI bridges
pci*		at ppb? bus ?

ahci*		at pci? dev ? function ?
ahci_sd*  	at ahci?
ahci_cdrom* at ahci?

ohci*   	at pci? dev ? function ?	# OHCI
usb*      	at ohci?

#czh 20220507 for dwc2 otg
#otg0		at pci? dev ? function ? 	# DWC2 OTG
#usb*		at otg0
#select		mod_usb_otg
#select		otg-device

#lehci*   	at pci? dev ? function ?	# EHCI
#usb*      	at lehci?

#lohci1    	at localbus0 base 0x800000001f050000	# EHCI as OHCI
#usb*      	at lohci1

#lohci0    	at localbus0 base 0x800000001f058000	# OHCI
#usb*      	at lohci0

#lxhci0    	at localbus0 base 0x800000001f060000	# XHCI
#xhci0		at lxhci0
#pcixhci0    	at pci? dev ? function ?
#xhci0		at pcixhci0
#usb*		at xhci0
#select 	mod_usb_xhci

##### USB
#uhci*		at pci? dev ? function ?

#### Networking Devices
#USB网卡
#usbnet*    at ohci?
#
#syn0		at localbus0 base 0x800000001f020000
#syn1		at localbus0 base 0x800000001f030000
pcisyn0		at pci? dev ? function ?
pcisyn1		at pci? dev ? function ?
select		gmac
option		USE_GMAC_NUM=2
option		GMAC_USE_FLASH_MAC

#ngbe*      at pci? dev ? function ?    # Intel 82576
#txgbe*     at pci? dev ? function ?    # Intel 82576
igb*		at pci? dev ? function ?	# Intel 82576
select      igb1

#### SCSI support
#siop*		at pci? dev ? function ?	# Symbios/NCR 53c...
#scsibus*	at siop?
#sd*		at scsibus? target ? lun ?
#cd*		at scsibus? target ? lun ?

#### Networking Devices
#gt0		at localbus? base 4
#gt1		at localbus? base 5
#gt2		at localbus? base 6

# fxp normally only used for debugging (enable/disable both)
#fxp*		at pci? dev ? function ?        # Intel 82559 Device
#inphy*		at mii? phy ?                   # Intel 82555 PHYs
#rtl*		at pci? dev ? function ?
rtk*		at pci? dev ? function ?
em*		    at pci? dev ? function ?
#uhci*		at pci? dev ? function ?
#ohci*		at pci? dev ? function ?
#usb*		at usbbus ?
#ohci1		at pci? dev ? function ?


select		pcinvme
pcinvme*	at pci? dev ? function ?
nvme*		at pcinvme?

#### IDE controllers
option 		IDE_DMA
pciide* 	at pci ? dev ? function ? flags 0x0000

#### IDE hard drives
wd*		    at pciide? channel ? drive ? flags 0x0000

loopdev0 at mainbus0
sdcard0		at localbus0 flags 0

select	    sdio

#### Pseudo devices
pseudo-device	loop	1	# network loopback

ide_cd* 	at pciide? channel ? drive ? flags 0x0001
select		cmd_testfire
#option		USE_ENVMAC
option		FLOATINGPT
#option		WDC_NORESET
option		KBD_CHECK_FAST
option		TEST_USB_HOST
option		CONFIG_SLOW_PCI_FOR_BROKENDEV

select		cmd_lwdhcp
select      raw_ether
#LOONGSON PAI2 默认复用关系
#DVO0,DVO1,SATA,PWM0,PWM1,PWM2,PWM3,i2c0,i2c1,can0,can1,HDA,GMAC1
# | GMAC1 | HDA | AC97 | I2S | LIO | SATA | NAND | I2C | PWM | CAN | E1  | SDIO |
# | 3     | 4   |  5   |  6  | 7   |  8   |  9   |10:11|12:15|16:17|18:19|  20  |
option     CONFIG_REG0=0x13fd19
option     CONFIG_REG1=0xf
option     CONFIG_REG2=0xde
#option    SEL_CAN

##HDA 复用
#option     SEL_HDA         #GPIO24-30,I2S

#mtd device config
#select		nand
#select     m25p80
#select     spinand_lld
#option     CONFIG_SPINAND_CS=0
#option     CONFIG_LS2K_NAND
#option     NAND_USE_CS=2
#select     nand_bch

#file System config
#select      yaffsfs

#Memory config
#option		AUTO_DDR_CONFIG		# for DIMM recognize
option      DDR_PARAM_018="0x3535353516100000"
option      NO_AUTO_TRFC
option      DDR_RESET_REVERT

option      HPET_RTC

select      i2c
select      sii9022a
#蜂鸣器
option      BEEP_GPIO=39
##无源蜂鸣器
option      HS0636
#龙芯派默认配置
#option     PAI2
option      LCD_EN
#option     GPIO36_RESET_PHY
option      GPIO_RESET=36
option      UART0_ENABLE
option      CONFIG_UART0_SPLIT
##PCIE配置
option	    PCIE_LANE_FLIP=0            #LANE翻0转，0x3代表PCIE0 port0，0xc代表PCIE0 port1 ...
#
#option     FORCE_PCIE_GEN1=0xf           #强制PCIE 1.0 ，0x1代表PCIE port0，0x2代表PCIE0 port1 ...
#option     PCIE_PHY_SKIP_DETECT=0x1

#调试
#option     LS2K_RTC_WAKEUP_REBOOT=0x8
#option     USE_ENVMAC
#option     AUTOCONF_VERBOSE=1
