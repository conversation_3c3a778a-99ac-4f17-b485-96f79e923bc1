# $Id: files.Bonito
#
# Bonito Target specific files
#

file    Targets/soc/ls2k300/serial.S
file    Targets/soc/ls2k300/cpulib.S
file	Targets/soc/pci/pci_machdep.c
file	Targets/soc/cache_stage/cache_stage.c
file	Targets/soc/ls2k300/tgt_machdep.c
file	Targets/soc/ls2k300/pincfgs_pai_nn.c   		pai_99 needs-flag
file	Targets/soc/ls2k300/pincfgs_pai_nn_plus.c   pai_99_plus needs-flag
file	Targets/soc/ls2k300/pincfgs_pai_nn_plus_single_net.c   pai_99_plus_single_net needs-flag
file	Targets/soc/ls2k300/pincfgs_pai_2k300.c   	pai_2k300_dual_net needs-flag
file	Targets/soc/ls2k300/ls2k300_init.c
file	Targets/soc/dev/dc.c
file	Targets/soc/pci/ls2k_pci.c
file	Targets/soc/dev/fl_env.c
file    Targets/soc/dev/pwm.c

define	localbus { [base = -1 ] }
device	localbus
attach	localbus at mainbus
file	Targets/soc/dev/localbus.c		localbus
file	Targets/soc/dev/spi_w.c
file	Targets/soc/dev/spi_only_io.c
#file	Targets/soc/dev/nand_opt.c
#file	Targets/soc/dev/set_cpu_ddr_freq.c
#file	Targets/soc/dev/set_vol.c
#file	Targets/soc/dev/rtc.c
#file	Targets/soc/dev/eeprom.c
file	Targets/soc/dev/load_dtb.c		cmd_dtb
#file	Targets/soc/dev/signal_test.c
#file	Targets/soc/dev/slt.c
#file	Targets/soc/dev/hda_test.c
#file	Targets/soc/dev/sgl_instruction_ts.c
#file	Targets/soc/dev/st7701s.c st7701s needs-flag
file    Targets/soc/dev/adc_test.o
file    Targets/soc/dev/ls2k0300_adc.o
file    Targets/soc/dev/ls2k300_dma.o
file   Targets/soc/dev/gpio_ls2k300.c

#TCM2 kx
file    Targets/ls2k/dev/spi.c                             cmd_tcm2
file    sys/dev/tcm/tcm2_tis_spi.c                         cmd_tcm2
file    sys/dev/tcm/tcm2_device.c                          cmd_tcm2
file    sys/dev/tcm/tcm2_sm3.c                             cmd_tcm2
file    sys/dev/tcm/tcm2_mailbox.c                         cmd_tcm2
file    sys/dev/tcm/tcm2_command.c                         cmd_tcm2
file    sys/dev/tcm/tcm2_key.c                             cmd_tcm2
file    sys/dev/tcm/tcm2_log.c                             cmd_tcm2
file    sys/dev/tcm/tcm2_sm4.c                             cmd_tcm2
file    sys/dev/tcm/tcm2_sm2.c                             cmd_tcm2
file    sys/dev/tcm/tcm2_sigver.c                          cmd_tcm2
file    sys/dev/tcm/tcm2_test.c                            cmd_tcm2
file    sys/dev/tcm/tcm2_context.c                         cmd_tcm2
file    sys/dev/tcm/tcm2_nv.c                              cmd_tcm2
file    sys/dev/tcm/tcm2_pcr.c                             cmd_tcm2
file    sys/dev/tcm/tcm2_metric.c                          cmd_tcm2

#XHCI
device	lxhci  : xhcibus
attach	lxhci at localbus

# OHCI
#device	lohci {[channel = -1]} :usbbus
#attach	lohci at localbus

#XHCI
#device	lxhci  : xhcibus
#attach	lxhci at localbus

# OTG Device
file sys/dev/usb/otg-dev/dwc2_udc.c		otg-device
file sys/dev/usb/otg-dev/dwc2_udc_otg.c		otg-device
file sys/dev/usb/otg-dev/printer.c		otg-device
file sys/dev/usb/otg-dev/gadget_config.c	otg-device
file sys/dev/usb/otg-dev/usbstring.c		otg-device
file sys/dev/usb/otg-dev/epautoconf.c		otg-device
file sys/dev/usb/otg-dev/otg_cmd.c		otg-device

# GMAC
#file	sys/dev/gmac/synopGMAC_Host.c
file	sys/dev/gmac/synopGMAC_Dev.c
file	sys/dev/gmac/synopGMAC_network_interface.c
file	sys/dev/gmac/synopGMAC_pci_bus_interface.c
file	sys/dev/gmac/synopGMAC_plat.c

# NAND
#file	sys/dev/nand/ls2k500-nand.c nand
file	sys/dev/nand/m25p80.c   m25p80 & nand needs-flag


device emmc
attach emmc at localbus

device tfcard
attach tfcard at localbus

file sys/dev/mmc/mmc.c       mci
file sys/dev/mmc/sd_card.c   mci
file sys/dev/mmc/core.c      mci needs-flag 
file sys/dev/mmc/ls_mci.c    mci 
file sys/dev/mmc/mci_pmon.c  mci 



device	syn: ether, ifnet
attach	syn at localbus
file	sys/dev/gmac/if_gmac.c		gmac


# Ethernet driver for Discovery ethernet
device	gt: ether, ifnet, ifmedia
attach	gt at localbus
file	sys/dev/ic/if_gt.c		gt

# AHCI
device	lahci {[channel = -1]} :ahcibus
attach	lahci at localbus

#
# Media Indepedent Interface (mii)
#
include "sys/dev/mii/files.mii"

# Various PCI bridges

include "sys/dev/pci/files.pci"

#device	pcisyn: ether, ifnet
#attach	pcisyn at pci

device	lohci {[channel = -1]} :usbbus
attach	lohci at localbus

device  lotg {[channel = -1]} :usbbus
attach  lotg at localbus

device	lehci {[channel = -1]} :usbbus
attach	lehci at localbus
#
# Machine-independent ATAPI drivers
#

include "sys/dev/ata/files.ata"

#
#  SCSI framework
#
include "sys/scsi/files.scsi"

#
# Custom application files
#
include "pmon/custom/files.custom"
