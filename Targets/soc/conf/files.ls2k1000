# $Id: files.Bonito
#
# Bonito Target specific files
#

file    Targets/soc/ls2k1000/serial.S
file    Targets/soc/ls2k1000/cpulib.S
file	Targets/soc/pci/pci_machdep.c
file	Targets/soc/ls2k1000/tgt_machdep.c
file	Targets/soc/dev/dc.c
file	Targets/soc/pci/ls2k_pci.c
file	Targets/soc/dev/fl_env.c

define  localbus { [base = -1 ] }
device  localbus
attach  localbus at mainbus
file    Targets/soc/dev/localbus.c		localbus
file    Targets/soc/dev/spi_w.c
file	Targets/soc/dev/eeprom.c
file	Targets/soc/dev/load_dtb.c		cmd_dtb
#file	Targets/ls2k/dev/slt.c

#XHCI
device lxhci  : xhcibus
attach lxhci at localbus

# OHCI
#device	lohci {[channel = -1]} :usbbus
#attach	lohci at localbus

#XHCI
#device lxhci  : xhcibus
#attach lxhci at localbus

# OTG Device
file sys/dev/usb/otg-dev/dwc2_udc.c		otg-device
file sys/dev/usb/otg-dev/dwc2_udc_otg.c		otg-device
file sys/dev/usb/otg-dev/printer.c		otg-device
file sys/dev/usb/otg-dev/gadget_config.c	otg-device
file sys/dev/usb/otg-dev/usbstring.c		otg-device
file sys/dev/usb/otg-dev/epautoconf.c		otg-device
file sys/dev/usb/otg-dev/otg_cmd.c		otg-device

# GMAC
#file	sys/dev/gmac/synopGMAC_Host.c
file	sys/dev/gmac/synopGMAC_Dev.c
file	sys/dev/gmac/synopGMAC_network_interface.c
file	sys/dev/gmac/synopGMAC_pci_bus_interface.c
file	sys/dev/gmac/synopGMAC_plat.c
file	sys/dev/nand/ls2k-nand.c nand needs-flag

file    sys/dev/nand/spinand_lld.c spinand_lld & nand needs-flag
#file    sys/dev/nand/spi/core.c spinand & nand needs-flag
#file    sys/dev/nand/spi/gigadevice.c spinand & nand needs-flag
file    sys/dev/nand/m25p80.c m25p80 & nand needs-flag


file    Targets/soc/dev/i2c.c
file    Targets/soc/dev/i2c-1.c lt8618 needs-flag
file	Targets/soc/dev/9022a.c sii9022a needs-flag
file	Targets/soc/dev/dc_bridge.c dc_bridge needs-flag
file    Targets/soc/dev/pai2.c
file	Targets/soc/dev/lt9211.c lt9211 needs-flag


device	syn: ether, ifnet
attach	syn at localbus
file	sys/dev/gmac/if_gmac.c gmac


# Ethernet driver for Discovery ethernet
device  gt: ether, ifnet, ifmedia
attach  gt at localbus
file    sys/dev/ic/if_gt.c			gt

device	sdcard
attach	sdcard at localbus


# AHCI
device	lahci {[channel = -1]} :ahcibus
attach	lahci at localbus

#
# Media Indepedent Interface (mii)
#
include "sys/dev/mii/files.mii"

# Various PCI bridges

include "sys/dev/pci/files.pci"

device	pcisyn: ether, ifnet
attach	pcisyn at pci

device	lohci {[channel = -1]} :usbbus
attach	lohci at localbus

device	lehci {[channel = -1]} :usbbus
attach	lehci at localbus
#
# Machine-independent ATAPI drivers
#

include "sys/dev/ata/files.ata"

#
#  SCSI framework
#
include "sys/scsi/files.scsi"

#
# Custom application files
#
include "pmon/custom/files.custom"
