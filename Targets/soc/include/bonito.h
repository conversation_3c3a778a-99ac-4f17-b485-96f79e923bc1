/*
 * Bonito Register Map
 * Copyright (c) 1999 Algorithmics Ltd
 *
 * Algorithmics gives permission for anyone to use and modify this file
 * without any obligation or license condition except that you retain
 * this copyright message in any source redistribution in whole or part.
 *
 * Updated copies of this and other files can be found at
 * ftp://ftp.algor.co.uk/pub/bonito/
 *
 * Users of the Bonito controller are warmly recommended to contribute
 * any useful changes back to Algorithmics (mail to
 * <EMAIL>).
 */

/* Revision 1.48 autogenerated on 08/17/99 15:20:01 */

#ifndef _BONITO_H_
#define _BONITO_H_
#include "cpu.h"

#ifdef __ASSEMBLER__
//__ASSEMBLER__ is not defined
/* offsets from base register */
#define BONITO(x)	(x)

#else /* !__ASSEMBLER */
extern char           *heaptop;

/* offsets from base pointer, this construct allows optimisation */
/* static char * const _bonito = PA_TO_KVA1(BONITO_BASE); */
/*#define BONITO(x)	*(volatile unsigned long *)(PHYS_TO_UNCACHED(BONITO_REG_BASE))+(x))*/
#define BONITO(x)	*(volatile unsigned long *)(PHYS_TO_UNCACHED(x))
struct pci_config_data {
		int bus;
		int dev;
		int func;
		int interrupt;
		int primary;
		int secondary;
		int subordinate;
		unsigned int mem_start;
		unsigned int mem_end;
		unsigned int io_start;
		unsigned int io_end;
#define PCI_DEV		0x1
#define PCI_BRIDGE	0x2
		int type;
}__attribute__((aligned(4)));
#define LS2K_PCI_IO_MASK	0x1ffffff
#endif /* __ASSEMBLER__ */

#if 1
/* 
 * Note: This code is shared with the memory source code and cannot be included by conditions defined
 * 	in other files, After modifying this part of the code, you need to recompile the memory code
 * ----------------------------------------------------------------------------------
 * | CLK0:8 | SLOT0_SPD0:8 | SLOT1_SPD0:8 | VREF0:20 | BIT_TRAIN0:252 | DDR_VREF0:152 = 18 * 8 + 8 |
 * | CLK1:8 | SLOT0_SPD1:8 | SLOT0_SPD1:8 | VREF1:20 | BIT_TRAIN1:252 | DDR_VREF1:152 = 18 * 8 + 8 |
 * ----------------------------------------------------------------------------------
 */
#define DRAM_DDR_VREF_NUM	18 * 8	/* 18 ddr vref value per rank, total 8 ranks */
#define RANK_DDR_VREF_NUM	8	/* 8 ddr vref values for 8 ranks */

#define MC_CHANNEL_CLK_WIDTH		8
#define MC_CHANNEL_SLOT0_WIDTH		8
#define MC_CHANNEL_SLOT1_WIDTH		8
#define MC_CHANNEL_MC_VREF_WIDTH	20
#define MC_CHANNEL_BIT_TRAIN_WIDTH	252
#define MC_CHANNEL_DDR_VREF_WIDTH	(DRAM_DDR_VREF_NUM + RANK_DDR_VREF_NUM)
#define MC_CHANNEL_CHECKSUM_WIDTH	8

#define DIMM_INFO_IN_FLASH_OFFS	(PHYS_TO_UNCACHED(0x1c000000) + DTB_OFFS - 0x1000)
#define LS7A_DIMM_INFO_IN_FLASH_OFFS	(PHYS_TO_UNCACHED(0x1c000000) + DTB_OFFS - 0x1000) + 0x9000
#define DIMM_INFO_IN_CACHE_OFFS	PHYS_TO_CACHED(0x10000000)
#define DIMM_INFO_IN_SDRAM_OFFS	PHYS_TO_UNCACHED(0xfff0000)
#define SMBIOS_INFO_IN_SDRAM_OFFS PHYS_TO_UNCACHED(0xfffc000)
#define DIMM_OFFS_CLK		0
#define DIMM_OFFS_SLOT0_SPD	(MC_CHANNEL_CLK_WIDTH)
#define DIMM_OFFS_SLOT1_SPD	(MC_CHANNEL_SLOT0_WIDTH + MC_CHANNEL_CLK_WIDTH)
#define DIMM_OFFS_VREF		(MC_CHANNEL_SLOT1_WIDTH + DIMM_OFFS_SLOT1_SPD)
#define DIMM_OFFS_BIT_TRAIN	(MC_CHANNEL_MC_VREF_WIDTH + DIMM_OFFS_VREF)
#define DIMM_OFFS_DDR_VREF	(MC_CHANNEL_BIT_TRAIN_WIDTH + DIMM_OFFS_BIT_TRAIN)
#define DIMM_OFFS_CHECKSUM	(MC_CHANNEL_DDR_VREF_WIDTH + DIMM_OFFS_DDR_VREF)

#ifdef LOONGSON_3C5000
#ifdef FLAT_MODE
#define DIMM_INFO_SIZE		(4 * MC_INFO_SIZE)
#else
#define DIMM_INFO_SIZE		(1 * MC_INFO_SIZE)
#endif
#else
#define DIMM_INFO_SIZE		(2 * MC_INFO_SIZE)
#endif

#define MC_INFO_SIZE		(DIMM_OFFS_CHECKSUM + MC_CHANNEL_CHECKSUM_WIDTH)
#define DIMM_VREF_DATA_NUM	9	/* vref has used 9 byte */
#define DIMM_OFFS_SPD1		(MC_INFO_SIZE + DIMM_OFFS_SPD)

/*********************************************************************/
/*nvram define                                                       */
/*********************************************************************/
#ifdef NVRAM_IN_FLASH
#define	NVRAM_SIZE              492
#ifdef GMAC_USE_FLASH_MAC
#define	GMAC_MAC_OFFS           500
#define	NVRAM_SECSIZE	        518
#else
#define	NVRAM_SECSIZE           500
#endif
#define ACTIVECOM_OFFS          492
#define MASTER_BRIDGE_OFFS      493
#ifdef BOOT_FROM_NAND
#define NVRAM_OFFS              0x000ffc00  /*number 4 block*/
#else
#define	NVRAM_OFFS              0x000ff000  /* erase ops is align with 4K, Set in common use */
#endif
#define ETHER_OFFS              494         /* Ethernet address base */
#else	/* Use clock ram, 256 bytes only */
#define NVRAM_SIZE              114
#ifdef GMAC_USE_FLASH_MAC
#define	GMAC_MAC_OFFS           NVRAM_SIZE
#define	NVRAM_SECSIZE	        (NVRAM_SIZE + 18)  /* Helper */
#else
#define NVRAM_SECSIZE           NVRAM_SIZE	/* Helper */
#endif
#define NVRAM_OFFS              0
#define ETHER_OFFS              108        /* Ethernet address base */
#endif

#ifdef LOONGSON_2K300 
#define	DTB_SIZE 0x5000		/* 20K dtb size*/
#else
#define	DTB_SIZE 0x4000		/* 16K dtb size*/
#endif
#define	DTB_OFFS		(NVRAM_OFFS - DTB_SIZE)
#endif
#define RTC_INDEX_REG		0x70
#define RTC_DATA_REG		0x71
#define RTC_NVRAM_BASE		0x0e

#if defined(LOONGARCH_2K500)
#define COM1_BASE_ADDR		LS2K500_UART2_REG_BASE
#endif

#if defined(LOONGARCH_2K1000)
#ifdef DEBUG_CONSOLE
#define COM1_BASE_ADDR		(LS2K1000_UART_REG_BASE | (DEBUG_CONSOLE << 8))
#else
#define COM1_BASE_ADDR		LS2K1000_UART_REG_BASE
#endif
#endif
//#define	NS16550HZ	1843200
#define	NS16550HZ		3686400

#if defined(LOONGARCH_2K300)
#ifdef USE_UART5_DEBUG
#define COM1_BASE_ADDR		LS2K300_UART5_REG_BASE		//for fpga 
#elif	USE_UART6_DEBUG
#define COM1_BASE_ADDR		LS2K300_UART6_REG_BASE		//for fpga 
#else
#define COM1_BASE_ADDR		LS2K300_UART0_REG_BASE		//for fpga 
#endif
#endif

#define BONITO_FLASH_BASE		PHYS_TO_UNCACHED(0x1c000000)
#define BONITO_FLASH_SIZE		0x100000

#ifdef LOONGARCH_2K1000
#define LIO_MEM_BASE0			PHYS_TO_UNCACHED(0x1e000000)
#define LIO_MEM_BASE1			PHYS_TO_UNCACHED(0x1b000000)
#define LIO_MEM_SIZE			0x2000000
#endif

#if defined(LOONGARCH_2K500) || defined(LOONGARCH_2K300)
#define LIO_MEM_BASE0			PHYS_TO_UNCACHED(0x1a000000)
#define LIO_MEM_BASE1			PHYS_TO_UNCACHED(0x1b000000)
#define LIO_MEM_SIZE			0x1000000
#endif

#define LPC_MEM_BASE			PHYS_TO_UNCACHED(0x1df00000)
#define LPC_FIRMWARE_MEM_BASE		PHYS_TO_UNCACHED(0x1d400000)
#define LPC_MEM_SIZE			0x1000000

#define SPI_MEM_BASE			PHYS_TO_UNCACHED(0x18000000)
#define SPI_MEM_SIZE			0x2000000

#define BONITO_PCICFG0_BASE_VA		PHYS_TO_UNCACHED(0xfe00000000)
#define BONITO_PCICFG1_BASE_VA		PHYS_TO_UNCACHED(0xfe10000000)

#define PCI_IO_SPACE_BASE		0x00000000

#ifdef LOONGARCH_2K1000
#define BONITO_PCILO_BASE_VA    	PHYS_TO_UNCACHED(0x20000000)
#define BONITO_PCILO_SIZE		0x40000000
#define BONITO_PCILO0_BASE		0x40000000
#define BONITO_PCIIO_BASE		0x18000000
#define BONITO_PCIIO_BASE_VA		PHYS_TO_UNCACHED(0x18000000)
#define BONITO_PCIIO_SIZE		0x02000000

#define LOCK_CACHE_BASE	PHYS_TO_CACHED(0x90000000)
#define LOCK_CACHE_SIZE		0x40000	//256KB

#define KBD_STATUS_REG					PHYS_TO_UNCACHED(0x1ff4c004)
#define KBD_CNTL_REG					PHYS_TO_UNCACHED(0x1ff4c004)
#define KBD_DATA_REG					PHYS_TO_UNCACHED(0x1ff4c000)

#define kbd_read_input()				inb(KBD_DATA_REG)
#define kbd_read_status()				inb(KBD_STATUS_REG)
#define kbd_write_output(val)				outb(KBD_DATA_REG,val)
#define kbd_write_command(val)				outb(KBD_CNTL_REG, val)
#endif

#if defined(LOONGARCH_2K500) || defined(LOONGARCH_2K300)
#define BONITO_PCILO_BASE_VA		PHYS_TO_UNCACHED(0x10000000)
#define BONITO_PCILO_SIZE		0x0c000000
#define BONITO_PCILO_TOP		(BONITO_PCILO_BASE+BONITO_PCILO_SIZE-1)
#define BONITO_PCILO0_BASE		0x10000000
#define BONITO_PCILO1_BASE		0x14000000
#define BONITO_PCILO2_BASE		0x18000000
#define BONITO_PCIHI_BASE		0x20000000
#define BONITO_PCIHI_SIZE		0x20000000
#define BONITO_PCIHI_TOP		(BONITO_PCIHI_BASE+BONITO_PCIHI_SIZE-1)
#define BONITO_PCIIO_BASE		0x1fd00000
#define BONITO_PCIIO_BASE_VA		PHYS_TO_UNCACHED(0x1fd00000)
#define BONITO_PCIIO_SIZE		0x00010000
#define BONITO_PCIIO_TOP		(BONITO_PCIIO_BASE+BONITO_PCIIO_SIZE-1)
#define BONITO_PCICFG_BASE		0x1fe80000
#define BONITO_PCICFG_SIZE		0x00080000
#define BONITO_PCICFG_TOP		(BONITO_PCICFG_BASE+BONITO_PCICFG_SIZE-1)

#define LOCK_CACHE_BASE			PHYS_TO_CACHED(0x90000000)
#define LOCK_CACHE_SIZE			0x40000	//256KB

#define SMBIOS_INFO_IN_SDRAM_OFFS	PHYS_TO_UNCACHED(0xfffc000)
#define KBD_STATUS_REG			PHYS_TO_UNCACHED(0x1ff4c004)
#define KBD_CNTL_REG			PHYS_TO_UNCACHED(0x1ff4c004)
#define KBD_DATA_REG			PHYS_TO_UNCACHED(0x1ff4c000)

#define kbd_read_input()		inb(KBD_DATA_REG)
#define kbd_read_status()		inb(KBD_STATUS_REG)
#define kbd_write_output(val)		outb(KBD_DATA_REG, val)
#define kbd_write_command(val)		outb(KBD_CNTL_REG, val)

#if 1
/* Bonito Register Bases */

#define  PCI_COMMAND_IO			0x1	/* Enable response in I/O space */
#define  PCI_COMMAND_MEMORY		0x2	/* Enable response in Memory space */
#define  PCI_COMMAND_MASTER		0x4	/* Enable bus mastering */

/* PCI Configuration Registers */

#define LOONGSON_PCIMAP_PCIMAP_LO0	0x0000003f
#define LOONGSON_PCIMAP_PCIMAP_LO0_SHIFT	0
#define LOONGSON_PCIMAP_PCIMAP_LO1	0x00000fc0
#define LOONGSON_PCIMAP_PCIMAP_LO1_SHIFT	6
#define LOONGSON_PCIMAP_PCIMAP_LO2	0x0003f000
#define LOONGSON_PCIMAP_PCIMAP_LO2_SHIFT	12
#define LOONGSON_PCIMAP_PCIMAP_2	0x00040000
#define LOONGSON_PCIMAP_WIN(WIN, ADDR)	\
	((((ADDR)>>26) & LOONGSON_PCIMAP_PCIMAP_LO0) << ((WIN)*6))


#define LOONGSON_PCILO0_BASE		0x10000000
#define LOONGSON_PCILO1_BASE		0x20000000
#define LOONGSON_PCILO2_BASE		0x24000000
/* PCI Configuration  Registers */

#define LOONGSON_PCICFG_BASE		0x17100000
#define BONITO_PCI_REG(x)		BONITO(0x17110000 + (x))
#define BONITO_PCIDID			BONITO_PCI_REG(0x00)
#define BONITO_PCICMD			BONITO_PCI_REG(0x04)
#define BONITO_PCICLASS			BONITO_PCI_REG(0x08)
#define BONITO_PCILTIMER		BONITO_PCI_REG(0x0c)
#define BONITO_PCIBASE0			BONITO_PCI_REG(0x10)
#define BONITO_PCIBASE1			BONITO_PCI_REG(0x14)
#define BONITO_PCIBASE2			BONITO_PCI_REG(0x18)
#define BONITO_PCIBASE3			BONITO_PCI_REG(0x1c)
#define BONITO_PCIBASE4			BONITO_PCI_REG(0x20)
#define BONITO_PCIEXPRBASE		BONITO_PCI_REG(0x30)
#define BONITO_PCIINT			BONITO_PCI_REG(0x3c)
#define BONITO_PCI_ISR4C		BONITO_PCI_REG(0x4c)

#define BONITO_PCICMD_PERR_CLR		0x80000000
#define BONITO_PCICMD_SERR_CLR		0x40000000
#define BONITO_PCICMD_MABORT_CLR	0x20000000
#define BONITO_PCICMD_MTABORT_CLR	0x10000000
#define BONITO_PCICMD_TABORT_CLR	0x08000000
#define BONITO_PCICMD_MPERR_CLR		0x01000000
#define BONITO_PCICMD_PERRRESPEN	0x00000040
#define BONITO_PCICMD_ASTEPEN		0x00000080
#define BONITO_PCICMD_SERREN		0x00000100
#define BONITO_PCILTIMER_BUSLATENCY	0x0000ff00
#define BONITO_PCILTIMER_BUSLATENCY_SHIFT	8

#define BONITO_REGBASE			0x1fe11100
#define BONITO_PXARB_CFG		BONITO(BONITO_REGBASE + 0x0c)
#define BONITO_PXARB_STATUS		BONITO(BONITO_REGBASE + 0x10)
#define BONITO_PCIMAP			BONITO(BONITO_REGBASE + 0x14)
#define BONITO_PCIMAP_CFG		BONITO(BONITO_REGBASE + 0x20)
#define BONITO_PCIX_RGATE		LOONGSON_REG(BONITO_REGBASE + 0x18)
#define BONITO_PCIX_RELAX_EN		LOONGSON_REG(BINITO_REGBASE + 0x1c)
#define BONITO_PCI_HIT0_SEL_L		BONITO(BONITO_REGBASE + 0x28)
#define BONITO_PCI_HIT0_SEL_H		BONITO(BONITO_REGBASE + 0x2c)
#define BONITO_PCI_HIT1_SEL_L		BONITO(BONITO_REGBASE + 0x30)
#define BONITO_PCI_HIT1_SEL_H		BONITO(BONITO_REGBASE + 0x34)
#define BONITO_PCI_HIT2_SEL_L		BONITO(BONITO_REGBASE + 0x38)
#define BONITO_PCI_HIT2_SEL_H		BONITO(BONITO_REGBASE + 0x3c)
#endif
#endif
#endif /* _BONITO_H_ */
