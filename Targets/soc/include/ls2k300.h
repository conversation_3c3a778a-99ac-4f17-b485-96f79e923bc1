#ifndef _LS2K300_H
#define _LS2K300_H
#include "cpu.h"

#define SRAM_TOP (0x16114000-4)

/* CHIP CONFIG regs */
#define LS2K300_GENERAL_CFG0				PHYS_TO_UNCACHED(0x16000100)
#define LS2K300_GENERAL_CFG1				PHYS_TO_UNCACHED(0x16000104)
#define LS2K300_GENERAL_CFG2				PHYS_TO_UNCACHED(0x16000108)
#define LS2K300_GENERAL_CFG3				PHYS_TO_UNCACHED(0x1600010c)
#define LS2K300_GENERAL_CFG4				PHYS_TO_UNCACHED(0x16000110)
#define LS2K300_GENERAL_CFG5				PHYS_TO_UNCACHED(0x16000114)
#define LS2K300_SAMPLE_CFG0					PHYS_TO_UNCACHED(0x16000120)
#define LS2K300_SAMPLE_CFG1					PHYS_TO_UNCACHED(0x16000124)
#define LS2K300_CHIP_HPT_LO					PHYS_TO_UNCACHED(0x16000130)
#define LS2K300_CHIP_HPT_HI					PHYS_TO_UNCACHED(0x16000134)

//GPIO regs
#define LS2K300_GPIO_MULTI_CFG				PHYS_TO_UNCACHED(0x16000490)
#define LS2K300_GPIO_BASE				    PHYS_TO_UNCACHED(0x16104000)

#define LS2K300_GPIO_BASE_BIT				PHYS_TO_UNCACHED(LS2K300_GPIO_BASE)

#define LS2K300_GPIO_BIT_OEN				PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BIT + 0x00)
#define LS2K300_GPIO_BIT_O				    PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BIT + 0x10)
#define LS2K300_GPIO_BIT_I				    PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BIT + 0x20)

#define LS2K300_GPIO_BIT_INT_EN				PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BIT + 0x30)
#define LS2K300_GPIO_BIT_INT_POL			PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BIT + 0x40)
#define LS2K300_GPIO_BIT_INT_EDGE			PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BIT + 0x50)
#define LS2K300_GPIO_BIT_INT_CLEAR			PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BIT + 0x60)
#define LS2K300_GPIO_BIT_INT_STS			PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BIT + 0x70)

#define LS2K300_GPIO_BASE_BYTE				PHYS_TO_UNCACHED(LS2K300_GPIO_BASE + 0x800)

#define LS2K300_GPIO_BYTE_OEN				PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BYTE + 0x000)
#define LS2K300_GPIO_BYTE_O					PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BYTE + 0x100)
#define LS2K300_GPIO_BYTE_I					PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BYTE + 0x200)

#define LS2K300_GPIO_BYTE_INT_EN			PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BYTE + 0x300)
#define LS2K300_GPIO_BYTE_INT_POL			PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BYTE + 0x400)
#define LS2K300_GPIO_BYTE_INT_EDGE			PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BYTE + 0x500)
#define LS2K300_GPIO_BYTE_INT_CLEAR			PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BYTE + 0x600)
#define LS2K300_GPIO_BYTE_INT_STS			PHYS_TO_UNCACHED(LS2K300_GPIO_BASE_BYTE + 0x700)


//USB PHY CFG
#define LS2K300_USB_PHY_CFG0				PHYS_TO_UNCACHED(0x16000500)
#define LS2K300_USB_PHY_CFG1				PHYS_TO_UNCACHED(0x16000504)

/* OTG regs */
#define LS2K300_OHCI_BASE				    PHYS_TO_UNCACHED(0x16040000)

/* USB regs */
#define LS2K300_EHCI_BASE				    PHYS_TO_UNCACHED(0x16080000)


/* GMAC regs */


/* DC regs */
#define	LS2K300_DC_BASE						PHYS_TO_UNCACHED(0x16090000)
#define LS2K300_FB_CFG_DVO_REG				(0x1240)
#define LS2K300_FB_CFG_VGA_REG				(0x1250)
#define LS2K300_FB_ADDR0_DVO_REG			(0x1260)
#define LS2K300_FB_ADDR0_VGA_REG			(0x1270)
#define LS2K300_FB_STRI_DVO_REG				(0x1280)
#define LS2K300_FB_STRI_VGA_REG				(0x1290)
#define LS2K300_FB_ADDR1_DVO_REG			(0x1580)
#define LS2K300_FB_ADDR1_VGA_REG			(0x1590)

#define LS2K300_FB_CUR_CFG_REG				(0x1520)
#define LS2K300_FB_CUR_ADDR_REG				(0x1530)
#define LS2K300_FB_CUR_LOC_ADDR_REG			(0x1540)
#define LS2K300_FB_CUR_BACK_REG				(0x1550)
#define LS2K300_FB_CUR_FORE_REG				(0x1560)

#define LS2K300_FB_DAC_CTRL_REG				(0x1600)

/* SPI regs */
#define LS2K300_SPI0_BASE					PHYS_TO_UNCACHED(0x16010000)
#define LS2K300_SPI1_BASE					PHYS_TO_UNCACHED(0x16018000)
#define LS2K300_SPI2_BASE					PHYS_TO_UNCACHED(0x1610c000)
#define LS2K300_SPI3_BASE					PHYS_TO_UNCACHED(0x1610e000)

/* UART regs */
#define LS2K300_UART0_REG_BASE				PHYS_TO_UNCACHED(0x16100000)
#define LS2K300_UART1_REG_BASE				PHYS_TO_UNCACHED(0x16100400)
#define LS2K300_UART2_REG_BASE				PHYS_TO_UNCACHED(0x16100800)
#define LS2K300_UART3_REG_BASE				PHYS_TO_UNCACHED(0x16100c00)
#define LS2K300_UART4_REG_BASE				PHYS_TO_UNCACHED(0x16101000)
#define LS2K300_UART5_REG_BASE				PHYS_TO_UNCACHED(0x16101400)
#define LS2K300_UART6_REG_BASE				PHYS_TO_UNCACHED(0x16101800)
#define LS2K300_UART7_REG_BASE				PHYS_TO_UNCACHED(0x16101c00)
#define LS2K300_UART8_REG_BASE				PHYS_TO_UNCACHED(0x16102000)
#define LS2K300_UART9_REG_BASE				PHYS_TO_UNCACHED(0x16102400)

/* I2C regs */
#define LS2K300_I2C0_REG_BASE				PHYS_TO_UNCACHED(0x16108000)
#define LS2K300_I2C0_PRER_LO_REG			(LS2K300_I2C0_REG_BASE + 0x0)
#define LS2K300_I2C0_PRER_HI_REG			(LS2K300_I2C0_REG_BASE + 0x1)
#define LS2K300_I2C0_CTR_REG				(LS2K300_I2C0_REG_BASE + 0x2)
#define LS2K300_I2C0_TXR_REG				(LS2K300_I2C0_REG_BASE + 0x3)
#define LS2K300_I2C0_RXR_REG				(LS2K300_I2C0_REG_BASE + 0x3)
#define LS2K300_I2C0_CR_REG					(LS2K300_I2C0_REG_BASE + 0x4)
#define LS2K300_I2C0_SR_REG					(LS2K300_I2C0_REG_BASE + 0x4)

#define LS2K300_I2C1_REG_BASE				PHYS_TO_UNCACHED(0x16109000)
#define LS2K300_I2C1_PRER_LO_REG			(LS2K300_I2C1_REG_BASE + 0x0)
#define LS2K300_I2C1_PRER_HI_REG			(LS2K300_I2C1_REG_BASE + 0x1)
#define LS2K300_I2C1_CTR_REG				(LS2K300_I2C1_REG_BASE + 0x2)
#define LS2K300_I2C1_TXR_REG				(LS2K300_I2C1_REG_BASE + 0x3)
#define LS2K300_I2C1_RXR_REG				(LS2K300_I2C1_REG_BASE + 0x3)
#define LS2K300_I2C1_CR_REG					(LS2K300_I2C1_REG_BASE + 0x4)
#define LS2K300_I2C1_SR_REG					(LS2K300_I2C1_REG_BASE + 0x4)


#define CR_START					0x80
#define CR_STOP						0x40
#define CR_READ						0x20
#define CR_WRITE					0x10
#define CR_ACK						0x8
#define CR_IACK						0x1

#define SR_NOACK					0x80
#define SR_BUSY						0x40
#define SR_AL						0x20
#define SR_TIP						0x2
#define	SR_IF						0x1

/* PWM regs */
#define LS2K300_PWM0_REG_BASE				PHYS_TO_UNCACHED(0x1611b000)

/* SDIO regs */
#define LS2K300_SDIO0_BASE					PHYS_TO_UNCACHED(0x16140000)
#define LS2K300_SDIO1_BASE					PHYS_TO_UNCACHED(0x16148000)

/* HPET regs */
#define LS2K300_HPET0_BASE					PHYS_TO_UNCACHED(0x16120000)
#define LS2K300_HPET0_PERIOD				LS2K300_HPET0_BASE + 0x4
#define LS2K300_HPET0_CONF					LS2K300_HPET0_BASE + 0x10
#define LS2K300_HPET0_MAIN					LS2K300_HPET0_BASE + 0xF0

#define LS2K300_HPET1_BASE					PHYS_TO_UNCACHED(0x16121000)
#define LS2K300_HPET2_BASE					PHYS_TO_UNCACHED(0x16122000)
#define LS2K300_HPET3_BASE					PHYS_TO_UNCACHED(0x16123000)

/* AC97 regs */
#define LS2K300_AC97_REG_BASE				PHYS_TO_UNCACHED(0x16114000)

/* DMA regs */
#define LS2K300_DMA_ORDER0					PHYS_TO_UNCACHED(0x1612c000)

//APB
//DMA
#define DMA_BASE			  PHYS_TO_UNCACHED(0x1612c000)

#define DMA1_BASE             (DMA_BASE)                 //(DMA_BASE + 0x0000)
#define DMA1_Channel1_BASE    (DMA1_BASE + 0x0008)       //(DMA_BASE + 0x0008)
#define DMA1_Channel2_BASE    (DMA1_BASE + 0x001C)       //(DMA_BASE + 0x001C)
#define DMA1_Channel3_BASE    (DMA1_BASE + 0x0030)       //(DMA_BASE + 0x0030)
#define DMA1_Channel4_BASE    (DMA1_BASE + 0x0044)       //(DMA_BASE + 0x0044)
#define DMA1_Channel5_BASE    (DMA1_BASE + 0x0058)       //(DMA_BASE + 0x0058)
#define DMA1_Channel6_BASE    (DMA1_BASE + 0x006c)       //(DMA_BASE + 0x006C)
#define DMA1_Channel7_BASE    (DMA1_BASE + 0x0080)       //(DMA_BASE + 0x0080)
#define DMA1_Channel8_BASE    (DMA1_BASE + 0x0094)       //(DMA_BASE + 0x0094)

#define DMA1                ((DMA_TypeDef *) DMA1_BASE)
#define DMA1_Channel1       ((DMA_Channel_TypeDef *) DMA1_Channel1_BASE)
#define DMA1_Channel2       ((DMA_Channel_TypeDef *) DMA1_Channel2_BASE)
#define DMA1_Channel3       ((DMA_Channel_TypeDef *) DMA1_Channel3_BASE)
#define DMA1_Channel4       ((DMA_Channel_TypeDef *) DMA1_Channel4_BASE)
#define DMA1_Channel5       ((DMA_Channel_TypeDef *) DMA1_Channel5_BASE)
#define DMA1_Channel6       ((DMA_Channel_TypeDef *) DMA1_Channel6_BASE)
#define DMA1_Channel7       ((DMA_Channel_TypeDef *) DMA1_Channel7_BASE)
#define DMA1_Channel8       ((DMA_Channel_TypeDef *) DMA1_Channel8_BASE)

#define DMA2_BASE             (DMA1_BASE) 
#define DMA2_Channel1_BASE    (0x4)
#define DMA2_Channel2_BASE    (0x5)
#define DMA2_Channel3_BASE    (0x6) 
#define DMA2_Channel4_BASE    (0x7)
#define DMA2_Channel5_BASE    (0x8)
#define DMA2                  ((DMA_TypeDef *) DMA2_BASE)
/* WDT regs */
#define LS2K300_WDT_BASE				PHYS_TO_UNCACHED(0x16124000)
#define LS2K300_RST_CNT_REG				(PHYS_TO_UNCACHED(0x16124000))
#define LS2K300_WD_SET_REG				(LS2K500_RST_CNT_REG + 0x4)
#define LS2K300_WD_TIMER_REG				(LS2K500_RST_CNT_REG + 0x8)

/* RTC regs */
#define LS2K300_RTC_REG_BASE				PHYS_TO_UNCACHED(0x16128000)
#define	LS2K300_TOY_TRIM_REG				(LS2K300_RTC_REG_BASE + 0x0020)
#define	LS2K300_TOY_WRITE0_REG				(LS2K300_RTC_REG_BASE + 0x0024)
#define	LS2K300_TOY_WRITE1_REG				(LS2K300_RTC_REG_BASE + 0x0028)
#define	LS2K300_TOY_READ0_REG				(LS2K300_RTC_REG_BASE + 0x002c)
#define	LS2K300_TOY_READ1_REG				(LS2K300_RTC_REG_BASE + 0x0030)
#define	LS2K300_TOY_MATCH0_REG				(LS2K300_RTC_REG_BASE + 0x0034)
#define	LS2K300_TOY_MATCH1_REG				(LS2K300_RTC_REG_BASE + 0x0038)
#define	LS2K300_TOY_MATCH2_REG				(LS2K300_RTC_REG_BASE + 0x003c)
#define	LS2K300_RTC_CTRL_REG				(LS2K300_RTC_REG_BASE + 0x0040)
#define	LS2K300_RTC_TRIM_REG				(LS2K300_RTC_REG_BASE + 0x0060)
#define	LS2K300_RTC_WRITE0_REG				(LS2K300_RTC_REG_BASE + 0x0064)
#define	LS2K300_RTC_READ0_REG				(LS2K300_RTC_REG_BASE + 0x0068)
#define	LS2K300_RTC_MATCH0_REG				(LS2K300_RTC_REG_BASE + 0x006c)
#define	LS2K300_RTC_MATCH1_REG				(LS2K300_RTC_REG_BASE + 0x0070)
#define	LS2K300_RTC_MATCH2_REG				(LS2K300_RTC_REG_BASE + 0x0074)

#define ADC_BASE	PHYS_TO_UNCACHED(0x1611c000)
#define ADC1_BASE  (ADC_BASE)
#define ADC1		((ADC_TypeDef *)ADC1_BASE)
#define ADC2_BASE   0x1
#define ADC3_BASE   0x2

#define READ_REG32(addr) (*(volatile uint32_t*)(addr))
#define WRITE_REG32(addr, value) *(volatile uint32_t*)(addr) = value

/* NODE PLL */
#define GMAC_FREQ	125
#define I2S_FREQ	100
#define NODE_REFC	6
#define NODE_DIV	2
#ifdef USE_120M
#define NODE_LOOPC	(CORE_FREQ*NODE_REFC*NODE_DIV/120)
#else
#define NODE_LOOPC	(CORE_FREQ*NODE_REFC*NODE_DIV/100)
#endif
#define GMAC_DIV	(CORE_FREQ*NODE_DIV/125)
#define I2S_DIV		(CORE_FREQ*NODE_DIV/100)

/* DDR PLL */
#define APB_FREQ_DEF	120	// The default frequency of APB when the chip is powered on
#define APB_FREQ	160
#ifdef USE_120M
#define DDR_REFC	3
#define DDR_DIV		2
#define DDR_LOOPC	(DDR_FREQ*DDR_REFC*DDR_DIV/120)
#else
#define DDR_REFC	4
#define DDR_DIV		4
#define DDR_LOOPC	(DDR_FREQ*DDR_REFC*DDR_DIV/100)
#endif
#define MEMDIV_MODE	1
#define DEV_DIV		(DDR_FREQ*DDR_DIV/APB_FREQ)
#define NETWORK_DIV	(DDR_FREQ*DDR_DIV/400)	//NETWORK400MHz

/* PIX PLL */
#define PIX_REFC	4
#ifdef USE_120M
#define PIX_LOOPC	50 //PIX_FREQ 100M
#define PIX_DIV		15
#define GMACBP_DIV	12 //GMAC_FREQ 125M
#else
#define PIX_LOOPC	80 //PIX_FREQ 100M
#define PIX_DIV		20
#define GMACBP_DIV	16 //GMAC_FREQ 125M
#endif

#define SEL_PLL0	(0x1)
#define SEL_PLL1	(0x2)
#define SEL_PLL2	(0x4)
#define PLL_L1_ENA	(0x1 << 3)
#define PLL_L1_LOCKED	(0x1 << 7)

//SB REG ADDR
#define CONFBUS_BASE    0x8000000016000000
#define CBUS_FEATURE    0x0008

#define CBUS_CHIP_CTRL0 0x0100
#define CBUS_CHIP_CTRL1 0x0108
#define CBUS_CHIP_CTRL2 0x0110
#define CBUS_CHIP_CTRL3 0x0118
#define CBUS_CHIP_CTRL4 0x0120
#define CBUS_CHIP_CTRL5 0x0128
#define CBUS_CHIP_CTRL6 0x0130
#define CBUS_CHIP_CTRL7 0x0138
#define CBUS_CHIP_SAMP0 0x0140
#define CBUS_CHIP_SAMP1 0x0148

#define CBUS_PLL_NODE    (CONFBUS_BASE + 0x0400)
#define CBUS_PLL_DDR     (CONFBUS_BASE + 0x0408)
#define CBUS_PLL_PIX     (CONFBUS_BASE + 0x0410)

#define CBUS_SB_GPIOCFG0 (CONFBUS_BASE + 0x0490)
#define CBUS_SB_GPIOCFG1 (CONFBUS_BASE + 0x0498)
#define CBUS_SB_GPIOCFG2 (CONFBUS_BASE + 0x04a0)
#define CBUS_SB_GPIOCFG3 (CONFBUS_BASE + 0x04a8)

#define CBUS_USB_PHY     (CONFBUS_BASE + 0x0500)

#define CBUS_THSENS_INT  (CONFBUS_BASE + 0x1500)

#define CBUS_CHIP_ID0    (CONFBUS_BASE + 0x3ff0)
#define CBUS_CHIP_ID1    (CONFBUS_BASE + 0x3ff8)
#endif /*_LS2K300_H*/
