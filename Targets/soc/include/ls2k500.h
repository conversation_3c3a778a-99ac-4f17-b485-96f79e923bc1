#ifndef _LS2H_H
#define _LS2H_H
#include "cpu.h"

/* CHIP CONFIG regs */
#define LS2K500_GENERAL_CFG0				PHYS_TO_UNCACHED(0x1fe10100)
#define LS2K500_GENERAL_CFG1				PHYS_TO_UNCACHED(0x1fe10104)
#define LS2K500_GENERAL_CFG2				PHYS_TO_UNCACHED(0x1fe10108)
#define LS2K500_GENERAL_CFG3				PHYS_TO_UNCACHED(0x1fe1010c)
#define LS2K500_GENERAL_CFG4				PHYS_TO_UNCACHED(0x1fe10110)
#define LS2K500_GENERAL_CFG5				PHYS_TO_UNCACHED(0x1fe10114)
#define LS2K500_SAMPLE_CFG0				PHYS_TO_UNCACHED(0x1fe10120)
#define LS2K500_CHIP_HPT_LO				PHYS_TO_UNCACHED(0x1fe10130)
#define LS2K500_CHIP_HPT_HI				PHYS_TO_UNCACHED(0x1fe10134)

#define GPIO_SKIP_64_OFFSET				(0x20)
#define LS2K500_GPIO_00_63_DIR				PHYS_TO_UNCACHED(0x1fe10430)
#define LS2K500_GPIO_00_63_IN				PHYS_TO_UNCACHED(0x1fe10438)
#define LS2K500_GPIO_00_63_OUT				PHYS_TO_UNCACHED(0x1fe10440)

#define LS2K500_GPIO_MULTI_CFG				PHYS_TO_UNCACHED(0x1fe10490)
#define LS2K500_GPIO_IRQ_EN_CFG				PHYS_TO_UNCACHED(0x1fe104e0)
#define LS2K500_USB_PHY_CFG0				PHYS_TO_UNCACHED(0x1fe10500)
#define LS2K500_USB_PHY_CFG1				PHYS_TO_UNCACHED(0x1fe10504)
#define LS2K500_USB_PHY_CFG2				PHYS_TO_UNCACHED(0x1fe10508)
#define LS2K500_USB_PHY_CFG3				PHYS_TO_UNCACHED(0x1fe1050c)
#define LS2K500_PCIE0_CFG0				PHYS_TO_UNCACHED(0x1fe10530)
#define LS2K500_PCIE0_CFG1				PHYS_TO_UNCACHED(0x1fe10534)
#define LS2K500_PCIE0_CFG2				PHYS_TO_UNCACHED(0x1fe10538)
#define LS2K500_PCIE0_CFG3				PHYS_TO_UNCACHED(0x1fe1053c)
#define LS2K500_PCIE0_PHY_CFG0				PHYS_TO_UNCACHED(0x1fe10540)
#define LS2K500_PCIE0_PHY_CFG1				PHYS_TO_UNCACHED(0x1fe10544)

#define LS2K500_PIXCLK0_CTRL0_REG			PHYS_TO_UNCACHED(0x1fe10418)
#define LS2K500_PIXCLK0_CTRL1_REG			PHYS_TO_UNCACHED(0x1fe1041c)
#define LS2K500_PIXCLK1_CTRL0_REG			PHYS_TO_UNCACHED(0x1fe10420)
#define LS2K500_PIXCLK1_CTRL1_REG			PHYS_TO_UNCACHED(0x1fe10424)

#define PIXCLK_CTRL0_PSTDIV_SET				(1 << 31)
#define PIXCLK_CTRL0_PSTDIV_PD				(1 << 30)
#define PIXCLK_CTRL0_PSTDIV_DF				(0X3f << 24)
#define PIXCLK_CTRL0_PLL_PD				(1 << 7)
#define PIXCLK_CTRL0_PLL_LDF				(0xff << 16)
#define PIXCLK_CTRL0_PLL_ODF				(0x3 << 5)
#define PIXCLK_CTRL0_PLL_IDF				(0X7 << 2)
#define PIXCLK_CTRL0_REF_SEL				0X3

#define writel_reg_bit( addr, clear_bit, bit_val)	(*(volatile unsigned int*)(addr)) = (*(volatile unsigned int*)(addr)) & (~clear_bit) | bit_val

/* OTG regs */

/* OHCI regs */
#define LS2K500_EHCI_BASE				PHYS_TO_UNCACHED(0x1f050000)

/* XHCI regs */
#define LS2K500_XHCI_BASE				PHYS_TO_UNCACHED(0x1f060000)
#define LS2K500_XHCI_GCTL				(LS2K500_XHCI_BASE + 0xc110)
#define LS2K500_XHCI_PIPECTL				(LS2K500_XHCI_BASE + 0xc2c0)

/* GMAC regs */

/* HDA regs */

/* SATAregs */
#define LS2K500_SATA_BASE				PHYS_TO_UNCACHED(0x1f040000)

/* GPU regs */

/* DC regs */
#define	LS2K500_DC_BASE					PHYS_TO_UNCACHED(0x1f010000)
#define LS2K500_FB_CFG_DVO_REG				(0x1240)
#define LS2K500_FB_CFG_VGA_REG				(0x1250)
#define LS2K500_FB_ADDR0_DVO_REG			(0x1260)
#define LS2K500_FB_ADDR0_VGA_REG			(0x1270)
#define LS2K500_FB_STRI_DVO_REG				(0x1280)
#define LS2K500_FB_STRI_VGA_REG				(0x1290)
#define LS2K500_FB_ADDR1_DVO_REG			(0x1580)
#define LS2K500_FB_ADDR1_VGA_REG			(0x1590)

#define LS2K500_FB_CUR_CFG_REG				(0x1520)
#define LS2K500_FB_CUR_ADDR_REG				(0x1530)
#define LS2K500_FB_CUR_LOC_ADDR_REG			(0x1540)
#define LS2K500_FB_CUR_BACK_REG				(0x1550)
#define LS2K500_FB_CUR_FORE_REG				(0x1560)

#define LS2K500_FB_DAC_CTRL_REG				(0x1600)

/* SPI regs */
#define LS2K500_SPI0_BASE				PHYS_TO_UNCACHED(0x1fd00000)
#define LS2K500_SPI1_BASE				PHYS_TO_UNCACHED(0x1fd40000)
#define LS2K500_SPI2_BASE				PHYS_TO_UNCACHED(0x1ff50000)
#define LS2K500_SPI3_BASE				PHYS_TO_UNCACHED(0x1ff51000)
#define LS2K500_SPI4_BASE				PHYS_TO_UNCACHED(0x1ff52000)
#define LS2K500_SPI5_BASE				PHYS_TO_UNCACHED(0x1ff53000)

/* UART regs */
#define LS2K500_UART0_REG_BASE				PHYS_TO_UNCACHED(0x1ff40000)
#define LS2K500_UART1_REG_BASE				PHYS_TO_UNCACHED(0x1ff40400)
#define LS2K500_UART2_REG_BASE				PHYS_TO_UNCACHED(0x1ff40800)
#define LS2K500_UART3_REG_BASE				PHYS_TO_UNCACHED(0x1ff40c00)
#define LS2K500_UART4_REG_BASE				PHYS_TO_UNCACHED(0x1ff41000)
#define LS2K500_UART5_REG_BASE				PHYS_TO_UNCACHED(0x1ff41400)
#define LS2K500_UART6_REG_BASE				PHYS_TO_UNCACHED(0x1ff41800)
#define LS2K500_UART7_REG_BASE				PHYS_TO_UNCACHED(0x1ff41c00)
#define LS2K500_UART8_REG_BASE				PHYS_TO_UNCACHED(0x1ff42000)
#define LS2K500_UART9_REG_BASE				PHYS_TO_UNCACHED(0x1ff42400)

/* I2C regs */
//APB configured addr 0x1fe0,i2c0 addr is 0x1fe01000
#define LS2K500_I2C0_REG_BASE				PHYS_TO_UNCACHED(0x1ff48000)
#define LS2K500_I2C0_PRER_LO_REG			(LS2K500_I2C0_REG_BASE + 0x0)
#define LS2K500_I2C0_PRER_HI_REG			(LS2K500_I2C0_REG_BASE + 0x1)
#define LS2K500_I2C0_CTR_REG				(LS2K500_I2C0_REG_BASE + 0x2)
#define LS2K500_I2C0_TXR_REG				(LS2K500_I2C0_REG_BASE + 0x3)
#define LS2K500_I2C0_RXR_REG				(LS2K500_I2C0_REG_BASE + 0x3)
#define LS2K500_I2C0_CR_REG				(LS2K500_I2C0_REG_BASE + 0x4)
#define LS2K500_I2C0_SR_REG				(LS2K500_I2C0_REG_BASE + 0x4)
#define GPIO_MULUSE_BASE					PHYS_TO_UNCACHED(0x1fe104b0)

#define LS2K500_I2C1_REG_BASE				PHYS_TO_UNCACHED(0x1ff48800)
#define LS2K500_I2C1_PRER_LO_REG			(LS2K500_I2C1_REG_BASE + 0x0)
#define LS2K500_I2C1_PRER_HI_REG			(LS2K500_I2C1_REG_BASE + 0x1)
#define LS2K500_I2C1_CTR_REG				(LS2K500_I2C1_REG_BASE + 0x2)
#define LS2K500_I2C1_TXR_REG				(LS2K500_I2C1_REG_BASE + 0x3)
#define LS2K500_I2C1_RXR_REG				(LS2K500_I2C1_REG_BASE + 0x3)
#define LS2K500_I2C1_CR_REG				(LS2K500_I2C1_REG_BASE + 0x4)
#define LS2K500_I2C1_SR_REG				(LS2K500_I2C1_REG_BASE + 0x4)

#define LS2K500_I2C4_REG_BASE				PHYS_TO_UNCACHED(0x1ff4a000)	//PIX0

#define CR_START					0x80
#define CR_STOP						0x40
#define CR_READ						0x20
#define CR_WRITE					0x10
#define CR_ACK						0x8
#define CR_IACK						0x1

#define SR_NOACK					0x80
#define SR_BUSY						0x40
#define SR_AL						0x20
#define SR_TIP						0x2
#define	SR_IF						0x1

/* PWM regs */
#define LS2K500_PWM0_REG_BASE				PHYS_TO_UNCACHED(0x1ff5c000)
#define LS2K500_PWM1_REG_BASE				PHYS_TO_UNCACHED(0x1ff5c010)

/* SDIO regs */
#define LS2K500_SDIO0_BASE 				PHYS_TO_UNCACHED(0x1ff64000)
#define LS2K500_SDIO1_BASE 				PHYS_TO_UNCACHED(0x1ff66000)

/* HPET regs */
#define LS2K500_HPET0_BASE 				PHYS_TO_UNCACHED(0x1ff68000)
#define LS2K500_HPET0_PERIOD				LS2K500_HPET0_BASE + 0x4
#define LS2K500_HPET0_CONF				LS2K500_HPET0_BASE + 0x10
#define LS2K500_HPET0_MAIN				LS2K500_HPET0_BASE + 0xF0

#define LS2K500_HPET1_BASE 				PHYS_TO_UNCACHED(0x1ff69000)
#define LS2K500_HPET2_BASE 				PHYS_TO_UNCACHED(0x1ff6a000)
#define LS2K500_HPET3_BASE 				PHYS_TO_UNCACHED(0x1ff6b000)

/* AC97 regs */
#define LS2K500_AC97_REG_BASE				PHYS_TO_UNCACHED(0x1ff54000)

/* NAND regs */
#define LS2K500_NAND_REG_BASE				PHYS_TO_UNCACHED(0x1ff58000)
#define LS2K500_NAND_CMD_REG				(LS2K500_NAND_REG_BASE + 0x0000)
#define LS2K500_NAND_ADDR_C_REG				(LS2K500_NAND_REG_BASE + 0x0004)
#define LS2K500_NAND_ADDR_R_REG				(LS2K500_NAND_REG_BASE + 0x0008)
#define LS2K500_NAND_TIMING_REG				(LS2K500_NAND_REG_BASE + 0x000c)
#define LS2K500_NAND_IDL_REG				(LS2K500_NAND_REG_BASE + 0x0010)
#define LS2K500_NAND_STA_IDH_REG			(LS2K500_NAND_REG_BASE + 0x0014)
#define LS2K500_NAND_PARAM_REG				(LS2K500_NAND_REG_BASE + 0x0018)
#define LS2K500_NAND_OP_NUM_REG				(LS2K500_NAND_REG_BASE + 0x001c)
#define LS2K500_NAND_CSRDY_MAP_REG			(LS2K500_NAND_REG_BASE + 0x0020)
#define LS2K500_NAND_DMA_ACC_REG			(LS2K500_NAND_REG_BASE + 0x0040)

/* ACPI regs */
#define LS2K500_ACPI_REG_BASE				PHYS_TO_UNCACHED(0x1ff6c000)
#define LS2K500_PM_SOC_REG				(LS2K500_ACPI_REG_BASE + 0x0000)
#define LS2K500_PM_RESUME_REG				(LS2K500_ACPI_REG_BASE + 0x0004)
#define LS2K500_PM_RTC_REG				(LS2K500_ACPI_REG_BASE + 0x0008)
#define LS2K500_PM1_STS_REG				(LS2K500_ACPI_REG_BASE + 0x000c)
#define LS2K500_PM1_EN_REG				(LS2K500_ACPI_REG_BASE + 0x0010)
#define LS2K500_PM1_CNT_REG				(LS2K500_ACPI_REG_BASE + 0x0014)
#define LS2K500_PM1_TMR_REG				(LS2K500_ACPI_REG_BASE + 0x0018)
#define LS2K500_P_CNT_REG				(LS2K500_ACPI_REG_BASE + 0x001c)
#define LS2K500_P_LVL2_REG				(LS2K500_ACPI_REG_BASE + 0x0020)
#define LS2K500_P_LVL3_REG				(LS2K500_ACPI_REG_BASE + 0x0024)
#define LS2K500_GPE0_STS_REG				(LS2K500_ACPI_REG_BASE + 0x0028)
#define LS2K500_GPE0_EN_REG				(LS2K500_ACPI_REG_BASE + 0x002c)
#define LS2K500_RST_CNT_REG				(LS2K500_ACPI_REG_BASE + 0x0030)
#define LS2K500_WD_SET_REG				(LS2K500_ACPI_REG_BASE + 0x0034)
#define LS2K500_WD_TIMER_REG				(LS2K500_ACPI_REG_BASE + 0x0038)
#define LS2K500_DVFS_CNT_REG				(LS2K500_ACPI_REG_BASE + 0x003c)
#define LS2K500_DVFS_STS_REG				(LS2K500_ACPI_REG_BASE + 0x0040)
#define LS2K500_MS_CNT_REG				(LS2K500_ACPI_REG_BASE + 0x0044)
#define LS2K500_MS_THT_REG				(LS2K500_ACPI_REG_BASE + 0x0048)
#define	LS2K500_THSENS_CNT_REG				(LS2K500_ACPI_REG_BASE + 0x004c)
#define LS2K500_GEN_RTC1_REG				(LS2K500_ACPI_REG_BASE + 0x0050)
#define LS2K500_GEN_RTC2_REG				(LS2K500_ACPI_REG_BASE + 0x0054)

/* DMA regs */
#define LS2K500_DMA_ORDER0				PHYS_TO_UNCACHED(0x1fe10c00)
#define LS2K500_DMA_ORDER1				PHYS_TO_UNCACHED(0x1fe10c10)
#define LS2K500_DMA_ORDER2				PHYS_TO_UNCACHED(0x1fe10c20)
#define LS2K500_DMA_ORDER3				PHYS_TO_UNCACHED(0x1fe10c30)


/* RTC regs */
#define LS2K500_RTC_REG_BASE				PHYS_TO_UNCACHED(0x1ff6c100)
#define	LS2K500_TOY_TRIM_REG				(LS2K500_RTC_REG_BASE + 0x0020)
#define	LS2K500_TOY_WRITE0_REG				(LS2K500_RTC_REG_BASE + 0x0024)
#define	LS2K500_TOY_WRITE1_REG				(LS2K500_RTC_REG_BASE + 0x0028)
#define	LS2K500_TOY_READ0_REG				(LS2K500_RTC_REG_BASE + 0x002c)
#define	LS2K500_TOY_READ1_REG				(LS2K500_RTC_REG_BASE + 0x0030)
#define	LS2K500_TOY_MATCH0_REG				(LS2K500_RTC_REG_BASE + 0x0034)
#define	LS2K500_TOY_MATCH1_REG				(LS2K500_RTC_REG_BASE + 0x0038)
#define	LS2K500_TOY_MATCH2_REG				(LS2K500_RTC_REG_BASE + 0x003c)
#define	LS2K500_RTC_CTRL_REG				(LS2K500_RTC_REG_BASE + 0x0040)
#define	LS2K500_RTC_TRIM_REG				(LS2K500_RTC_REG_BASE + 0x0060)
#define	LS2K500_RTC_WRITE0_REG				(LS2K500_RTC_REG_BASE + 0x0064)
#define	LS2K500_RTC_READ0_REG				(LS2K500_RTC_REG_BASE + 0x0068)
#define	LS2K500_RTC_MATCH0_REG				(LS2K500_RTC_REG_BASE + 0x006c)
#define	LS2K500_RTC_MATCH1_REG				(LS2K500_RTC_REG_BASE + 0x0070)
#define	LS2K500_RTC_MATCH2_REG				(LS2K500_RTC_REG_BASE + 0x0074)

/* LPC regs */
#define LS2K500_LPC_MEM_BASE				PHYS_TO_UNCACHED(0x1c000000)
#define LS2K500_LPC_IO_BASE				PHYS_TO_UNCACHED(0x1f0d0000)
#define LS2K500_LPC_REG_BASE				PHYS_TO_UNCACHED(0x1f0e0000)
#define LS2K500_LPC_CFG0_REG				(LS2K500_LPC_REG_BASE + 0x0)
#define LS2K500_LPC_CFG1_REG				(LS2K500_LPC_REG_BASE + 0x4)
#define LS2K500_LPC_CFG2_REG				(LS2K500_LPC_REG_BASE + 0x8)
#define LS2K500_LPC_CFG3_REG				(LS2K500_LPC_REG_BASE + 0xc)

/* For PS2 */
#define LS2K500_PS2_DLL					PHYS_TO_UNCACHED(0x1ff4c008)
#define LS2K500_PS2_DLH					PHYS_TO_UNCACHED(0x1ff4c009)

/* suspend to ram */
#define STR_STORE_BASE					PHYS_TO_UNCACHED(0xfaaa000)
#define SLEEP_TYPE_S3					0x5
#define STR_XBAR_CONFIG_NODE_a0(OFFSET, BASE, MASK, MMAP) \
        addi.d   v0, t0, OFFSET;     \
        li.d     t1, BASE;           \
        or      t1, t1, a0;         \
        st.d      t1, v0, 0x00;       \
        li.d     t1, MASK;           \
        st.d      t1, v0, 0x40;       \
        li.d     t1, MMAP;           \
        st.d      t1, v0, 0x80;

/* NODE PLL */
//#define CORE_FREQ	500					//CPU 500Mhz defined at platform.h
#define NODE_REFC	4
#define NODE_DIV	4
#define NODE_LOOPC	(CORE_FREQ*NODE_REFC*NODE_DIV/100)

/* DDR PLL */
//#define DDR_FREQ	400					//MEM 400~600Mhz
#define DDR_REFC	4
#define DDR_DIV		6
#define DDR_LOOPC	(DDR_FREQ*DDR_REFC*DDR_DIV/100)
#define HDA_DIV		((DDR_FREQ*DDR_DIV+12)/24)		//HDA 24MHz
#define NETWORK_DIV	((DDR_FREQ*DDR_DIV+100)/200)		//NETWORK 200~400MHz

/* SOC PLL */
#define GMAC_FREQ	125					//GMAC 125MHz
#define GPU_FREQ	200					//GPU 200~300MHz
#define SB_FREQ		100					//SB 100~200MHz
#define SOC_LOOPC	(GMAC_FREQ*SOC_REFC*GMAC_DIV/100)
#define SOC_REFC	4
#define GMAC_DIV	16
#define GPU_DIV		((GMAC_FREQ*GMAC_DIV+GPU_FREQ/2)/GPU_FREQ)
#define SB_DIV		((GMAC_FREQ*GMAC_DIV+SB_FREQ/2)/SB_FREQ)

/* PIX PLL */
#define PIX0_LOOPC	109					//100~200MHz
#define PIX0_REFC	5
#define PIX0_DIV	20

#define PIX1_LOOPC	109
#define PIX1_REFC	5
#define PIX1_DIV	20

#define SEL_PLL0	(0x1)
#define SEL_PLL1	(0x2)
#define SEL_PLL2	(0x4)
#define PLL_L1_ENA	(0x1 << 3)
#define PLL_L1_LOCKED	(0x1 << 7)

#endif /*_LS2H_H*/
