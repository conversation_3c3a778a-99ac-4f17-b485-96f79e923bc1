/*
 * Copyright (c) 2000 Opsycon AB  (www.opsycon.se)
 * Copyright (c) 2002 <PERSON><PERSON> (www.lindergren.com)
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB.
 *	This product includes software developed by <PERSON><PERSON>.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */
/* $Id: diskfs.h,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ */

#ifndef __DISKFS_H__
#define __DISKFS_H__

#include <part.h>
#include <sys/queue.h>
#define FS_TYPE_EXT2  0x0
#define FS_TYPE_SWAP  0x1
#define FS_TYPE_FAT   0x2
#define FS_TYPE_ISO9660 0x3
#define FS_TYPE_NTFS 0x4
#define FS_TYPE_BSD 0x5
#define FS_TYPE_COMPOUND 0xFE
#define FS_TYPE_UNKNOWN 0xFF 

//#define FS_SHOW_INFO
#ifdef FS_SHOW_INFO
#define fs_info(format, arg...) printf("FS_INFO: " format "\n", ## arg)
#else
#define fs_info(format, arg...) do {} while(0)
#endif

typedef __signed__ char __s8;
typedef unsigned char __u8;
typedef __signed__ short __s16;
typedef unsigned short __u16;
typedef __signed__ int __s32;
typedef unsigned int __u32;
typedef unsigned long long __u64;

typedef struct DiskFileSystem {
	char *fsname;
	int (*open) __P((int, const char *, int, int));
	int (*read) __P((int , void *, size_t));
	int (*write) __P((int , const void *, size_t));
	off_t (*lseek) __P((int , off_t, int));
	int (*close) __P((int));
	int (*ioctl) __P((int , unsigned long , ...));
	SLIST_ENTRY(DiskFileSystem)	i_next;
} DiskFileSystem;
#if 0
typedef struct DiskFile {
	char *devname;
	DiskFileSystem *dfs;
} DiskFile;
#endif

int diskfs_init(DiskFileSystem *fs);

typedef struct DiskPartitionTable { 
	struct DiskPartitionTable* Next;
	struct DiskPartitionTable* logical;
	unsigned char bootflag;
	unsigned char tag;
	unsigned char id;
	unsigned int sec_begin;
	unsigned int size;
	unsigned int sec_end;
	unsigned int part_fstype;
	DiskFileSystem* fs;
} DiskPartitionTable;

#define MAX_PARTS 128
typedef struct DeviceDisk {
	struct DeviceDisk* Next;
	char device_name[20];
	unsigned int dev_fstype;
	unsigned int dev_fscount;
//	DiskPartitionTable* part;
	block_dev_desc_t *desc;
	DiskPartitionTable* part[MAX_PARTS];
} DeviceDisk;

typedef struct DiskFile {
	char *devname;
	DiskFileSystem *dfs;
	DiskPartitionTable* part;
	int part_index;
} DiskFile;

/*GPT header magic: EFI''PART*/
static __u8 gpt_magic[8] = {
	0x45, 0x46, 0x49, 0x20, 0x50, 0x41, 0x52, 0x54
};

/*EFI PatitiontypeGUID*/
static __u8 gpt_boot_type[16] = {
	0x28, 0x73, 0x2a, 0xc1, 0x1f, 0xf8, 0xd2, 0x11,
	0xba, 0x4b, 0x0,  0xa0, 0xc9, 0x3e, 0xc9, 0x3b
};

static struct gpt_parttype {
	char *name;
	unsigned char type[16];
};

static const struct gpt_parttype gpt_type[] = {
    {"NULL",                        {0}},
    /* esp -- the first partition */
    {"EFI System",                  {0x28, 0x73, 0x2a, 0xc1, 0x1f, 0xf8, 0xd2, 0x11, 0xba, 0x4b, 0x00, 0xa0, 0xc9, 0x3e, 0xc9, 0x3b}},
    {"MBR partition scheme",        {0x41, 0xee, 0x4d, 0x02, 0xe7, 0x33, 0xd3, 0x11, 0x9d, 0x69, 0x00, 0x08, 0xc7, 0x81, 0xf3, 0x9f}},
    {"Intel Fast Flash",            {0xde, 0xe2, 0xbf, 0xd3, 0xaf, 0x3d, 0xdf, 0x11, 0xba, 0x40, 0xe3, 0xa5, 0x56, 0xd8, 0x95, 0x93}},
    {"BIOS boot",                   {0x48, 0x61, 0x68, 0x21, 0x49, 0x64, 0x6f, 0x6e, 0x74, 0x4e, 0x65, 0x65, 0x64, 0x45, 0x46, 0x49}},
    {"Microsoft reserved",          {0x16, 0xe3, 0xc9, 0xe3, 0x5c, 0x0b, 0xb8, 0x4d, 0x81, 0x7d, 0xf9, 0x2d, 0xf0, 0x02, 0x15, 0xae}},
    {"Microsoft basic data",        {0xa2, 0xa0, 0xd0, 0xeb, 0xe5, 0xb9, 0x33, 0x44, 0x87, 0xc0, 0x68, 0xb6, 0xb7, 0x26, 0x99, 0xc7}},
    {"Microsoft LDM metadata",      {0xaa, 0xc8, 0x08, 0x58, 0x8f, 0x7e, 0xe0, 0x42, 0x85, 0xd2, 0xe1, 0xe9, 0x04, 0x34, 0xcf, 0xb3}},
    {"Microsoft LDM data",          {0xa0, 0x60, 0x9b, 0xaf, 0x31, 0x14, 0x62, 0x4f, 0xbc, 0x68, 0x33, 0x11, 0x71, 0x4a, 0x69, 0xad}},
    {"Windows recovery environment",{0xa4, 0xbb, 0x94, 0xde, 0xd1, 0x06, 0x40, 0x4d, 0xa1, 0x6a, 0xbf, 0xd5, 0x01, 0x79, 0xd6, 0xac}},
    {"IBM General Parallel Fs",     {0x90, 0xfc, 0xaf, 0x37, 0x7d, 0xef, 0x96, 0x4e, 0x91, 0xc3, 0x2d, 0x7a, 0xe0, 0x55, 0xb1, 0x74}},
    {"Microsoft Storage Spaces",    {0x8f, 0xaf, 0x5c, 0xe7, 0x80, 0xf6, 0xee, 0x4c, 0xaf, 0xa3, 0xb0, 0x01, 0xe5, 0x6e, 0xfc, 0x2d}},
    {"HP-UX data",                  {0x1e, 0x4c, 0x89, 0x75, 0xeb, 0x3a, 0xd3, 0x11, 0xb7, 0xc1, 0x7b, 0x03, 0xa0, 0x00, 0x00, 0x00}},
    {"HP-UX service",               {0x28, 0xe7, 0xa1, 0xe2, 0xe3, 0x32, 0xd6, 0x11, 0xa6, 0x82, 0x7b, 0x03, 0xa0, 0x00, 0x00, 0x00}},
    {"Linux swap",                  {0x6d, 0xfd, 0x57, 0x06, 0xab, 0xa4, 0xc4, 0x43, 0x84, 0xe5, 0x09, 0x33, 0xc8, 0x4b, 0x4f, 0x4f}},
    /* linux data -- ext2/3/4 */
    {"Linux filesystem",            {0xaf, 0x3d, 0xc6, 0x0f, 0x83, 0x84, 0x72, 0x47, 0x8e, 0x79, 0x3d, 0x69, 0xd8, 0x47, 0x7d, 0xe4}},
    {"Linux server data",           {0x25, 0x84, 0x8f, 0x3b, 0xe0, 0x20, 0x3b, 0x4f, 0x90, 0x7f, 0x1a, 0x25, 0xa7, 0x6f, 0x98, 0xe8}},
    {"Linux root (x86)",            {0x40, 0x95, 0x47, 0x44, 0x97, 0xf2, 0xb2, 0x41, 0x9a, 0xf7, 0xd1, 0x31, 0xd5, 0xf0, 0x45, 0x8a}},
    {"Linux root (x86-64)",         {0xe3, 0xbc, 0x68, 0x4f, 0xcd, 0xe8, 0xb1, 0x4d, 0x96, 0xe7, 0xfb, 0xca, 0xf9, 0x84, 0xb7, 0x09}},
    {"Linux reserved",              {0x39, 0x33, 0xa6, 0x8d, 0x07, 0x00, 0xc0, 0x60, 0xc4, 0x36, 0x08, 0x3a, 0xc8, 0x23, 0x09, 0x08}},
    {"Linux home",                  {0xe1, 0xc7, 0x3a, 0x93, 0xb4, 0x2e, 0x13, 0x4f, 0xb8, 0x44, 0x0e, 0x14, 0xe2, 0xae, 0xf9, 0x15}},
    {"Linux RAID",                  {0x0f, 0x88, 0x9d, 0xa1, 0xfc, 0x05, 0x3b, 0x4d, 0xa0, 0x06, 0x74, 0x3f, 0x0f, 0x84, 0x91, 0x1e}},
    {"Linux extended boot",         {0xff, 0xc2, 0x13, 0xbc, 0xe6, 0x59, 0x62, 0x42, 0xa3, 0x52, 0xb2, 0x75, 0xfd, 0x6f, 0x71, 0x72}},
    {"Linux LVM",                   {0x79, 0xd3, 0xd6, 0xe6, 0x07, 0xf5, 0xc2, 0x44, 0xa2, 0x3c, 0x23, 0x8f, 0x2a, 0x3d, 0xf9, 0x28}},
    /* BSD data */
    {"FreeBSD data",                {0xB4, 0x7C, 0x6E, 0x51, 0xCF, 0x6E, 0xD6, 0x11, 0xF8, 0x8F, 0x2B, 0x71, 0x09, 0x2D, 0x02, 0x00}}
};

/*GPT header*/
typedef union {
	struct  {
		__u8 magic[8];
		__u32 version;
		__u32 headersize;
		__u32 crc32;
		__u32 unused1;
		__u64 primary;
		__u64 backup;
		__u64 start;
		__u64 end;
		__u8 guid[16];
		__u64 partitions;
		__u32 maxpart;
		__u32 partentry_size;
		__u32 partentry_crc32;
	};
	__u8 gpt[512];
} gpt_header;

/*GPT patition table*/
typedef union {
	struct {
		char type[16];
		char guid[16];
		__u64 start;
		__u64 end;
		__u64 attrib;
		char name[72];
	};
	__u8 ent[128];
} gpt_partentry;

#endif /* __DISKFS_H__ */
