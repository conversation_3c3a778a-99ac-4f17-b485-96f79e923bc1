/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Extensible Firmware Interface
 * Based on 'Extensible Firmware Interface Specification' version 0.9,
 * April 30, 1999
 *
 * Copyright (C) 1999 VA Linux Systems
 * Copyright (C) 1999 <PERSON> <<EMAIL>>
 * Copyright (C) 1999, 2002-2003 Hewlett-Packard Co.
 *	<PERSON>-<PERSON> <<EMAIL>>
 *	<PERSON><PERSON> <<EMAIL>>
 *
 * Copyright (C) 2020 Loongson Technology Corporation Limited
 *
 * From include/linux/efi.h in kernel 4.1 with some additions/subtractions
 */

#ifndef _EFI_H
#define _EFI_H

#include <stdio.h>
#include <stdarg.h>
#include <linux/libata.h>
#include <linux/types.h>
#include <sys/types.h>

/* Type INTN in UEFI specification */
#define efi_intn_t ssize_t
/* Type UINTN in UEFI specification*/
#define efi_uintn_t size_t

#define EFIAPI asmlinkage
#define efi_va_list va_list
#define efi_va_start va_start
#define efi_va_arg va_arg
#define efi_va_end va_end

#define EFI32_LOADER_SIGNATURE	"EL32"
#define EFI64_LOADER_SIGNATURE	"EL64"
#define true		1
#define false		0

#define MAX_ADDRESS   0xFFFFFFFFFFFFFFFFULL

struct efi_device_path;

typedef struct {
	u8 b[16];
} efi_guid_t __attribute__((aligned(8)));

#define EFI_BITS_PER_LONG   (sizeof(long) * 8)

/* Bit mask for EFI status code with error */
#define EFI_ERROR_MASK (1UL << (EFI_BITS_PER_LONG - 1))
/* Status codes returned by EFI protocols */
#define EFI_SUCCESS			0
#define EFI_LOAD_ERROR			(EFI_ERROR_MASK | 1)
#define EFI_INVALID_PARAMETER		(EFI_ERROR_MASK | 2)
#define EFI_UNSUPPORTED			(EFI_ERROR_MASK | 3)
#define EFI_BAD_BUFFER_SIZE		(EFI_ERROR_MASK | 4)
#define EFI_BUFFER_TOO_SMALL		(EFI_ERROR_MASK | 5)
#define EFI_NOT_READY			(EFI_ERROR_MASK | 6)
#define EFI_DEVICE_ERROR		(EFI_ERROR_MASK | 7)
#define EFI_WRITE_PROTECTED		(EFI_ERROR_MASK | 8)
#define EFI_OUT_OF_RESOURCES		(EFI_ERROR_MASK | 9)
#define EFI_VOLUME_CORRUPTED		(EFI_ERROR_MASK | 10)
#define EFI_VOLUME_FULL			(EFI_ERROR_MASK | 11)
#define EFI_NO_MEDIA			(EFI_ERROR_MASK | 12)
#define EFI_MEDIA_CHANGED		(EFI_ERROR_MASK | 13)
#define EFI_NOT_FOUND			(EFI_ERROR_MASK | 14)
#define EFI_ACCESS_DENIED		(EFI_ERROR_MASK | 15)
#define EFI_NO_RESPONSE			(EFI_ERROR_MASK | 16)
#define EFI_NO_MAPPING			(EFI_ERROR_MASK | 17)
#define EFI_TIMEOUT			(EFI_ERROR_MASK | 18)
#define EFI_NOT_STARTED			(EFI_ERROR_MASK | 19)
#define EFI_ALREADY_STARTED		(EFI_ERROR_MASK | 20)
#define EFI_ABORTED			(EFI_ERROR_MASK | 21)
#define EFI_ICMP_ERROR			(EFI_ERROR_MASK | 22)
#define EFI_TFTP_ERROR			(EFI_ERROR_MASK | 23)
#define EFI_PROTOCOL_ERROR		(EFI_ERROR_MASK | 24)
#define EFI_INCOMPATIBLE_VERSION	(EFI_ERROR_MASK | 25)
#define EFI_SECURITY_VIOLATION		(EFI_ERROR_MASK | 26)
#define EFI_CRC_ERROR			(EFI_ERROR_MASK | 27)
#define EFI_END_OF_MEDIA		(EFI_ERROR_MASK | 28)
#define EFI_END_OF_FILE			(EFI_ERROR_MASK | 31)
#define EFI_INVALID_LANGUAGE		(EFI_ERROR_MASK | 32)
#define EFI_COMPROMISED_DATA		(EFI_ERROR_MASK | 33)
#define EFI_IP_ADDRESS_CONFLICT		(EFI_ERROR_MASK | 34)
#define EFI_HTTP_ERROR			(EFI_ERROR_MASK | 35)

#define EFI_WARN_UNKNOWN_GLYPH		1
#define EFI_WARN_DELETE_FAILURE		2
#define EFI_WARN_WRITE_FAILURE		3
#define EFI_WARN_BUFFER_TOO_SMALL	4
#define EFI_WARN_STALE_DATA		5
#define EFI_WARN_FILE_SYSTEM		6
#define EFI_WARN_RESET_REQUIRED		7

typedef unsigned long efi_status_t;
typedef u64 efi_physical_addr_t;
typedef u64 efi_virtual_addr_t;
typedef struct efi_object *efi_handle_t;

#define EFI_GUID(a, b, c, d0, d1, d2, d3, d4, d5, d6, d7) \
	{{ (a) & 0xff, ((a) >> 8) & 0xff, ((a) >> 16) & 0xff, \
		((a) >> 24) & 0xff, \
		(b) & 0xff, ((b) >> 8) & 0xff, \
		(c) & 0xff, ((c) >> 8) & 0xff, \
		(d0), (d1), (d2), (d3), (d4), (d5), (d6), (d7) } }

/* Generic EFI table header */
struct efi_table_hdr {
	u64 signature;
	u32 revision;
	u32 headersize;
	u32 crc32;
	u32 reserved;
};

/* Allocation types for calls to boottime->allocate_pages*/
/**
 * enum efi_allocate_type - address restriction for memory allocation
 */
enum efi_allocate_type {
	/**
	 * @EFI_ALLOCATE_ANY_PAGES:
	 * Allocate any block of sufficient size. Ignore memory address.
	 */
	EFI_ALLOCATE_ANY_PAGES,
	/**
	 * @EFI_ALLOCATE_MAX_ADDRESS:
	 * Allocate a memory block with an uppermost address less or equal
	 * to the indicated address.
	 */
	EFI_ALLOCATE_MAX_ADDRESS,
	/**
	 * @EFI_ALLOCATE_ADDRESS:
	 * Allocate a memory block starting at the indicatged adress.
	 */
	EFI_ALLOCATE_ADDRESS,
	/**
	 * @EFI_MAX_ALLOCATE_TYPE:
	 * Value use for range checking.
	 */
	EFI_MAX_ALLOCATE_TYPE,
};

/* Enumeration of memory types introduced in UEFI */
enum efi_memory_type {
	EFI_RESERVED_MEMORY_TYPE,
	/*
	 * The code portions of a loaded application.
	 * (Note that UEFI OS loaders are UEFI applications.)
	 */
	EFI_LOADER_CODE,
	/*
	 * The data portions of a loaded application and
	 * the default data allocation type used by an application
	 * to allocate pool memory.
	 */
	EFI_LOADER_DATA,
	/* The code portions of a loaded Boot Services Driver */
	EFI_BOOT_SERVICES_CODE,
	/*
	 * The data portions of a loaded Boot Services Driver and
	 * the default data allocation type used by a Boot Services
	 * Driver to allocate pool memory.
	 */
	EFI_BOOT_SERVICES_DATA,
	/* The code portions of a loaded Runtime Services Driver */
	EFI_RUNTIME_SERVICES_CODE,
	/*
	 * The data portions of a loaded Runtime Services Driver and
	 * the default data allocation type used by a Runtime Services
	 * Driver to allocate pool memory.
	 */
	EFI_RUNTIME_SERVICES_DATA,
	/* Free (unallocated) memory */
	EFI_CONVENTIONAL_MEMORY,
	/* Memory in which errors have been detected */
	EFI_UNUSABLE_MEMORY,
	/* Memory that holds the ACPI tables */
	EFI_ACPI_RECLAIM_MEMORY,
	/* Address space reserved for use by the firmware */
	EFI_ACPI_MEMORY_NVS,
	/*
	 * Used by system firmware to request that a memory-mapped IO region
	 * be mapped by the OS to a virtual address so it can be accessed by
	 * EFI runtime services.
	 */
	EFI_MMAP_IO,
	/*
	 * System memory-mapped IO region that is used to translate
	 * memory cycles to IO cycles by the processor.
	 */
	EFI_MMAP_IO_PORT,
	/*
	 * Address space reserved by the firmware for code that is
	 * part of the processor.
	 */
	EFI_PAL_CODE,
	/*
	 * Non-volatile memory.
	 */
	EFI_PERSISTENT_MEMORY_TYPE,
	/*
	 * Unaccepted memory must be accepted by boot target before usage.
	 */
	EFI_UNACCEPTED_MEMORY_TYPE,

	EFI_MAX_MEMORY_TYPE,
};

/* Attribute values */
#define EFI_MEMORY_UC		((u64)0x0000000000000001ULL)	/* uncached */
#define EFI_MEMORY_WC		((u64)0x0000000000000002ULL)	/* write-coalescing */
#define EFI_MEMORY_WT		((u64)0x0000000000000004ULL)	/* write-through */
#define EFI_MEMORY_WB		((u64)0x0000000000000008ULL)	/* write-back */
#define EFI_MEMORY_UCE		((u64)0x0000000000000010ULL)	/* uncached, exported */
#define EFI_MEMORY_WP		((u64)0x0000000000001000ULL)	/* write-protect */
#define EFI_MEMORY_RP		((u64)0x0000000000002000ULL)	/* read-protect */
#define EFI_MEMORY_XP		((u64)0x0000000000004000ULL)	/* execute-protect */
#define EFI_MEMORY_NV		((u64)0x0000000000008000ULL)	/* non-volatile */
#define EFI_MEMORY_MORE_RELIABLE \
				((u64)0x0000000000010000ULL)	/* higher reliability */
#define EFI_MEMORY_RO		((u64)0x0000000000020000ULL)	/* read-only */
#define EFI_MEMORY_SP		((u64)0x0000000000040000ULL)	/* specific-purpose memory (SPM) */
#define EFI_MEMORY_CPU_CRYPTO	((u64)0x0000000000080000ULL)	/* cryptographically protectable */
#define EFI_MEMORY_RUNTIME	((u64)0x8000000000000000ULL)	/* range requires runtime mapping */
#define EFI_MEM_DESC_VERSION	1

#define EFI_PAGE_SHIFT		12
#define EFI_PAGE_SIZE		(1ULL << EFI_PAGE_SHIFT)
#define EFI_PAGE_MASK		(EFI_PAGE_SIZE - 1)

struct efi_mem_desc {
	u32 type;
	u32 reserved;
	efi_physical_addr_t physical_start;
	efi_virtual_addr_t virtual_start;
	u64 num_pages;
	u64 attribute;
};

#define EFI_MEMORY_DESCRIPTOR_VERSION 1

/* Allocation types for calls to boottime->allocate_pages*/
#define EFI_ALLOCATE_ANY_PAGES		0
#define EFI_ALLOCATE_MAX_ADDRESS	1
#define EFI_ALLOCATE_ADDRESS		2
#define EFI_MAX_ALLOCATE_TYPE		3

/* Types and defines for Time Services */
#define EFI_TIME_ADJUST_DAYLIGHT 0x1
#define EFI_TIME_IN_DAYLIGHT     0x2
#define EFI_UNSPECIFIED_TIMEZONE 0x07ff

struct efi_time {
	u16 year;
	u8 month;
	u8 day;
	u8 hour;
	u8 minute;
	u8 second;
	u8 pad1;
	u32 nanosecond;
	s16 timezone;
	u8 daylight;
	u8 pad2;
};

struct rtc_time {
	int tm_sec;
	int tm_min;
	int tm_hour;
	int tm_mday;
	int tm_mon;
	int tm_year;
	int tm_wday;
	int tm_yday;
	int tm_isdst;
};

struct efi_time_cap {
	u32 resolution;
	u32 accuracy;
	u8 sets_to_zero;
};

typedef union {
	struct {
		unsigned int toy_millisec  :4;
		unsigned int toy_sec       :6;
		unsigned int toy_min       :6;
		unsigned int toy_hour      :5;
		unsigned int toy_day       :5;
		unsigned int toy_month     :6;
	} bits;
	unsigned int data;
} ls7a_toy_reg0; //read and write similar

typedef union {
	struct {
		unsigned int toy_sec       :6;
		unsigned int toy_min       :6;
		unsigned int toy_hour      :5;
		unsigned int toy_day       :5;
		unsigned int toy_month     :4;
		unsigned int toy_year      :6;
	} bits;
	unsigned int data;
} ls7a_toy_match0;

enum efi_locate_search_type {
	ALL_HANDLES,
	BY_REGISTER_NOTIFY,
	BY_PROTOCOL
};

struct efi_open_protocol_info_entry {
	efi_handle_t agent_handle;
	efi_handle_t controller_handle;
	u32 attributes;
	u32 open_count;
};

typedef struct {
	unsigned int type;
	unsigned int pad;
	unsigned long long phys_addr;
	unsigned long long virt_addr;
	unsigned long long num_pages;
	unsigned long long attribute;
} efi_memory_desc_t;

struct efi_boot_memmap {
	unsigned long		map_size;
	unsigned long		desc_size;
	unsigned int		desc_ver;
	unsigned long		key_ptr;
	unsigned long		buff_size;
	efi_memory_desc_t	map[];
};

#define EFI_TABLE_VERSION	1

/**
 * struct efi_info_hdr - Header for the EFI info table
 *
 * @version:	EFI_TABLE_VERSION
 * @hdr_size:	Size of this struct in bytes
 * @total_size:	Total size of this header plus following data
 * @spare:	Spare space for expansion
 */
struct efi_info_hdr {
	u32 version;
	u32 hdr_size;
	u32 total_size;
	u32 spare[5];
};

/*
 * Variable Attributes
 */
#define EFI_VARIABLE_NON_VOLATILE       0x0000000000000001
#define EFI_VARIABLE_BOOTSERVICE_ACCESS 0x0000000000000002
#define EFI_VARIABLE_RUNTIME_ACCESS     0x0000000000000004
#define EFI_VARIABLE_HARDWARE_ERROR_RECORD 0x0000000000000008
#define EFI_VARIABLE_AUTHENTICATED_WRITE_ACCESS 0x0000000000000010
#define EFI_VARIABLE_TIME_BASED_AUTHENTICATED_WRITE_ACCESS 0x0000000000000020
#define EFI_VARIABLE_APPEND_WRITE	0x0000000000000040

#define EFI_VARIABLE_MASK	(EFI_VARIABLE_NON_VOLATILE | \
				EFI_VARIABLE_BOOTSERVICE_ACCESS | \
				EFI_VARIABLE_RUNTIME_ACCESS | \
				EFI_VARIABLE_HARDWARE_ERROR_RECORD | \
				EFI_VARIABLE_AUTHENTICATED_WRITE_ACCESS | \
				EFI_VARIABLE_TIME_BASED_AUTHENTICATED_WRITE_ACCESS | \
				EFI_VARIABLE_APPEND_WRITE)

/**
 * efi_get_mmap() - Get the memory map from EFI
 *
 * This is used in the app. The caller must free *@descp when done
 *
 * @descp:	Returns allocated pointer to EFI memory map table
 * @sizep:	Returns size of table in bytes
 * @keyp:	Returns memory-map key
 * @desc_sizep:	Returns size of each @desc_base record
 * @versionp:	Returns version number of memory map
 * Returns: 0 on success, -ve on error
 */
int efi_get_mmap(struct efi_mem_desc **descp, int *sizep, uint *keyp,
		 int *desc_sizep, uint *versionp);

#endif /* _LINUX_EFI_H */
