/*	$Id: platform.h, v 1.1.1.1 2024/09/20 11:59:06 root Exp $ */

/*
 * Copyright (c) 2001 Opsycon AB  (www.opsycon.se)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */
/*
 * PMON Written for LSI LOGIC Corp. by Phil Bunce. Released to public
 * domain by LSI LOGIC.
 *              Phil Bunce, 100 Dolores St. Ste 242, Carmel CA 93923
 *
 * PMON Ported/rewritten for PowerPC and MIPS by Opsycon AB. New version
 * released under the BSD copyright above.
 */

#ifndef __PLATFORM_H__
#define __PLATFORM_H__

#ifndef TOT_NODE_NUM
#define TOT_NODE_NUM            0
#endif
#ifndef TOT_7A_NUM
#define TOT_7A_NUM              0
#endif

#define LS7A_PERF_ENH           0

#if defined(LOONGSON_3A6000) || defined(LOONGSON_3C6000)
#define THREAD_PER_CORE         2
#else
#define THREAD_PER_CORE         1
#endif
#define NODES_PER_PACKAGE       1

#ifdef LOONGSON_3C6000
#define IOMMU_NUM_PER_BRIDGE    2
#if !defined(LOONGSON_3D6000) && (TOT_NODE_NUM == 4)
#define TOT_BRIDGE_NUM          TOT_NODE_NUM / 2
#else
#define TOT_BRIDGE_NUM          TOT_NODE_NUM
#endif
#else
#define IOMMU_NUM_PER_BRIDGE    1
#define TOT_BRIDGE_NUM          TOT_7A_NUM
#endif
#define MAX_IOMMU_NUM           (TOT_BRIDGE_NUM * IOMMU_NUM_PER_BRIDGE)

#ifndef VGA_BASE
#ifdef LOONGSON_3C6000
#define VGA_BASE                0xe0000000000ULL
#else
#define VGA_BASE                0x1e000000
#endif
#endif //VGA_BASE

#ifndef LS_STR
#if (TOT_NODE_NUM <= 1)
#define LS_STR
#endif
#endif //LS_STR

#ifndef INTERLEAVE_OFFSET
#ifdef LOONGSON_3D5000
#define INTERLEAVE_OFFSET       12
#elif defined(LOONGSON_3A6000) || defined(LOONGSON_3C6000)
#define INTERLEAVE_OFFSET       13
#else
#define INTERLEAVE_OFFSET       8
#endif
#endif //INTERLEAVE_OFFSET

#ifndef DTB
#ifndef SMBIOS_SUPPORT
#define SMBIOS_SUPPORT
#endif
#ifndef ACPI_SUPPORT
#define ACPI_SUPPORT
#endif
#endif

#ifndef DDR_FREQ
#ifdef LOONGSON_3D6000
#define DDR_FREQ                600
#elif defined(LOONGSON_2K2000)
#define DDR_FREQ                1200
#elif defined(LOONGSON_2K1500)
#define DDR_FREQ                600
#elif defined(LOONGSON3A5000)
#define DDR_FREQ                800
#elif defined(LOONGSON_2K1000)
#define DDR_FREQ                400
#elif defined(LOONGARCH_2K500)
#define DDR_FREQ                400
#endif
#endif
#ifndef DDR_FREQ_2SLOT
#define DDR_FREQ_2SLOT          600
#endif

#ifndef CORE_FREQ
#if defined(LOONGSON_3D6000) || defined(LOONGSON_3D5000)
#define CORE_FREQ                2000
#elif defined(LOONGSON_3B6000)
#define CORE_FREQ                2300
#elif defined(LOONGSON_3C6000) || defined(LOONGSON_3C5000)
#define CORE_FREQ                2200
#elif (TOT_NODE_NUM >= 4)
#define CORE_FREQ                2000
#elif defined(LOONGSON3A5000)
#define CORE_FREQ                2500
#elif defined(LOONGSON_2K2000)
#define CORE_FREQ                1400
#elif defined(LOONGSON_2K1500)
#define CORE_FREQ                800
#elif defined(LOONGSON_2K1000)
#define CORE_FREQ                1000
#elif defined(LOONGARCH_2K500)
#define CORE_FREQ                500
#endif
#endif //CORE_FREQ

#ifndef CORES_PER_NODE
#if defined(LOONGSON_3C6000) && defined(FLAT_MODE)
#define CORES_PER_NODE           32
#elif defined(LOONGSON_3C5000) && defined(FLAT_MODE)
#define CORES_PER_NODE           16
#elif defined(LOONGSON_3A6000) && defined(FLAT_MODE)
#define CORES_PER_NODE           8
#elif defined(LOONGSON3A5000)
#define CORES_PER_NODE           4
#elif defined(LOONGSON_LS2K)
#define CORES_PER_NODE           2
#endif
#endif //CORES_PER_NODE

#ifndef MC_PER_NODE
#if defined(LOONGSON_3C6000) || defined(LOONGSON_3C5000)
#define MC_PER_NODE           4
#elif (TOT_NODE_NUM >= 4)
#define MC_PER_NODE           1
#elif defined(LOONGSON3A5000)
#define MC_PER_NODE           2
#elif defined(LOONGSON_LS2K)
#define MC_PER_NODE           1
#endif
#endif //MC_PER_NODE

#ifndef MCC
#if (TOT_NODE_NUM > 1)
#define MCC
#endif
#endif

#define NEW_IRQ                 0

//#define LS132_CORE

//#define SUPER_IO_ENABLE

#ifndef BONITOEL
#define BONITOEL
#endif

//#define LS_SE_ENABLE
 
#endif //__PLATFORM_H__
